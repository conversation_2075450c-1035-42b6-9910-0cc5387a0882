
v1.5.3
------

Fix bug with listener calling .removeAllListener() and another being a one-time listener (.once())


v1.5.2
------

LeanEvents: slightly faster (and more benchmarks)


v1.5.1
------

LeanEvents: slight improvements


v1.5.0
------

New: LeanEvents class, with only the most basic (observer) features + state events
faster than full blown NextGenEvents, no high level reserved event (error/newListener/removeListener/etc)


v1.4.0
------

New: .addListener()/.on() now support a 'unique' option, only adding a listener if its ID is unique for this event


v1.3.4
------

Improved code/performance/memory usage of .removeListener()


v1.3.3
------

.emitIfListener(), fixed


v1.3.2
------

.emitIfListener()


v1.3.1
------

[optimization] .listenerCount() now longer causes init or create empty event listener array, but simply return 0


v1.3.0
------

New: NextGenEvents.emitIntricatedEvents() (for Spellcast specific use-case, not sure if it could be useful elsewhere)
nasty context serialization bug fixed when listeners are not sync


v1.2.1
------

Minor internal improvements


v1.2.0
------

New: listener priority


v1.1.1
------

Unit test improved, coding style enforced, etc...


v1.1.0
------

Change of static method names .groupGlobalOnce() -> .groupOnceFirst() and .groupGlobalOnceAll() -> .groupOnceLast(),
former names are deprecated (reason: they are ambiguous and unclear) and will be removed on the next semver major.
New: .groupWaitFor*() static methods


v1.0.1
------

Fixed README.md: advertise the lib as stable


v1.0.0
------

v1!


v0.15.0
-------

.removeAllListeners() bug fixed
finally added supports for setMaxListeners() and .getMaxListeners()


v0.14.6
-------

Unit test fixed


v0.14.5
-------

Dependencies, moved to Tea-Time's builtin 'expect'


v0.14.4
-------

Documentation: npm sucks... big README.md are not supported


v0.14.3
-------

More documentation fixes


v0.14.2
-------

Documentation improved (again)


v0.14.1
-------

Documentation improved


v0.14.0
-------

New: .waitFor() and .waitForAll()


v0.13.1
-------

Fixing Internal constructor using 'from' when there are contexts


v0.13.0
-------

Context serialization refacto (deadlock prevention)


v0.12.4
-------

New constant


v0.12.3
-------

a console.log() was not cleaned up


v0.12.2
-------

Spellcast specific patch


v0.12.1
-------

Spellcast specific patch


v0.12.0
-------

Now the __ngev property is an instance of NextGenEvents.Internal


v0.11.3
-------

Documentation


v0.11.2
-------

Desynced emit fixed


v0.11.1
-------

Desync emit callback when the emitter is not SYNC


v0.11.0
-------

Breaking change: ES6 standard update, so node v6 is required


v0.10.2
-------

Minor fixes (build, etc)


v0.10.1
-------

Faster emitter creation when using the 'new' operator


v0.9.9
------

Dependencies


v0.9.8
------

Test and browser tests


v0.9.7
------

Fixing tests to ensure async-try-catch module compatibilities


v0.9.6
------

Bugfix: groupEmit() now supports the nice argument


v0.9.5
------

async-try-catch compatibility improvements


v0.9.4
------

New: NextGenEvents.groupRemoveAllListeners()


v0.9.3
------

New: NextGenEvents.reset(), to reset an emitter


v0.9.2
------

Bugfixed: missing NextGenEvents init in .hasState() and .getAllStates()


v0.9.1
------

Bugfix: a state-event should not be emitted if the state and its event's arguments hasn't changed.


v0.9.0
------

Breaking change: new methods on the prototype: .hasState() and .getAllStates() ; new static method: NextGenEvents.groupDefineStates()


v0.8.0
------

Breaking change: .emitOnce() (0.7.x) is removed in favor of a more generic 'state-events' approach, featuring group exclusivity:
.defineStates().
Normal .emit() can be used, so better backward-compatibility with 0.6.x and below.


v0.7.0
------

New: emit once, for 'ready'-like event


v0.6.5
------

Bugfix: make __ngev property configurable


v0.6.4
------

New: NextGenEvents.groupGlobalOnceAll()


v0.6.3
------

New: NextGenEvents.share(), share the event bus on differents objects


v0.6.2
------

Bugfixed: completion callback should be called only once for groupEmit()


v0.6.1
------

New features: group emitters ; compatible change: listener ID can be anything now


v0.6.0
------

Proxy: message now use __type instead of type, to avoid collision


v0.5.23
-------

Bugfixed: inside a 'newListener' listener, the .listenerCount() should report correctly


v0.5.22
-------

Fix the doc again (formating on npmjs.org's narrow column)


v0.5.21
-------

Fix the doc


v0.5.20
-------

Added a sparse documentation on the Proxy Services features


v0.5.19
-------

Proxy services features: ack/callback on emit and listen!


v0.5.18
-------

New feature: listener in 'eventObject' mode.


v0.5.17
-------

Proxy services: basic features ok!


v0.5.16
-------

.once() arguments management bugfixed (rare).


v0.5.14 - v0.5.15
-----------------

Async-try-catch module compatibility.


v0.5.13
-------

Browser tests.


v0.5.12
-------

The __ngev property is not enumerable anymore.


v0.5.11
-------

Browser build was missing...


v0.5.10
-------

Fixed recursive emit issue when a listener modify the current listener array in the emit loop.


v0.5.9
------

Documentation.


v0.5.8
------

Browser build.


v0.5.7
------

Browser support: ok!


v0.5.6
------

Exception message clean up.


v0.5.5
------

Bugfix completion handler when there is no listener for an event.


v0.5.4
------

Internal event object changes.


v0.5.3
------

.emit()'s completion callback now has the event object passed as argument #1.


v0.5.2
------

New method .listenerCount() to be up to date with Node.js built-in events.


v0.5.1
------

Documentation: spelling.


v0.5.0
------

Breaking change: .emit() now accepts a final (optional) completion callback argument.


v0.4.1
------

Documentation about compatibilities with Node.js built-in events.


v0.4.0
------

.emit() does not return the emitter anymore, but instead an object representing the event.

The method .setInterruptible() allow the emitter to be interruptible, i.e. when an event is emitted, a listener returning
a truthy value will abort the emitting process, other emitters will not receive the event.
Furthermore, an 'interrupt' event will be fired.


