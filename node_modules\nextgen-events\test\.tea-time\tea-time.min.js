(function(e){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=e()}else if(typeof define==="function"&&define.amd){define([],e)}else{var t;if(typeof window!=="undefined"){t=window}else if(typeof global!=="undefined"){t=global}else if(typeof self!=="undefined"){t=self}else{t=this}t.createTeaTime=e()}})(function(){var e,t,r;return function e(t,r,n){function i(o,a){if(!r[o]){if(!t[o]){var l=typeof require=="function"&&require;if(!a&&l)return l(o,!0);if(s)return s(o,!0);var u=new Error("Cannot find module '"+o+"'");throw u.code="MODULE_NOT_FOUND",u}var c=r[o]={exports:{}};t[o][0].call(c.exports,function(e){var r=t[o][1][e];return i(r?r:e)},c,c.exports,e,t,r,n)}return r[o].exports}var s=typeof require=="function"&&require;for(var o=0;o<n.length;o++)i(n[o]);return i}({1:[function(e,t,r){"use strict";function n(e,t){if(!t){t=Object.create(n.prototype,{teaTime:{value:e,enumerable:true}})}document.querySelector("body").insertAdjacentHTML("beforeend",'<div class="tea-time-classic-reporter" style="background-color:black;color:white"></div>');t.container=document.querySelector("div.tea-time-classic-reporter");t.teaTime.on("enterSuite",n.enterSuite.bind(t));t.teaTime.on("ok",n.ok.bind(t));t.teaTime.on("fail",n.fail.bind(t));t.teaTime.on("optionalFail",n.optionalFail.bind(t));t.teaTime.on("skip",n.skip.bind(t));t.teaTime.on("report",n.report.bind(t));t.teaTime.on("errorReport",n.errorReport.bind(t));return t}t.exports=n;function i(){(document.querySelector("div.tea-time-classic-reporter p:last-child")||document.querySelector("div.tea-time-classic-reporter h4:last-child")||document.querySelector("div.tea-time-classic-reporter pre:last-child")).scrollIntoView()}function s(e){return"margin-left:"+(1+2*e)+"%;"}var o="color:grey;";var a="color:green;";var l="color:red;";var u="color:brown;";var c="color:blue;";var f="color:grey;";var p="color:yellow;";var h="color:red;";var d="color:brown;font-weight:bold;";var v="color:red;font-weight:bold;";var y="background-color:red;color:white;font-weight:bold;";var m="background-color:green;color:white;font-weight:bold;";var g="background-color:red;color:white;font-weight:bold;";n.enterSuite=function e(t,r){this.container.insertAdjacentHTML("beforeend",'<h4 class="tea-time-classic-reporter" style="'+s(r)+'">'+t+"</h4>");i()};n.ok=function e(t,r,n,o){var l="✔ "+t;if(!o){l+=' <span style="'+f+'">('+n+"ms)</span>"}else if(o===1){l+=' <span style="'+p+'">('+n+"ms)</span>"}else{l+=' <span style="'+h+'">('+n+"ms)</span>"}this.container.insertAdjacentHTML("beforeend",'<p class="tea-time-classic-reporter" style="'+a+s(r)+'">'+l+"</p>");i()};n.fail=function e(t,r,n,o,a){var u="✘ "+t;if(n!==undefined){if(!o){u+=' <span style="'+f+'">('+n+"ms)</span>"}else if(o===1){u+=' <span style="'+p+'">('+n+"ms)</span>"}else{u+=' <span style="'+h+'">('+n+"ms)</span>"}}this.container.insertAdjacentHTML("beforeend",'<p class="tea-time-classic-reporter" style="'+l+s(r)+'">'+u+"</p>");i()};n.optionalFail=function e(t,r,n,o,a){var l="✘ "+t;if(n!==undefined){if(!o){l+=' <span style="'+f+'">('+n+"ms)</span>"}else if(o===1){l+=' <span style="'+p+'">('+n+"ms)</span>"}else{l+=' <span style="'+h+'">('+n+"ms)</span>"}}this.container.insertAdjacentHTML("beforeend",'<p class="tea-time-classic-reporter" style="'+u+s(r)+'">'+l+"</p>");i()};n.skip=function e(t,r){var n="· "+t;this.container.insertAdjacentHTML("beforeend",'<p class="tea-time-classic-reporter" style="'+c+s(r)+'">'+n+"</p>");i()};n.report=function e(t,r,n,f,p){this.container.insertAdjacentHTML("beforeend","<hr />"+'<p class="tea-time-classic-reporter" style="font-weight:bold;'+a+s(1)+'">'+t+" passing "+(p<2e3?'<span style="'+o+'">('+Math.round(p)+"ms)</span>":'<span style="'+o+'">('+Math.round(p/1e3)+"s)</span>")+"</p>"+'<p class="tea-time-classic-reporter" style="font-weight:bold;'+l+s(1)+'">'+r+" failing</p>"+(n?'<p class="tea-time-classic-reporter" style="font-weight:bold;'+u+s(1)+'">'+n+" opt failing</p>":"")+(f?'<p class="tea-time-classic-reporter" style="font-weight:bold;'+c+s(1)+'">'+f+" pending</p>":""));i()};n.errorReport=function e(t){var r,n,o="";o+='<h4 class="tea-time-classic-reporter" style="'+v+s(0)+'">== Errors ==</h4>';for(r=0;r<t.length;r++){n=t[r];o+='<p class="tea-time-classic-reporter" style="'+(n.optional?d:v)+s(1)+'">'+(r+1)+" ) ";switch(n.type){case"test":if(n.error.testTimeout){o+='<span style="'+y+'">TEST TIMEOUT</span> '}break;case"setup":o+='<span style="'+y+'">SETUP HOOK</span> ';break;case"teardown":o+='<span style="'+y+'">TEARDOWN HOOK</span> ';break;case"suiteSetup":o+='<span style="'+y+'">SUITE SETUP HOOK</span> ';break;case"suiteTeardown":o+='<span style="'+y+'">SUITE TEARDOWN HOOK</span> ';break}if(n.error.uncaught){o+='<span style="'+y+'">UNCAUGHT EXCEPTION</span> '}o+=n.name;o+="</p>";o+=this.reportOneError(n.error)}this.container.insertAdjacentHTML("beforeend","<hr />"+o);i()};n.prototype.reportOneError=function e(t){var r="";if("expected"in t&&"actual"in t){r+='<p class="tea-time-classic-reporter" style="'+s(2)+'">'+'<span style="'+m+'">expected</span><span style="'+g+'">actual</span>'+"</p>";r+='<pre class="tea-time-classic-reporter"; style="'+s(2)+'">';r+=this.teaTime.htmlColorDiff(t.actual,t.expected);r+="</pre>"}r+='<pre class="tea-time-classic-reporter" style="'+s(2)+'">'+this.teaTime.inspect.inspectError({style:"html"},t)+"</pre>";return r}},{}],2:[function(e,t,r){"use strict";function n(e,t){if(!t){t=Object.create(n.prototype,{teaTime:{value:e,enumerable:true}})}t.teaTime.on("ok",n.ok.bind(t));t.teaTime.on("fail",n.fail.bind(t));t.teaTime.on("optionalFail",n.optionalFail.bind(t));t.teaTime.on("skip",n.skip.bind(t));t.teaTime.on("report",n.report.bind(t));return t}t.exports=n;n.ok=function e(t,r,n,i){console.log("OK:",t,"("+n+")")};n.fail=function e(t,r,n,i,s){console.log("Fail:",t,n!==undefined?"("+n+")":"")};n.optionalFail=function e(t,r,n,i,s){console.log("Opt fail:",t,n!==undefined?"("+n+")":"")};n.skip=function e(t,r){console.log("Pending:",t)};n.report=function e(t,r,n,i){console.log("Report -- ok:",t," fail:",r," opt fail:",n," pending:",i)}},{}],3:[function(e,t,r){"use strict";function n(e,t){if(!t){t=Object.create(n.prototype,{teaTime:{value:e,enumerable:true}})}t.teaTime.on("ready",{fn:n.ready.bind(t),async:true});t.teaTime.on("run",n.forward.bind(t,"run"));t.teaTime.on("enterSuite",n.forward.bind(t,"enterSuite"));t.teaTime.on("exitSuite",n.forward.bind(t,"exitSuite"));t.teaTime.on("enterTest",n.forward.bind(t,"enterTest"));t.teaTime.on("exitTest",n.forward.bind(t,"exitTest"));t.teaTime.on("ok",n.forward.bind(t,"ok"));t.teaTime.on("fail",n.forward.bind(t,"fail"));t.teaTime.on("optionalFail",n.forward.bind(t,"optionalFail"));t.teaTime.on("skip",n.forward.bind(t,"skip"));t.teaTime.on("report",n.forward.bind(t,"report"));t.teaTime.on("errorReport",n.forward.bind(t,"errorReport"));t.teaTime.on("exit",n.exit.bind(t,"exit"));return t}t.exports=n;n.ready=function e(t){var r=this;this.ws=new WebSocket("ws://127.0.0.1:7357/"+this.teaTime.token);this.ws.onopen=function e(){n.forward.call(r,"ready");console.log("Websocket opened!");t()};this.ws.onclose=function e(){console.log("Websocket closed!")}};n.forward=function e(t){var r=Array.prototype.slice.call(arguments,1);this.teaTime.prepareSerialize(r);this.ws.send(JSON.stringify({event:t,args:r}))};n.exit=function e(t){n.forward.call(this,"exit");this.ws.close()}},{}],4:[function(e,t,r){"use strict";var n=e("./tea-time.js");var i=e("./diff.js");var s=e("./htmlColorDiff.js");var o=e("string-kit/lib/inspect.js");var a=e("dom-kit");var l=e("url");function u(){var t={microTimeout:function(e){setTimeout(e,0)},onceUncaughtException:function(e){window.onerror=function(t,r,n,i,s){window.onerror=function(){};e(s);return true}},offUncaughtException:function(){window.onerror=function(){}},allowConsole:true};n.populateOptionsWithArgs(t,l.parse(window.location.href,true).query);window.teaTime=n.create(t);window.teaTime.init();window.teaTime.diff=i;window.teaTime.htmlColorDiff=s;window.teaTime.inspect=o;window.teaTime.prepareSerialize=c;window.teaTime.reporters={console:e("./browser-reporters/console.js"),classic:e("./browser-reporters/classic.js"),websocket:e("./browser-reporters/websocket.js")};t.reporters.forEach(function(e){window.teaTime.reporters[e](window.teaTime)});if(t.ws){window.teaTime.ws=true}return window.teaTime}t.exports=u;function c(e){var t,r,n,i;if(!e||typeof e!=="object"){return}if(Array.isArray(e)){for(t=0,r=e.length;t<r;t++){c(e[t])}return}i=e.__proto__&&e.__proto__.constructor.name;if(i&&i!=="Object"){e.__prototype=i}if(e instanceof Error){Object.defineProperties(e,{__prototype:{value:e.constructor.name,enumerable:true,writable:true},name:{value:e.name,enumerable:true,writable:true},message:{value:e.message,enumerable:true,writable:true},type:{value:e.type||e.constructor.name,enumerable:true,writable:true},stack:{value:e.stack,enumerable:true,writable:true}})}n=Object.keys(e);for(t=0,r=n.length;t<r;t++){c(e[n[t]])}}u();a.ready(function(){window.teaTime.run()})},{"./browser-reporters/classic.js":1,"./browser-reporters/console.js":2,"./browser-reporters/websocket.js":3,"./diff.js":5,"./htmlColorDiff.js":6,"./tea-time.js":7,"dom-kit":40,"string-kit/lib/inspect.js":47,url:23}],5:[function(e,t,r){"use strict";var n=e("string-kit/lib/inspect.js");var i=e("diff");var s={minimal:true,depth:10,sort:true};function o(e,t){var r="",n=o.raw(e,t,true);n.forEach(function(e){r+=e.value.replace(/^(?!$)/gm,function(){if(e.added){return"++"}else if(e.removed){return"--"}else{return"  "}})});return r}t.exports=o;o.raw=function e(t,r,o){var a,l=0;var u=n.inspect(s,t);var c=n.inspect(s,r);if(!o){a=i.diffChars(u,c);a.forEach(function(e){if(e.added||e.removed){l+=15+e.value.length}});if(l<80){return a}}a=i.diffLines(u,c);return a}},{diff:34,"string-kit/lib/inspect.js":47}],6:[function(e,t,r){"use strict";var n=e("./diff.js").raw;t.exports=function e(t,r){var i="",s=n(t,r);s.forEach(function(e){if(e.added){i+=e.value.replace(/^(\s*)(\S(?:[^\n]*\S)?)(\s*)$/gm,function(e,t,r,n){return t+'<span style="background-color:green;color:white">'+r+"</span>"+n})}else if(e.removed){i+=e.value.replace(/^(\s*)(\S(?:[^\n]*\S)?)(\s*)$/gm,function(e,t,r,n){return t+'<span style="background-color:red;color:white">'+r+"</span>"+n})}else{i+='<span style="color:grey">'+e.value+"</span>"}});return i}},{"./diff.js":5}],7:[function(e,t,r){(function(r){"use strict";var n=e("async-kit");var i=e("nextgen-events");var s=e("async-try-catch").try;function o(){throw new Error("Use TeaTime.create() instead")}o.prototype=Object.create(i.prototype);o.prototype.constructor=o;t.exports=o;o.create=function e(t){var r=Object.create(o.prototype,{timeout:{value:t.timeout||2e3,writable:true,enumerable:true},slowTime:{value:t.slowTime||75,writable:true,enumerable:true},suite:{value:o.createSuite(),enumerable:true},grep:{value:Array.isArray(t.grep)?t.grep:[],writable:true,enumerable:true},allowConsole:{value:!!t.allowConsole,writable:true,enumerable:true},bail:{value:!!t.bail,writable:true,enumerable:true},skipOptional:{value:!!t.skipOptional,writable:true,enumerable:true},token:{value:t.token||null,writable:true,enumerable:true},acceptTokens:{value:t.acceptTokens||null,writable:true,enumerable:true},startTime:{value:0,writable:true,enumerable:true},testCount:{value:0,writable:true,enumerable:true},done:{value:0,writable:true,enumerable:true},ok:{value:0,writable:true,enumerable:true},fail:{value:0,writable:true,enumerable:true},optionalFail:{value:0,writable:true,enumerable:true},skip:{value:0,writable:true,enumerable:true},errors:{value:[],enumerable:true},microTimeout:{value:t.microTimeout,enumerable:true},onceUncaughtException:{value:t.onceUncaughtException,enumerable:true},offUncaughtException:{value:t.offUncaughtException,enumerable:true}});Object.defineProperties(r,{registerStack:{value:[r.suite],writable:true,enumerable:true}});return r};o.populateOptionsWithArgs=function e(t,r){var n,i,s;if(!t.reporters){t.reporters=["classic"]}if(!t.clientReporters){t.clientReporters=["classic"]}if(r.console!==undefined){t.allowConsole=r.console}else if(r.c!==undefined){t.allowConsole=r.c}if(r.b||r.bail){t.bail=true}if(r["skip-optional"]||r.O){t.skipOptional=true}if(r.timeout&&(s=parseInt(r.timeout,10))){t.timeout=s}else if(r.t&&(s=parseInt(r.t,10))){t.timeout=s}if(r.slow&&(s=parseInt(r.slow,10))){t.slowTime=s}else if(r.s&&(s=parseInt(r.s,10))){t.slowTime=s}if(r.reporter){if(!Array.isArray(r.reporter)){r.reporter=[r.reporter]}t.reporters=r.reporter;if(r.R){if(!Array.isArray(r.R)){r.R=[r.R]}t.reporters=r.reporter.concat(r.R)}}else if(r.R){if(!Array.isArray(r.R)){r.R=[r.R]}t.reporters=r.R}if(r.clientReporter){if(!Array.isArray(r.clientReporter)){r.clientReporter=[r.clientReporter]}t.clientReporters=r.clientReporter}t.grep=[];t.sourceGrep=[];if(r.g){if(!Array.isArray(r.g)){r.g=[r.g]}for(n=0,i=r.g.length;n<i;n++){t.grep.push(new RegExp(r.g[n],"i"));t.sourceGrep.push(r.g[n])}}if(r.grep){if(!Array.isArray(r.grep)){r.grep=[r.grep]}for(n=0,i=r.grep.length;n<i;n++){t.grep.push(new RegExp(r.grep[n],"i"));t.sourceGrep.push(r.grep[n])}}if(r.token){t.token=r.token}};o.prototype.init=function e(t){r.suite=r.describe=r.context=o.registerSuite.bind(this);r.test=r.it=r.specify=o.registerTest.bind(this);r.test.skip=o.registerSkipTest.bind(this);r.test.optional=o.registerOptionalTest.bind(this);r.test.opt=o.registerOptionalTest.bind(this);r.test.next=o.registerOptionalTest.bind(this);r.setup=r.beforeEach=o.registerHook.bind(this,"setup");r.teardown=r.afterEach=o.registerHook.bind(this,"teardown");r.suiteSetup=r.before=o.registerHook.bind(this,"suiteSetup");r.suiteTeardown=r.after=o.registerHook.bind(this,"suiteTeardown");if(!this.allowConsole){o.disableConsole()}};o.disableConsole=function e(){console.log=console.error=console.assert=console.info=console.dir=console.warn=console.trace=console.time=console.timeEnd=function(){}};o.createSuite=function e(t){var r=[];Object.defineProperties(r,{name:{value:t},parent:{value:null,writable:true},suiteSetup:{value:[]},suiteTeardown:{value:[]},setup:{value:[]},teardown:{value:[]}});return r};o.sortSuite=function e(t){t.sort(function(e,t){var r=Array.isArray(e)?1:0;var n=Array.isArray(t)?1:0;if(r-n){return r-n}return e.order-t.order})};o.prototype.run=function e(t){var r=this,n=false;o.sortSuite(this.suite);this.emit("ready",function(){r.emit("run",r.testCount);var e=function(){if(n){return}if(typeof t==="function"){t()}};r.startTime=Date.now();r.runSuite(r.suite,0,function(){r.emit("report",r.ok,r.fail,r.optionalFail,r.skip,Date.now()-r.startTime);if(r.fail+r.optionalFail){r.emit("errorReport",r.errors)}r.emit("exit",e);setTimeout(e,1e4)})})};o.prototype.runSuite=function e(t,r,n){var i=this;if(r){i.emit("enterSuite",t.name,r-1)}var s=function(e){if(r){i.emit("exitSuite",t.name,r-1)}n(e)};this.runHooks(t.suiteSetup,r,function(e){if(e){i.patchError(e);i.errors.push({name:e.hookFn.hookName+"["+e.hookFn.hookType+"]",type:e.hookFn.hookType,fn:e.hookFn,error:e});i.failSuite(t,r,"suiteSetup",e.hookFn,e);i.runHooks(t.suiteTeardown,r,function(t){s(e)});return}i.runSuiteTests(t,r,function(e){i.runHooks(t.suiteTeardown,r,function(t){if(e){s(e)}else if(t){i.patchError(t);i.errors.push({name:t.hookFn.hookName+"["+t.hookFn.hookType+"]",type:t.hookFn.hookType,fn:t.hookFn,error:t});s(t)}else{s()}})})})};o.prototype.runSuiteTests=function e(t,r,i){var s=this;n.foreach(t,function(e,n){if(Array.isArray(e)){s.runSuite(e,r+1,n);return}s.runTest(t,r,e,n)}).fatal(s.bail).exec(i)};o.prototype.failSuite=function e(t,r,n,i,s){var o,a;for(o=0,a=t.length;o<a;o++){if(Array.isArray(t[o])){this.failSuite(t[o],r+1,n,i,s)}this.done++;this.fail++;this.emit("fail",t[o].testName,r,undefined,undefined,s)}};o.prototype.runTest=function e(t,r,n,i){var s=this;s.testInProgress=n;if(typeof n!=="function"){s.done++;s.skip++;s.emit("skip",n.testName,r);i();return}var a=t,l=t.setup,u=t.teardown;while(a.parent){a=a.parent;l=a.setup.concat(l);u=a.teardown.concat(u)}var c=n.length?o.asyncTest.bind(s):o.syncTest.bind(s);var f=function(e,t,o,a){if(e){s.done++;s.patchError(e);s.errors.push({name:(e.hookFn?e.hookFn.hookName+"["+e.hookFn.hookType+"] ":"")+n.testName,type:a,fn:n,optional:n.optional,error:e});if(n.optional){s.optionalFail++;s.emit("optionalFail",n.testName,r,t,o,e)}else{s.fail++;s.emit("fail",n.testName,r,t,o,e)}i(e)}else{s.done++;s.ok++;s.emit("ok",n.testName,r,t,o);i()}};s.runHooks(l,r,function(e){if(e){s.runHooks(u,r,function(t){f(e,undefined,undefined,"setup")});return}s.emit("enterTest",n.testName,r);c(n,function(e,t,i){s.emit("exitTest",n.testName,r);s.runHooks(u,r,function(r,s){if(e){f(e,t,i,"test",n)}else if(r){f(r,t,i,"teardown",s[s.length-1][2])}else{f(undefined,t,i)}})})})};o.syncTest=function e(t,r){var n,i,s=this.slowTime;r=this.freshCallback(r);var o={timeout:function(){},slow:function(e){s=e}};try{n=Date.now();t.call(o);i=Date.now()-n}catch(e){i=Date.now()-n;r(e,i,Math.floor(i/s));return}r(undefined,i,Math.floor(i/s))};o.asyncTest=function e(t,r){var n=this,i,o,a=false,l=null,u=n.slowTime;r=this.freshCallback(r);var c={timeout:function(e){if(a){return}if(l!==null){clearTimeout(l);l=null}l=setTimeout(function(){var e=new Error("Test timeout (local)");e.testTimeout=true;p(e)},e)},slow:function(e){u=e}};var f=function e(t){t.uncaught=true;p(t)};var p=function e(t){n.offUncaughtException(f);if(a){return}o=Date.now()-i;a=true;if(l!==null){clearTimeout(l);l=null}r(t,o,Math.floor(o/u))};l=setTimeout(function(){var e=new Error("Test timeout");e.testTimeout=true;p(e)},n.timeout);s(function(){i=Date.now();t.call(c,p)}).catch(function(e){p(e)});this.onceUncaughtException(f)};o.prototype.runHooks=function e(t,r,i){var s=this;n.foreach(t,function(e,t){var n=e.length?o.asyncHook.bind(s):o.syncHook.bind(s);s.emit("enterHook",e.hookType,e.hookName,r);n(e,function(n){s.emit("exitHook",e.hookType,e.hookName,r);if(n){n.hookFn=e}t(n)})}).fatal(true).exec(i)};o.syncHook=function e(t,r){r=this.freshCallback(r);try{t()}catch(e){r(e);return}r()};o.asyncHook=function e(t,r){var n=this,i=false;r=this.freshCallback(r);var o=function e(t){t.uncaught=true;a(t)};var a=function e(t){n.offUncaughtException(o);if(i){return}i=true;r(t)};s(function(){t(a)}).catch(function(e){a(e)});this.onceUncaughtException(o)};o.registerSuite=function e(t,r){if(!t||typeof t!=="string"||typeof r!=="function"){throw new Error("Usage is suite( name , fn )")}var n=this.registerStack[this.registerStack.length-1];var i=o.createSuite(t);this.registerStack.push(i);r();this.registerStack.pop();if(!i.length){return}Object.defineProperties(i,{order:{value:n.length}});o.sortSuite(i);n.push(i);Object.defineProperty(i,"parent",{value:n})};o.registerTest=function e(t,r,n){var i,s,o,a,l,u;if(!t||typeof t!=="string"){throw new Error("Usage is test( name , [fn] , [optional] )")}u=this.registerStack[this.registerStack.length-1];for(i=0,s=this.grep.length;i<s;i++){l=false;if(t.match(this.grep[i])){continue}for(o=1,a=this.registerStack.length;o<a;o++){if(this.registerStack[o].name.match(this.grep[i])){l=true;break}}if(!l){return}}this.testCount++;if(typeof r!=="function"){r={}}Object.defineProperties(r,{testName:{value:t},optional:{value:!!n},order:{value:u.length}});u.push(r)};o.registerSkipTest=function e(t){return o.registerTest.call(this,t)};o.registerOptionalTest=function e(t,r){return this.skipOptional?o.registerTest.call(this,t):o.registerTest.call(this,t,r,true)};o.registerHook=function e(t,r,n){var i;if(typeof r==="function"){n=r;r=undefined}else if(typeof n!=="function"){throw new Error("Usage is hook( [name] , fn )")}Object.defineProperties(n,{hookName:{value:r||n.name||"[no name]"},hookType:{value:t}});i=this.registerStack[this.registerStack.length-1];i[t].push(n)};o.prototype.freshCallback=function e(t){var r=this;return function(){var e=arguments;r.microTimeout(function(){t.apply(r,e)})}};o.prototype.patchError=function e(t){var r,n,i;if(!t.stack){return}i=t.stack;if(!Array.isArray(i)){i=t.stack.split("\n")}for(r=0,n=i.length;r<n;r++){if(i[r].match(/(^|\/)tea-time\.(min\.)?js/)){i=i.slice(0,r);break}}t.stack=i.join("\n")}}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"async-kit":8,"async-try-catch":13,"nextgen-events":42}],8:[function(e,t,r){"use strict";var n=e("./core.js");t.exports=n;n.wrapper=e("./wrapper.js");n.exit=e("./exit.js");var i=e("./safeTimeout.js");n.setSafeTimeout=i.setSafeTimeout;n.clearSafeTimeout=i.clearSafeTimeout},{"./core.js":9,"./exit.js":10,"./safeTimeout.js":11,"./wrapper.js":12}],9:[function(e,t,r){"use strict";var n=e("nextgen-events");var i=e("tree-kit/lib/extend.js");var s={};t.exports=s;s.AsyncError=function e(t){Error.call(this);Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor);this.message=t};s.AsyncError.prototype=Object.create(Error.prototype);s.AsyncError.prototype.constructor=s.AsyncError;s.Plan=function e(){throw new Error("[async] Cannot create an async Plan object directly")};s.Plan.prototype.constructor=s.Plan;var o={parallelLimit:{value:1,writable:true,enumerable:true,configurable:true},raceMode:{value:false,writable:true,enumerable:true,configurable:true},waterfallMode:{value:false,writable:true,enumerable:true,configurable:true},waterfallTransmitError:{value:false,writable:true,enumerable:true,configurable:true},whileAction:{value:undefined,writable:true,enumerable:true,configurable:true},whileActionBefore:{value:false,writable:true,enumerable:true,configurable:true},errorsAreFatal:{value:true,writable:true,enumerable:true,configurable:true},returnMapping1to1:{value:false,writable:true,enumerable:true,configurable:true},jobsData:{value:{},writable:true,enumerable:true},jobsKeys:{value:[],writable:true,enumerable:true},jobsUsing:{value:undefined,writable:true,enumerable:true},jobsTimeout:{value:undefined,writable:true,enumerable:true},useSafeTimeout:{value:false,writable:true,enumerable:true},returnLastJobOnly:{value:false,writable:true,enumerable:true},defaultAggregate:{value:undefined,writable:true,enumerable:true},returnAggregate:{value:false,writable:true,enumerable:true},transmitAggregate:{value:false,writable:true,enumerable:true},usingIsIterator:{value:false,writable:true,enumerable:true},thenAction:{value:undefined,writable:true,enumerable:true},catchAction:{value:undefined,writable:true,enumerable:true},finallyAction:{value:undefined,writable:true,enumerable:true},asyncEventNice:{value:-20,writable:true,enumerable:true},maxRetry:{value:0,writable:true,enumerable:true},retryTimeout:{value:0,writable:true,enumerable:true},retryMultiply:{value:1,writable:true,enumerable:true},retryMaxTimeout:{value:Infinity,writable:true,enumerable:true},execMappingMinInputs:{value:0,writable:true,enumerable:true},execMappingMaxInputs:{value:100,writable:true,enumerable:true},execMappingCallbacks:{value:["finally"],writable:true,enumerable:true},execMappingAggregateArg:{value:false,writable:true,enumerable:true},execMappingMinArgs:{value:0,writable:true,enumerable:true},execMappingMaxArgs:{value:101,writable:true,enumerable:true},execMappingSignature:{value:"( [finallyCallback] )",writable:true,enumerable:true},locked:{value:false,writable:true,enumerable:true}};s.do=function e(t){var r=Object.create(s.Plan.prototype,o);Object.defineProperties(r,{execInit:{value:a.bind(r)},execNext:{value:l.bind(r)},execCallback:{value:u},execLoopCallback:{value:c},execFinal:{value:f.bind(r)}});r.do(t);return r};s.parallel=function e(t){var r=Object.create(s.Plan.prototype,o);Object.defineProperties(r,{parallelLimit:{value:Infinity,writable:true,enumerable:true},execInit:{value:a.bind(r)},execNext:{value:l.bind(r)},execCallback:{value:u},execLoopCallback:{value:c},execFinal:{value:f.bind(r)}});r.do(t);return r};s.series=function e(t){var r=Object.create(s.Plan.prototype,o);Object.defineProperties(r,{parallelLimit:{value:1,enumerable:true},execInit:{value:a.bind(r)},execNext:{value:l.bind(r)},execCallback:{value:u},execLoopCallback:{value:c},execFinal:{value:f.bind(r)}});r.do(t);return r};s.race=function e(t){var r=Object.create(s.Plan.prototype,o);Object.defineProperties(r,{raceMode:{value:true,enumerable:true},parallelLimit:{value:Infinity,writable:true,enumerable:true},errorsAreFatal:{value:false,writable:true,enumerable:true},execInit:{value:a.bind(r)},execNext:{value:l.bind(r)},execCallback:{value:u},execLoopCallback:{value:c},execFinal:{value:f.bind(r)}});r.returnLastJobOnly=true;r.do(t);return r};s.waterfall=function e(t){var r=Object.create(s.Plan.prototype,o);Object.defineProperties(r,{waterfallMode:{value:true,enumerable:true},waterfallTransmitError:{value:false,writable:true,enumerable:true},parallelLimit:{value:1,enumerable:true},execInit:{value:a.bind(r)},execNext:{value:l.bind(r)},execCallback:{value:u},execLoopCallback:{value:c},execFinal:{value:f.bind(r)}});r.returnLastJobOnly=true;r.do(t);return r};s.foreach=s.forEach=function e(t,r){var n=Object.create(s.Plan.prototype,o);Object.defineProperties(n,{usingIsIterator:{value:true,writable:true,enumerable:true},errorsAreFatal:{value:false,writable:true,enumerable:true},execInit:{value:a.bind(n)},execNext:{value:l.bind(n)},execCallback:{value:u},execLoopCallback:{value:c},execFinal:{value:f.bind(n)}});n.do(t);n.iterator(r);return n};s.map=function e(t,r){var n=Object.create(s.Plan.prototype,o);Object.defineProperties(n,{parallelLimit:{value:Infinity,writable:true,enumerable:true},usingIsIterator:{value:true,writable:true,enumerable:true},errorsAreFatal:{value:false,writable:true,enumerable:true},returnMapping1to1:{value:true,writable:false,enumerable:true},execInit:{value:a.bind(n)},execNext:{value:l.bind(n)},execCallback:{value:u},execLoopCallback:{value:c},execFinal:{value:f.bind(n)}});n.do(t);n.iterator(r);return n};s.reduce=function e(t,r,n){var i=Object.create(s.Plan.prototype,o);Object.defineProperties(i,{parallelLimit:{value:1,writable:false,enumerable:true},usingIsIterator:{value:true,writable:true,enumerable:true},execInit:{value:a.bind(i)},execNext:{value:l.bind(i)},execCallback:{value:u},execLoopCallback:{value:c},execFinal:{value:f.bind(i)}});if(arguments.length<3){n=r;r=undefined;i.execMappingMinInputs=0;i.execMappingMaxInputs=100;i.execMappingCallbacks=["finally"];i.execMappingAggregateArg=true;i.execMappingMinArgs=1;i.execMappingMaxArgs=102;i.execMappingSignature="( aggregateArg, [finallyCallback] )"}i.transmitAggregate=true;i.returnAggregate=true;i.defaultAggregate=r;i.do(t);i.iterator(n);return i};s.while=function e(t){var r=Object.create(s.Plan.prototype,o);Object.defineProperties(r,{waterfallMode:{value:false,enumerable:true},whileAction:{value:undefined,writable:true,enumerable:true},whileActionBefore:{value:true,writable:false,enumerable:true},execInit:{value:a.bind(r)},execNext:{value:l.bind(r)},execCallback:{value:u},execLoopCallback:{value:c},execFinal:{value:f.bind(r)}});r.while(t);return r};s.and=function e(t){var r=Object.create(s.Plan.prototype,o);Object.defineProperties(r,{elseAction:{value:undefined,writable:true,enumerable:true},castToBoolean:{value:false,writable:true,enumerable:true},useLogicAnd:{value:true},execInit:{value:a.bind(r)},execNext:{value:l.bind(r)},execCallback:{value:p},execLoopCallback:{value:c},execFinal:{value:h.bind(r)}});r.do(t);return r};s.or=function e(t){var r=Object.create(s.Plan.prototype,o);Object.defineProperties(r,{elseAction:{value:undefined,writable:true,enumerable:true},castToBoolean:{value:false,writable:true,enumerable:true},useLogicAnd:{value:false},execInit:{value:a.bind(r)},execNext:{value:l.bind(r)},execCallback:{value:p},execLoopCallback:{value:c},execFinal:{value:h.bind(r)}});r.do(t);return r};s.if=function e(t){var r=Object.create(s.Plan.prototype,o);Object.defineProperties(r,{elseAction:{value:true,writable:true,enumerable:true},castToBoolean:{value:true,writable:true,enumerable:true},useLogicAnd:{value:true},execInit:{value:a.bind(r)},execNext:{value:l.bind(r)},execCallback:{value:p},execLoopCallback:{value:c},execFinal:{value:h.bind(r)}});if(t){r.do(t)}return r};s.if.and=s.if;s.if.or=function e(t){var r=Object.create(s.Plan.prototype,o);Object.defineProperties(r,{elseAction:{value:true,writable:true,enumerable:true},castToBoolean:{value:true,writable:true,enumerable:true},useLogicAnd:{value:false},execInit:{value:a.bind(r)},execNext:{value:l.bind(r)},execCallback:{value:p},execLoopCallback:{value:c},execFinal:{value:h.bind(r)}});if(t){r.do(t)}return r};s.callTimeout=function e(t,r,n,i){if(typeof n!=="function"){throw new Error("[async] async.callTimeout(): argument #0 should be a function")}var p=Object.create(s.Plan.prototype,o);Object.defineProperties(p,{returnLastJobOnly:{value:true,enumerable:true},jobsTimeout:{value:t,writable:true,enumerable:true},execInit:{value:a.bind(p)},execNext:{value:l.bind(p)},execCallback:{value:u},execLoopCallback:{value:c},execFinal:{value:f.bind(p)}});var h=[n.bind(i)].concat(Array.prototype.slice.call(arguments,4));p.do([h]);return p.exec(r)};s.Plan.prototype.do=function e(t){if(this.locked){return this}if(t&&typeof t==="object"){this.jobsData=t}else if(typeof t==="function"){this.jobsData=[t];this.returnLastJobOnly=true}else{this.jobsData={}}this.jobsKeys=Object.keys(this.jobsData);return this};s.Plan.prototype.parallel=function e(t){if(this.locked){return this}if(t===undefined||t===true){this.parallelLimit=Infinity}else if(t===false){this.parallelLimit=1}else if(typeof t==="number"){this.parallelLimit=t}return this};s.Plan.prototype.race=function e(t){if(!this.locked){this.raceMode=t||t===undefined?true:false}return this};s.Plan.prototype.waterfall=function e(t){if(!this.locked){this.waterfallMode=t||t===undefined?true:false}return this};s.Plan.prototype.while=function e(t,r){if(this.locked){return this}this.whileAction=t;if(r!==undefined){this.whileActionBefore=r?true:false}return this};s.Plan.prototype.repeat=function e(t){if(this.locked){return this}var r=0;if(typeof t!=="number"){t=parseInt(t)}this.whileActionBefore=true;this.whileAction=function(e,n,i){r++;i(r<=t)};return this};s.Plan.prototype.fatal=function e(t){if(!this.locked){this.errorsAreFatal=t||t===undefined?true:false}return this};s.Plan.prototype.boolean=function e(t){if(!this.locked){this.castToBoolean=t||t===undefined?true:false}return this};s.Plan.prototype.transmitError=function e(t){if(!this.locked){this.waterfallTransmitError=t||t===undefined?true:false}return this};s.Plan.prototype.timeout=function e(t){if(!this.locked){if(typeof t==="number"){this.jobsTimeout=t}else{this.jobsTimeout=undefined}}return this};s.Plan.prototype.safeTimeout=function e(t){if(!this.locked){this.useSafeTimeout=t===undefined?true:!!t}};s.Plan.prototype.retry=function e(t,r,n,i){if(this.locked){return this}if(typeof t==="number"){this.maxRetry=t}if(typeof r==="number"){this.retryTimeout=r}if(typeof n==="number"){this.retryMultiply=n}if(typeof i==="number"){this.retryMaxTimeout=i}return this};s.Plan.prototype.lastJobOnly=function e(t){if(!this.locked){this.returnLastJobOnly=t||t===undefined?true:false}return this};s.Plan.prototype.mapping1to1=function e(t){if(!this.locked){this.returnMapping1to1=t||t===undefined?true:false}return this};s.Plan.prototype.using=function e(t){if(!this.locked){this.jobsUsing=t}return this};s.Plan.prototype.iterator=function e(t){if(this.locked){return this}this.jobsUsing=t;this.usingIsIterator=true;return this};s.Plan.prototype.aggregator=function e(t,r,n){if(!this.locked){return this}this.transmitAggregate=t||t===undefined?true:false;this.returnAggregate=r||r===undefined?true:false;if(arguments.length>2){this.defaultAggregate=n}return this};s.Plan.prototype.usingIterator=function e(t){if(!this.locked){this.usingIsIterator=t||t===undefined?true:false}return this};s.Plan.prototype.nice=function e(t){if(this.locked){return this}if(t===undefined||t===null||t===true){this.asyncEventNice=-1}else if(t===false){this.asyncEventNice=-20}else{this.asyncEventNice=t}return this};s.Plan.prototype.then=function e(t){if(!this.locked){this.thenAction=t}return this};s.Plan.prototype.else=function e(t){if(!this.locked){
this.elseAction=t||true}return this};s.Plan.prototype.catch=function e(t){if(!this.locked){this.catchAction=t||true}return this};s.Plan.prototype.finally=function e(t){if(!this.locked){this.finallyAction=t||true}return this};s.Plan.prototype.clone=function e(){var t=Object.create(s.Plan.prototype,o);i(null,t,this);t.locked=false;return t};s.Plan.prototype.export=function e(t){switch(t){case"execFinally":return this.clone().execFinally.bind(this);case"execThenCatch":return this.clone().execThenCatch.bind(this);case"execThenElse":return this.clone().execThenElse.bind(this);case"execThenElseCatch":return this.clone().execThenElseCatch.bind(this);case"execArgs":return this.clone().execArgs.bind(this);case"execKV":return this.clone().execKV.bind(this);default:return this.clone().exec.bind(this)}};s.Plan.prototype.exec=function e(){var t={inputs:[],callbacks:{}},r=0,n;if(arguments.length<this.execMappingMinArgs){throw new Error("[async] Too few arguments, in this instance, the function signature is: fn"+this.execMappingSignature)}else if(arguments.length>this.execMappingMaxArgs){throw new Error("[async] Too much arguments, in this instance, the function signature is: fn"+this.execMappingSignature)}if(this.execMappingAggregateArg){r++;t.aggregate=arguments[0]}if(this.execMappingMinInputs===this.execMappingMaxInputs){t.inputs=Array.prototype.slice.call(arguments,r,this.execMappingMaxInputs+r);for(n=0;n<this.execMappingCallbacks.length&&t.inputs.length+n<arguments.length;n++){t.callbacks[this.execMappingCallbacks[n]]=arguments[t.inputs.length+r+n]}}else{t.inputs=Array.prototype.slice.call(arguments,r,-this.execMappingCallbacks.length);for(n=0;n<this.execMappingCallbacks.length;n++){t.callbacks[this.execMappingCallbacks[n]]=arguments[t.inputs.length+r+n]}}return this.execInit(t)};s.Plan.prototype.execFinally=function e(t){return this.execInit({inputs:[],callbacks:{finally:t}})};s.Plan.prototype.execThenCatch=function e(t,r,n){return this.execInit({inputs:[],callbacks:{then:t,catch:r,finally:n}})};s.Plan.prototype.execThenElse=function e(t,r,n){return this.execInit({inputs:[],callbacks:{then:t,else:r,finally:n}})};s.Plan.prototype.execThenElseCatch=function e(t,r,n,i){return this.execInit({inputs:[],callbacks:{then:t,else:r,catch:n,finally:i}})};s.Plan.prototype.execArgs=function e(){return this.execInit({inputs:arguments,callbacks:{}})};s.Plan.prototype.execMapping=function e(t){if(this.locked){return this}t=i(null,{minInputs:0,maxInputs:0},t);var r,n,s=5;t.minInputs=parseInt(t.minInputs);t.maxInputs=parseInt(t.maxInputs);if(t.minInputs<t.maxInputs){this.execMappingMinInputs=t.minInputs;this.execMappingMaxInputs=t.maxInputs}else{this.execMappingMinInputs=t.maxInputs;this.execMappingMaxInputs=t.minInputs}this.execMappingCallbacks=Array.isArray(t.callbacks)?t.callbacks:[];this.execMappingInputsName=Array.isArray(t.inputsName)?t.inputsName:[];this.execMappingSignature="( ";if(this.execMappingMinInputs===this.execMappingMaxInputs){this.execMappingMinArgs=this.execMappingMinInputs;this.execMappingMaxArgs=this.execMappingMaxInputs+this.execMappingCallbacks.length;if(t.aggregateArg){this.execMappingAggregateArg=t.aggregateArg;this.execMappingMinArgs++;this.execMappingMaxArgs++;this.execMappingSignature+="aggregateValue"}for(r=0;r<this.execMappingMaxInputs;r++){if(r>0||t.aggregateArg){this.execMappingSignature+=", "}if(r>=s&&typeof this.execMappingInputsName[r]!=="string"){this.execMappingSignature+="... ";break}this.execMappingSignature+=typeof this.execMappingInputsName[r]==="string"?this.execMappingInputsName[r]:"arg#"+(r+1)}for(n=0;n<this.execMappingCallbacks.length;n++){if(r+n>0||t.aggregateArg){this.execMappingSignature+=", "}this.execMappingSignature+="["+this.execMappingCallbacks[n]+"Callback]"}}else{this.execMappingMinArgs=this.execMappingMinInputs+this.execMappingCallbacks.length;this.execMappingMaxArgs=this.execMappingMaxInputs+this.execMappingCallbacks.length;if(t.aggregateArg){this.execMappingAggregateArg=t.aggregateArg;this.execMappingMinArgs++;this.execMappingMaxArgs++;this.execMappingSignature+="aggregateValue"}for(r=0;r<this.execMappingMaxInputs;r++){if(r>0||t.aggregateArg){this.execMappingSignature+=", "}if(r<this.execMappingMinInputs){if(r>=s&&typeof this.execMappingInputsName[r]!=="string"){this.execMappingSignature+="... ";break}this.execMappingSignature+=typeof this.execMappingInputsName[r]==="string"?this.execMappingInputsName[r]:"arg#"+(r+1)}else{if(r>=s&&typeof this.execMappingInputsName[r]!=="string"){this.execMappingSignature+="[...] ";break}this.execMappingSignature+="["+(typeof this.execMappingInputsName[r]==="string"?this.execMappingInputsName[r]:"arg#"+(r+1))+"]"}}for(n=0;n<this.execMappingCallbacks.length;n++){if(r+n>0||t.aggregateArg){this.execMappingSignature+=", "}this.execMappingSignature+=this.execMappingCallbacks[n]+"Callback"}}this.execMappingSignature+=" )";return this};s.Plan.prototype.execKV=function e(t){if(t.inputs===undefined){t.inputs=[]}else if(!Array.isArray(t.inputs)){t.inputs=[t.inputs]}if(t.callbacks===undefined||typeof t.callbacks!=="object"){t.callbacks={}}if(t.then){t.callbacks.then=t.then}if(t.else){t.callbacks.else=t.else}if(t.catch){t.callbacks.catch=t.catch}if(t.finally){t.callbacks.finally=t.finally}return this.execInit(t)};s.Plan.prototype.execLoop=function e(t){return this.execInit({},t)};s.Plan.prototype.execJob=function e(t,r,n,o){var a=this,l,u=t.jobsKeys[n];var c=Object.create(s.JobContext.prototype,{execContext:{value:t,enumerable:true},indexOfKey:{value:n,enumerable:true},tryIndex:{value:o,enumerable:true},aborted:{value:false,writable:true,enumerable:true},abortedLoop:{value:false,writable:true,enumerable:true}});Object.defineProperty(c,"callback",{value:this.execCallback.bind(this,c),enumerable:true});Object.defineProperty(c.callback,"jobContext",{value:c,enumerable:true});t.jobsStatus[u].status="pending";t.jobsStatus[u].tried++;if(typeof this.jobsUsing==="function"){if(this.usingIsIterator){if(this.transmitAggregate){if(this.jobsUsing.length<=3){this.jobsUsing.call(c,t.aggregate,r,c.callback)}else if(this.jobsUsing.length<=4){this.jobsUsing.call(c,t.aggregate,r,Array.isArray(t.jobsData)?n:u,c.callback)}else{this.jobsUsing.call(c,t.aggregate,r,Array.isArray(t.jobsData)?n:u,t.jobsData,c.callback)}}else{if(this.jobsUsing.length<=2){this.jobsUsing.call(c,r,c.callback)}else if(this.jobsUsing.length<=3){this.jobsUsing.call(c,r,Array.isArray(t.jobsData)?n:u,c.callback)}else{this.jobsUsing.call(c,r,Array.isArray(t.jobsData)?n:u,t.jobsData,c.callback)}}}else if(Array.isArray(r)){l=r.slice();if(this.transmitAggregate){l.unshift(t.aggregate)}l.push(c.callback);this.jobsUsing.apply(c,l)}else{this.jobsUsing.call(c,r,c.callback)}}else if(typeof r==="function"){if(this.waterfallMode&&n>0){l=t.results[t.jobsKeys[n-1]].slice(this.waterfallTransmitError?0:1);l.push(c.callback);r.apply(c,l)}else if(Array.isArray(this.jobsUsing)||this.execMappingMaxInputs){if(Array.isArray(this.jobsUsing)){l=i(null,[],this.jobsUsing,t.execInputs)}else{l=i(null,[],t.execInputs)}l.push(c.callback);r.apply(c,l)}else{r.call(c,c.callback)}}else if(Array.isArray(r)&&typeof r[0]==="function"){l=r.slice(1);l.push(c.callback);r[0].apply(c,l)}else if(typeof r==="object"&&r instanceof s.Plan){r.exec(c.callback)}else{this.execCallback.call(this,c);return this}if(t.jobsTimeoutTimers[u]!==undefined){clearTimeout(t.jobsTimeoutTimers[u]);t.jobsTimeoutTimers[u]=undefined}if(t.retriesTimers[u]!==undefined){clearTimeout(t.retriesTimers[u]);t.retriesTimers[u]=undefined}if(typeof this.jobsTimeout==="number"&&this.jobsTimeout!==Infinity){t.jobsTimeoutTimers[u]=setTimeout(function(){t.jobsTimeoutTimers[u]=undefined;t.jobsStatus[u].status="timeout";c.emit("timeout");a.execCallback.call(a,c,new s.AsyncError("jobTimeout"))},this.jobsTimeout)}return this};s.Plan.prototype.execAction=function e(t,r,n){if(typeof r==="function"){r.apply(t,n)}else if(typeof r==="object"&&r instanceof s.Plan){r.exec()}};s.JobContext=function e(){throw new Error("[async] Cannot create an async JobContext object directly")};s.JobContext.prototype=Object.create(n.prototype);s.JobContext.prototype.constructor=s.JobContext;s.JobContext.prototype.abort=function e(){this.aborted=true;this.callback.apply(undefined,arguments)};s.JobContext.prototype.abortLoop=function e(){this.aborted=true;this.abortedLoop=true;this.callback.apply(undefined,arguments)};s.ExecContext=function e(){throw new Error("[async] Cannot create an async ExecContext object directly")};s.ExecContext.prototype=Object.create(n.prototype);s.ExecContext.prototype.constructor=s.ExecContext;s.ExecContext.prototype.getJobsStatus=function e(){var t,r,n=Array.isArray(this.jobsData)?[]:{};for(t=0;t<this.jobsKeys.length;t++){r=this.jobsKeys[t];n[r]=i(null,{job:this.jobsData[r],result:this.results[r]},this.jobsStatus[r])}return n};function a(e,t){var r,n,o=Array.isArray(this.jobsData);if(t&&t.whileIterator===-1){n=t;n.whileIterator=0}else{n=Object.create(s.ExecContext.prototype,{plan:{value:this},aggregate:{value:"aggregate"in e?e.aggregate:this.defaultAggregate,writable:true,enumerable:true},results:{value:o?[]:{},writable:true,enumerable:true},result:{value:undefined,writable:true,enumerable:true},jobsTimeoutTimers:{value:o?[]:{},writable:true},jobsStatus:{value:o?[]:{},writable:true,enumerable:true},retriesTimers:{value:o?[]:{},writable:true},retriesCounter:{value:o?[]:{},writable:true,enumerable:true},tryUserResponseCounter:{value:o?[]:{},writable:true,enumerable:true},tryResponseCounter:{value:o?[]:{},writable:true,enumerable:true},iterator:{value:0,writable:true,enumerable:true},pending:{value:0,writable:true,enumerable:true},resolved:{value:0,writable:true,enumerable:true},ok:{value:0,writable:true,enumerable:true},failed:{value:0,writable:true,enumerable:true},status:{value:undefined,writable:true,enumerable:true},error:{value:undefined,writable:true,enumerable:true},statusTriggerJobsKey:{value:undefined,writable:true,enumerable:true},whileStatus:{value:undefined,writable:true},whileChecked:{value:false,writable:true}});if(!t){Object.defineProperties(n,{root:{value:n,enumerable:true},jobsData:{value:o?this.jobsData.slice(0):i(null,{},this.jobsData),enumerable:true},jobsKeys:{value:this.jobsKeys.slice(0),enumerable:true},execInputs:{value:e.inputs,enumerable:true},execCallbacks:{value:e.callbacks},whileIterator:{value:0,enumerable:true,writable:true}})}else{Object.defineProperties(n,{root:{value:t.root,enumerable:true},jobsData:{value:t.jobsData,enumerable:true},jobsKeys:{value:t.jobsKeys,enumerable:true},execInputs:{value:t.execInputs,enumerable:true},execCallbacks:{value:t.execCallbacks},whileIterator:{value:t.whileIterator+1,enumerable:true,writable:true}})}Object.defineProperties(n,{waiting:{value:n.jobsKeys.length,writable:true,enumerable:true}});for(r=0;r<n.jobsKeys.length;r++){n.jobsStatus[n.jobsKeys[r]]={status:"waiting",errors:[],tried:0}}n.setNice(this.asyncEventNice);if(t===undefined){n.root.on("resolved",this.execFinal.bind(this,n));if(typeof this.whileAction==="function"){n.root.on("while",this.whileAction.bind(this));n.root.on("nextLoop",this.execLoop.bind(this))}else{this.whileAction=undefined;n.whileStatus=false}n.root.on("next",this.execNext.bind(this));if(this.whileAction&&this.whileActionBefore){n.whileIterator=-1;n.root.emit("while",n.error,n.results,this.execLoopCallback.bind(this,n),null);return this}}}if(n.jobsKeys.length<=0){n.root.emit("resolved",n.error,n.results);n.root.emit("progress",{resolved:n.resolved,ok:n.ok,failed:n.failed,pending:n.pending,waiting:n.waiting,loop:n.whileIterator},n.error,n.results);n.root.emit("finish",n.error,n.results);return n.root}n.root.emit("next",n);return n.root}function l(e){var t,r,n=e.jobsKeys.length,i,s;i=e.iterator;for(;e.iterator<n&&e.pending<this.parallelLimit;e.iterator++){e.pending++;e.waiting--;t=e.iterator;r=e.jobsKeys[t];e.retriesCounter[r]=0;e.tryResponseCounter[r]=[];e.tryResponseCounter[r][0]=0;e.tryUserResponseCounter[r]=[];e.tryUserResponseCounter[r][0]=0;e.results[r]=undefined;s=e.iterator}for(t=i;t<=s;t++){this.execJob(e,e.jobsData[e.jobsKeys[t]],t,0)}}function u(e,t){var r=e.execContext,n=e.aborted,i=e.abortedLoop,o=e.indexOfKey,a=e.tryIndex;var l=this,u,c,f=r.jobsKeys.length,p=r.jobsKeys[o];var h=false,d=false,v=false,y=false;r.tryResponseCounter[p][a]++;if(!(t instanceof s.AsyncError)){r.tryUserResponseCounter[p][a]++}if(r.jobsTimeoutTimers[p]!==undefined){clearTimeout(r.jobsTimeoutTimers[p]);r.jobsTimeoutTimers[p]=undefined}if(r.retriesTimers[p]!==undefined){clearTimeout(r.retriesTimers[p]);r.retriesTimers[p]=undefined}if(r.tryUserResponseCounter[p][a]>1){r.jobsStatus[p].errors.push(new Error("This job has called its completion callback "+r.tryUserResponseCounter[p][a]+" times"));return}if(!n&&r.results[p]!==undefined){return}if(!n&&t&&this.maxRetry>r.retriesCounter[p]&&r.tryResponseCounter[p][a]<=1){r.jobsStatus[p].errors.push(t);u=this.retryTimeout*Math.pow(this.retryMultiply,r.retriesCounter[p]);if(u>this.retryMaxTimeout){u=this.retryMaxTimeout}r.retriesCounter[p]++;c=r.retriesCounter[p];r.retriesTimers[p]=setTimeout(function(){r.retriesTimers[p]=undefined;r.tryResponseCounter[p][c]=0;r.tryUserResponseCounter[p][c]=0;l.execJob(r,r.jobsData[p],o,c)},u);return}if(!n&&t&&a<r.retriesCounter[p]){return}r.resolved++;r.pending--;r.aggregate=arguments[2];if(n){r.failed++;r.jobsStatus[p].status="aborted"}else if(t){r.failed++;r.jobsStatus[p].errors.push(t);if(t instanceof s.AsyncError&&t.message==="jobTimeout"){r.jobsStatus[p].status="timeout"}else{r.jobsStatus[p].status="failed"}}else{r.ok++;r.jobsStatus[p].status="ok"}if(this.returnMapping1to1){r.results[p]=arguments[2]}else{r.results[p]=Array.prototype.slice.call(arguments,1)}if(r.status===undefined){if(this.raceMode&&!t){r.status="ok";r.statusTriggerJobsKey=p;if(this.whileAction&&!i){y=true}else{d=true}}else if(!this.raceMode&&t&&this.errorsAreFatal){r.status="fail";r.error=t;r.statusTriggerJobsKey=p;if(this.whileAction&&!i){y=true}else{d=true}}else if(n){r.status="aborted";r.statusTriggerJobsKey=p;if(this.whileAction&&!i){y=true}else{d=true}}}if(r.resolved>=f){if(r.status===undefined){if(this.raceMode){r.status="fail"}else{r.status="ok"}r.statusTriggerJobsKey=p;if(this.whileAction){y=true}else{d=v=true}}else{if(!this.whileAction||r.whileChecked&&r.whileStatus!==true){v=true}}}else if(r.status===undefined){if(r.iterator<f){h=true}}else if(r.pending<=0){if(!this.whileAction||r.whileChecked&&r.whileStatus!==true){v=true}}if(d){r.root.emit("resolved",r.error,r.results)}if(h){r.root.emit("next",r)}if(y){r.root.emit("while",r.error,r.results,this.execLoopCallback.bind(this,r),null)}r.root.emit("progress",{resolved:r.resolved,ok:r.ok,failed:r.failed,pending:r.pending,waiting:r.waiting,loop:r.whileIterator},r.error,r.results);if(v){r.root.emit("finish",r.error,r.results)}}function c(e){var t,r;var n=false,i=false,s=false;if(arguments.length<=1){t=undefined;r=false}else if(arguments[1]instanceof Error){e.error=arguments[1];t=arguments[1];r=false}else if(arguments.length<=2){t=arguments[1];r=t?true:false}else{t=arguments[2];r=t?true:false}if(r){e.whileStatus=true;n=true}else{e.whileStatus=false;i=true;if(e.pending<=0){s=true}}if(i){e.root.emit("resolved",e.error,e.results)}if(n){e.root.emit("nextLoop",e)}if(s){e.root.emit("finish",e.error,e.results)}e.whileChecked=true}function f(e,t,r){var n;if(t){if(this.returnAggregate){n=[t,e.aggregate]}else if(this.returnLastJobOnly){n=r[e.statusTriggerJobsKey]}else{n=[t,r]}if(this.catchAction){this.execAction(e,this.catchAction,n)}if(t&&e.execCallbacks.catch){this.execAction(e,e.execCallbacks.catch,n)}}else{if(this.returnAggregate){n=[e.aggregate]}else if(this.returnLastJobOnly){n=r[e.statusTriggerJobsKey].slice(1)}else{n=[r]}if(this.thenAction){this.execAction(e,this.thenAction,n)}if(e.execCallbacks.then){this.execAction(e,e.execCallbacks.then,n)}}if(this.returnAggregate){n=[t,e.aggregate]}else if(this.returnLastJobOnly){n=r[e.statusTriggerJobsKey]}else{n=[t,r]}if(this.finallyAction){this.execAction(e,this.finallyAction,n)}if(e.execCallbacks.finally){this.execAction(e,e.execCallbacks.finally,n)}}function p(e){var t=e.execContext,r=e.indexOfKey,n=e.tryIndex;var i=this,o,a,l,u,c=t.jobsKeys.length,f=t.jobsKeys[r];var p=false,h=false,d=false;if(arguments.length<=1){t.result=undefined;o=false}else if(arguments[1]instanceof Error){t.error=u=arguments[1];t.result=arguments[1];o=false}else if(arguments.length<=2){t.result=arguments[1];o=t.result?true:false}else{t.result=arguments[2];o=t.result?true:false}t.tryResponseCounter[f][n]++;if(!(u instanceof s.AsyncError)){t.tryUserResponseCounter[f][n]++}if(t.jobsTimeoutTimers[f]!==undefined){clearTimeout(t.jobsTimeoutTimers[f]);t.jobsTimeoutTimers[f]=undefined}if(t.retriesTimers[f]!==undefined){clearTimeout(t.retriesTimers[f]);t.retriesTimers[f]=undefined}if(t.results[f]!==undefined){return}if(t.tryUserResponseCounter[f][n]>1){return}if(u&&this.maxRetry>t.retriesCounter[f]&&t.tryResponseCounter[f][n]<=1){a=this.retryTimeout*Math.pow(this.retryMultiply,t.retriesCounter[f]);if(a>this.retryMaxTimeout){a=this.retryMaxTimeout}t.retriesCounter[f]++;l=t.retriesCounter[f];t.retriesTimers[f]=setTimeout(function(){t.retriesTimers[f]=undefined;t.tryResponseCounter[f][l]=0;t.tryUserResponseCounter[f][l]=0;i.execJob(t,t.jobsData[f],r,l)},a);return}if(u&&n<t.retriesCounter[f]){return}t.resolved++;t.pending--;if(u){t.failed++;if(u instanceof s.AsyncError&&u.message==="jobTimeout"){t.jobsStatus[f].status="timeout"}else{t.jobsStatus[f].status="failed"}}else{t.ok++;t.jobsStatus[f].status="ok"}if(this.castToBoolean&&(!(t.result instanceof Error)||!this.catchAction)){t.result=o}t.results[f]=t.result;if(o!==this.useLogicAnd&&t.status===undefined){t.status=!this.useLogicAnd;h=true}if(t.resolved>=c){if(t.status===undefined){t.status=this.useLogicAnd;h=true}d=true}else if(t.status===undefined){if(t.iterator<c){p=true}}else if(t.pending<=0){d=true}if(h){t.root.emit("resolved",t.result)}if(p){t.root.emit("next",t)}t.root.emit("progress",{resolved:t.resolved,pending:t.pending,waiting:t.waiting,loop:t.whileIterator},t.result);if(d){t.root.emit("finish",t.result)}}function h(e,t){if(t instanceof Error){if(this.catchAction){this.execAction(e,this.catchAction,[t])}else if(this.elseAction){this.execAction(e,this.elseAction,[t])}}else if(!e.result&&this.elseAction){this.execAction(e,this.elseAction,[t])}else if(e.result&&this.thenAction){this.execAction(e,this.thenAction,[t])}if(this.finallyAction){this.execAction(e,this.finallyAction,[t])}if(t instanceof Error){if(e.execCallbacks.catch){this.execAction(e,e.execCallbacks.catch,[t])}else if(e.execCallbacks.else){this.execAction(e,e.execCallbacks.else,[t])}}else if(!e.result&&e.execCallbacks.else){this.execAction(e,e.execCallbacks.else,[t])}else if(e.result&&e.execCallbacks.then){this.execAction(e,e.execCallbacks.then,[t])}if(e.execCallbacks.finally){this.execAction(e,e.execCallbacks.finally,[t])}}},{"nextgen-events":42,"tree-kit/lib/extend.js":48}],10:[function(e,t,r){(function(r){"use strict";var n=e("./async.js");var i=false;function s(e,t){if(i){return}i=true;var s=r.listeners("asyncExit");if(!s.length){r.exit(e);return}if(t===undefined){t=1e3}n.parallel(s).using(function(r,n){if(r.length<3){r(e,t);n()}else{r(e,t,n)}}).fatal(false).timeout(t).exec(function(){r.exit(e)})}t.exports=s}).call(this,e("_process"))},{"./async.js":8,_process:18}],11:[function(e,t,r){"use strict";r.setSafeTimeout=function e(t,r){var n={isSafeTimeout:true};n.timer=setTimeout(function(){n.timer=setTimeout(function(){n.timer=setTimeout(function(){n.timer=setTimeout(t,0)},r/2)},r/2)},0);return n};r.clearSafeTimeout=function e(t){if(t&&typeof t==="object"&&t.isSafeTimeout){clearTimeout(t.timer)}else{clearTimeout(t)}}},{}],12:[function(e,t,r){"use strict";var n={};t.exports=n;n.timeout=function e(t,r,n){var i=function(){var e=n||this,i=false,s=Array.prototype.slice.call(arguments),o=s.pop();var a=function(){if(i){return}i=true;o.apply(e,arguments)};s.push(a);t.apply(e,s);setTimeout(a.bind(undefined,new Error("Timeout")),r)};return i}},{}],13:[function(e,t,r){(function(r,n){"use strict";function i(){throw new Error("Use AsyncTryCatch.try() instead.")}t.exports=i;i.prototype.__prototypeUID__="async-try-catch/AsyncTryCatch";i.prototype.__prototypeVersion__=e("../package.json").version;if(n.AsyncTryCatch){if(n.AsyncTryCatch.prototype.__prototypeUID__==="async-try-catch/AsyncTryCatch"){var s=i.prototype.__prototypeVersion__.split(".");var o=n.AsyncTryCatch.prototype.__prototypeVersion__.split(".");if(o[0]!==s[0]||s[0]==="0"&&o[1]!==s[1]){throw new Error("Incompatible version of AsyncTryCatch already installed on global.AsyncTryCatch: "+n.AsyncTryCatch.prototype.__prototypeVersion__+", current version: "+i.prototype.__prototypeVersion__)}}else{throw new Error("Incompatible module already installed on global.AsyncTryCatch")}}else{n.AsyncTryCatch=i;n.AsyncTryCatch.stack=[];n.AsyncTryCatch.substituted=false;n.AsyncTryCatch.NextGenEvents=[]}if(r.browser&&!n.setImmediate){n.setImmediate=function e(t){return setTimeout(t,0)};n.clearImmediate=function e(t){return clearTimeout(t)}}if(!n.Vanilla){n.Vanilla={}}if(!n.Vanilla.setTimeout){n.Vanilla.setTimeout=setTimeout}if(!n.Vanilla.setImmediate){n.Vanilla.setImmediate=setImmediate}if(!n.Vanilla.nextTick){n.Vanilla.nextTick=r.nextTick}i.try=function e(t){var r=Object.create(i.prototype,{fn:{value:t,enumerable:true},parent:{value:n.AsyncTryCatch.stack[n.AsyncTryCatch.stack.length-1]}});return r};i.prototype.catch=function e(t){Object.defineProperties(this,{catchFn:{value:t,enumerable:true}});if(!n.AsyncTryCatch.substituted){i.substitute()}try{n.AsyncTryCatch.stack.push(this);this.fn();n.AsyncTryCatch.stack.pop()}catch(e){n.AsyncTryCatch.stack.pop();this.callCatchFn(e)}};i.prototype.callCatchFn=function e(t){if(!this.parent){this.catchFn(t);return}try{n.AsyncTryCatch.stack.push(this.parent);this.catchFn(t);n.AsyncTryCatch.stack.pop()}catch(e){n.AsyncTryCatch.stack.pop();this.parent.callCatchFn(e)}};i.timerWrapper=function e(t,r){var r,i,s,o=Array.prototype.slice.call(arguments,1);if(typeof r!=="function"||!n.AsyncTryCatch.stack.length){return t.apply(this,o)}i=n.AsyncTryCatch.stack[n.AsyncTryCatch.stack.length-1];s=function e(){try{n.AsyncTryCatch.stack.push(i);return r.apply(this,arguments);n.AsyncTryCatch.stack.pop()}catch(e){n.AsyncTryCatch.stack.pop();i.callCatchFn(e)}};o[0]=s;return t.apply(this,o)};i.addListenerWrapper=function e(t,r,i,s,o){var i,a,l,u;if(typeof i==="object"){s=i;i=s.fn;delete s.fn}if(typeof i!=="function"||!n.AsyncTryCatch.stack.length){return t.call(this,r,i,s)}a=n.AsyncTryCatch.stack[n.AsyncTryCatch.stack.length-1];if(o){u=false;l=function e(){if(u){return}u=true;this.removeListener(r,l);try{n.AsyncTryCatch.stack.push(a);return i.apply(this,arguments);n.AsyncTryCatch.stack.pop()}catch(e){n.AsyncTryCatch.stack.pop();a.callCatchFn(e)}}}else{l=function e(){try{n.AsyncTryCatch.stack.push(a);return i.apply(this,arguments);n.AsyncTryCatch.stack.pop()}catch(e){n.AsyncTryCatch.stack.pop();a.callCatchFn(e)}}}l.listener=i;return t.call(this,r,l,s)};i.setTimeout=i.timerWrapper.bind(undefined,n.Vanilla.setTimeout);i.setImmediate=i.timerWrapper.bind(undefined,n.Vanilla.setImmediate);i.nextTick=i.timerWrapper.bind(r,n.Vanilla.nextTick);i.addListener=function e(t,r){return i.addListenerWrapper.call(this,i.NodeEvents.__addListener,t,r)};i.addListenerOnce=function e(t,r){return i.addListenerWrapper.call(this,i.NodeEvents.__addListener,t,r,undefined,true)};i.removeListener=function e(t,r){return i.NodeEvents.__removeListener.call(this,t,r)};i.ngevAddListener=function e(t,r,n){if(!n){n={}}if(n.id===undefined){n.id=r}return i.addListenerWrapper.call(this,i.NextGenEvents[this.asyncTryCatchId].on,t,r,n)};i.ngevAddListenerOnce=function e(t,r,n){if(!n){n={}}if(n.id===undefined){n.id=r}return i.addListenerWrapper.call(this,i.NextGenEvents[this.asyncTryCatchId].once,t,r,n)};i.ngevRemoveListener=function e(t,r){return i.NextGenEvents[this.asyncTryCatchId].off.call(this,t,r)};i.substitute=function t(){n.AsyncTryCatch.substituted=true;n.setTimeout=i.setTimeout;n.setImmediate=i.setTimeout;r.nextTick=i.nextTick;try{i.NodeEvents=n.EventEmitter||e("events")}catch(e){}if(i.NodeEvents){if(!i.NodeEvents.__addListener){i.NodeEvents.__addListener=i.NodeEvents.prototype.on}if(!i.NodeEvents.__addListenerOnce){i.NodeEvents.__addListenerOnce=i.NodeEvents.prototype.once}if(!i.NodeEvents.__removeListener){i.NodeEvents.__removeListener=i.NodeEvents.prototype.removeListener}i.NodeEvents.prototype.on=i.addListener;i.NodeEvents.prototype.addListener=i.addListener;i.NodeEvents.prototype.once=i.addListenerOnce;i.NodeEvents.prototype.removeListener=i.removeListener}for(var s=0;s<i.NextGenEvents.length;s++){i.NextGenEvents[s].prototype.on=i.ngevAddListener;i.NextGenEvents[s].prototype.addListener=i.ngevAddListener;i.NextGenEvents[s].prototype.once=i.ngevAddListenerOnce;i.NextGenEvents[s].prototype.off=i.ngevRemoveListener;i.NextGenEvents[s].prototype.removeListener=i.ngevRemoveListener}};i.restore=function e(){n.AsyncTryCatch.substituted=false;n.setTimeout=n.Vanilla.setTimeout;n.setImmediate=n.Vanilla.setImmediate;r.nextTick=n.Vanilla.nextTick;if(i.NodeEvents){i.NodeEvents.prototype.on=i.NodeEvents.__addListener;i.NodeEvents.prototype.addListener=i.NodeEvents.__addListener;i.NodeEvents.prototype.once=i.NodeEvents.__addListenerOnce;i.NodeEvents.prototype.removeListener=i.NodeEvents.__removeListener}for(var t=0;t<i.NextGenEvents.length;t++){i.NextGenEvents[t].prototype.on=i.NextGenEvents[t].on;i.NextGenEvents[t].prototype.addListener=i.NextGenEvents[t].on;i.NextGenEvents[t].prototype.once=i.NextGenEvents[t].once;i.NextGenEvents[t].prototype.off=i.NextGenEvents[t].off;i.NextGenEvents[t].prototype.removeListener=i.NextGenEvents[t].removeListener}}}).call(this,e("_process"),typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"../package.json":14,_process:18,events:16}],14:[function(e,t,r){t.exports={name:"async-try-catch",version:"0.3.4",description:"Async try catch",main:"lib/AsyncTryCatch.js",directories:{test:"test"},dependencies:{},devDependencies:{browserify:"^13.1.0","expect.js":"^0.3.1",jshint:"^2.9.3",mocha:"^3.0.2","nextgen-events":"^0.9.7","uglify-js":"^2.7.3"},scripts:{test:"mocha -R dot"},repository:{type:"git",url:"git+https://github.com/cronvel/async-try-catch.git"},keywords:["async","try","catch"],author:{name:"Cédric Ronvel"},license:"MIT",bugs:{url:"https://github.com/cronvel/async-try-catch/issues"},copyright:{title:"Async Try-Catch",years:[2015,2016],owner:"Cédric Ronvel"},readme:"\n\n# Async Try-Catch\n\nThe name says it all: it performs async try catch. \n\n* License: MIT\n* Current status: beta\n* Platform: Node.js only\n\n",readmeFilename:"README.md",gitHead:"ab55eb1391c246e79f8482d6ffd292f0d808094a",homepage:"https://github.com/cronvel/async-try-catch#readme",_id:"async-try-catch@0.3.4",_shasum:"e481b68ff368cbfca3a8b24150eb3e6b4b3c1682",_from:"async-try-catch@0.3.4"}},{}],15:[function(e,t,r){},{}],16:[function(e,t,r){function n(){this._events=this._events||{};this._maxListeners=this._maxListeners||undefined}t.exports=n;n.EventEmitter=n;n.prototype._events=undefined;n.prototype._maxListeners=undefined;n.defaultMaxListeners=10;n.prototype.setMaxListeners=function(e){if(!s(e)||e<0||isNaN(e))throw TypeError("n must be a positive number");this._maxListeners=e;return this};n.prototype.emit=function(e){var t,r,n,s,l,u;if(!this._events)this._events={};if(e==="error"){if(!this._events.error||o(this._events.error)&&!this._events.error.length){t=arguments[1];if(t instanceof Error){throw t}else{var c=new Error('Uncaught, unspecified "error" event. ('+t+")");c.context=t;throw c}}}r=this._events[e];if(a(r))return false;if(i(r)){switch(arguments.length){case 1:r.call(this);break;case 2:r.call(this,arguments[1]);break;case 3:r.call(this,arguments[1],arguments[2]);break;default:s=Array.prototype.slice.call(arguments,1);r.apply(this,s)}}else if(o(r)){s=Array.prototype.slice.call(arguments,1);u=r.slice();n=u.length;for(l=0;l<n;l++)u[l].apply(this,s)}return true};n.prototype.addListener=function(e,t){var r;if(!i(t))throw TypeError("listener must be a function");if(!this._events)this._events={};if(this._events.newListener)this.emit("newListener",e,i(t.listener)?t.listener:t);if(!this._events[e])this._events[e]=t;else if(o(this._events[e]))this._events[e].push(t);else this._events[e]=[this._events[e],t];if(o(this._events[e])&&!this._events[e].warned){if(!a(this._maxListeners)){r=this._maxListeners}else{r=n.defaultMaxListeners}if(r&&r>0&&this._events[e].length>r){this._events[e].warned=true;console.error("(node) warning: possible EventEmitter memory "+"leak detected. %d listeners added. "+"Use emitter.setMaxListeners() to increase limit.",this._events[e].length);if(typeof console.trace==="function"){console.trace()}}}return this};n.prototype.on=n.prototype.addListener;n.prototype.once=function(e,t){if(!i(t))throw TypeError("listener must be a function");var r=false;function n(){this.removeListener(e,n);if(!r){r=true;t.apply(this,arguments)}}n.listener=t;this.on(e,n);return this};n.prototype.removeListener=function(e,t){var r,n,s,a;if(!i(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;r=this._events[e];s=r.length;n=-1;if(r===t||i(r.listener)&&r.listener===t){delete this._events[e];if(this._events.removeListener)this.emit("removeListener",e,t)}else if(o(r)){for(a=s;a-- >0;){if(r[a]===t||r[a].listener&&r[a].listener===t){n=a;break}}if(n<0)return this;if(r.length===1){r.length=0;delete this._events[e]}else{r.splice(n,1)}if(this._events.removeListener)this.emit("removeListener",e,t)}return this};n.prototype.removeAllListeners=function(e){var t,r;if(!this._events)return this;if(!this._events.removeListener){if(arguments.length===0)this._events={};else if(this._events[e])delete this._events[e];return this}if(arguments.length===0){for(t in this._events){if(t==="removeListener")continue;this.removeAllListeners(t)}this.removeAllListeners("removeListener");this._events={};return this}r=this._events[e];if(i(r)){this.removeListener(e,r)}else if(r){while(r.length)this.removeListener(e,r[r.length-1])}delete this._events[e];return this};n.prototype.listeners=function(e){var t;if(!this._events||!this._events[e])t=[];else if(i(this._events[e]))t=[this._events[e]];else t=this._events[e].slice();return t};n.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(i(t))return 1;else if(t)return t.length}return 0};n.listenerCount=function(e,t){return e.listenerCount(t)};function i(e){return typeof e==="function"}function s(e){return typeof e==="number"}function o(e){return typeof e==="object"&&e!==null}function a(e){return e===void 0}},{}],17:[function(e,t,r){t.exports=function(e){return!!(e!=null&&(e._isBuffer||e.constructor&&typeof e.constructor.isBuffer==="function"&&e.constructor.isBuffer(e)))}},{}],18:[function(e,t,r){var n=t.exports={};var i;var s;(function(){try{i=setTimeout}catch(e){i=function(){throw new Error("setTimeout is not defined")}}try{s=clearTimeout}catch(e){s=function(){throw new Error("clearTimeout is not defined")}}})();var o=[];var a=false;var l;var u=-1;function c(){if(!a||!l){return}a=false;if(l.length){o=l.concat(o)}else{u=-1}if(o.length){f()}}function f(){if(a){return}var e=i.call(null,c);a=true;var t=o.length;while(t){l=o;o=[];while(++u<t){if(l){l[u].run()}}u=-1;t=o.length}l=null;a=false;s.call(null,e)}n.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}o.push(new p(e,t));if(o.length===1&&!a){i.call(null,f,0)}};function p(e,t){this.fun=e;this.array=t}p.prototype.run=function(){this.fun.apply(null,this.array)};n.title="browser";n.browser=true;n.env={};n.argv=[];n.version="";n.versions={};function h(){}n.on=h;n.addListener=h;n.once=h;n.off=h;n.removeListener=h;n.removeAllListeners=h;n.emit=h;n.binding=function(e){throw new Error("process.binding is not supported");
};n.cwd=function(){return"/"};n.chdir=function(e){throw new Error("process.chdir is not supported")};n.umask=function(){return 0}},{}],19:[function(t,r,n){(function(t){(function(i){var s=typeof n=="object"&&n&&!n.nodeType&&n;var o=typeof r=="object"&&r&&!r.nodeType&&r;var a=typeof t=="object"&&t;if(a.global===a||a.window===a||a.self===a){i=a}var l,u=2147483647,c=36,f=1,p=26,h=38,d=700,v=72,y=128,m="-",g=/^xn--/,b=/[^\x20-\x7E]/,x=/[\x2E\u3002\uFF0E\uFF61]/g,w={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},k=c-f,_=Math.floor,T=String.fromCharCode,A;function j(e){throw new RangeError(w[e])}function C(e,t){var r=e.length;var n=[];while(r--){n[r]=t(e[r])}return n}function E(e,t){var r=e.split("@");var n="";if(r.length>1){n=r[0]+"@";e=r[1]}e=e.replace(x,".");var i=e.split(".");var s=C(i,t).join(".");return n+s}function L(e){var t=[],r=0,n=e.length,i,s;while(r<n){i=e.charCodeAt(r++);if(i>=55296&&i<=56319&&r<n){s=e.charCodeAt(r++);if((s&64512)==56320){t.push(((i&1023)<<10)+(s&1023)+65536)}else{t.push(i);r--}}else{t.push(i)}}return t}function S(e){return C(e,function(e){var t="";if(e>65535){e-=65536;t+=T(e>>>10&1023|55296);e=56320|e&1023}t+=T(e);return t}).join("")}function O(e){if(e-48<10){return e-22}if(e-65<26){return e-65}if(e-97<26){return e-97}return c}function M(e,t){return e+22+75*(e<26)-((t!=0)<<5)}function I(e,t,r){var n=0;e=r?_(e/d):e>>1;e+=_(e/t);for(;e>k*p>>1;n+=c){e=_(e/k)}return _(n+(k+1)*e/(e+h))}function N(e){var t=[],r=e.length,n,i=0,s=y,o=v,a,l,h,d,g,b,x,w,k;a=e.lastIndexOf(m);if(a<0){a=0}for(l=0;l<a;++l){if(e.charCodeAt(l)>=128){j("not-basic")}t.push(e.charCodeAt(l))}for(h=a>0?a+1:0;h<r;){for(d=i,g=1,b=c;;b+=c){if(h>=r){j("invalid-input")}x=O(e.charCodeAt(h++));if(x>=c||x>_((u-i)/g)){j("overflow")}i+=x*g;w=b<=o?f:b>=o+p?p:b-o;if(x<w){break}k=c-w;if(g>_(u/k)){j("overflow")}g*=k}n=t.length+1;o=I(i-d,n,d==0);if(_(i/n)>u-s){j("overflow")}s+=_(i/n);i%=n;t.splice(i++,0,s)}return S(t)}function P(e){var t,r,n,i,s,o,a,l,h,d,g,b=[],x,w,k,A;e=L(e);x=e.length;t=y;r=0;s=v;for(o=0;o<x;++o){g=e[o];if(g<128){b.push(T(g))}}n=i=b.length;if(i){b.push(m)}while(n<x){for(a=u,o=0;o<x;++o){g=e[o];if(g>=t&&g<a){a=g}}w=n+1;if(a-t>_((u-r)/w)){j("overflow")}r+=(a-t)*w;t=a;for(o=0;o<x;++o){g=e[o];if(g<t&&++r>u){j("overflow")}if(g==t){for(l=r,h=c;;h+=c){d=h<=s?f:h>=s+p?p:h-s;if(l<d){break}A=l-d;k=c-d;b.push(T(M(d+A%k,0)));l=_(A/k)}b.push(T(M(l,0)));s=I(r,w,n==i);r=0;++n}}++r;++t}return b.join("")}function F(e){return E(e,function(e){return g.test(e)?N(e.slice(4).toLowerCase()):e})}function D(e){return E(e,function(e){return b.test(e)?"xn--"+P(e):e})}l={version:"1.4.1",ucs2:{decode:L,encode:S},decode:N,encode:P,toASCII:D,toUnicode:F};if(typeof e=="function"&&typeof e.amd=="object"&&e.amd){e("punycode",function(){return l})}else if(s&&o){if(r.exports==s){o.exports=l}else{for(A in l){l.hasOwnProperty(A)&&(s[A]=l[A])}}}else{i.punycode=l}})(this)}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{}],20:[function(e,t,r){"use strict";function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.exports=function(e,t,r,s){t=t||"&";r=r||"=";var o={};if(typeof e!=="string"||e.length===0){return o}var a=/\+/g;e=e.split(t);var l=1e3;if(s&&typeof s.maxKeys==="number"){l=s.maxKeys}var u=e.length;if(l>0&&u>l){u=l}for(var c=0;c<u;++c){var f=e[c].replace(a,"%20"),p=f.indexOf(r),h,d,v,y;if(p>=0){h=f.substr(0,p);d=f.substr(p+1)}else{h=f;d=""}v=decodeURIComponent(h);y=decodeURIComponent(d);if(!n(o,v)){o[v]=y}else if(i(o[v])){o[v].push(y)}else{o[v]=[o[v],y]}}return o};var i=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"}},{}],21:[function(e,t,r){"use strict";var n=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};t.exports=function(e,t,r,a){t=t||"&";r=r||"=";if(e===null){e=undefined}if(typeof e==="object"){return s(o(e),function(o){var a=encodeURIComponent(n(o))+r;if(i(e[o])){return s(e[o],function(e){return a+encodeURIComponent(n(e))}).join(t)}else{return a+encodeURIComponent(n(e[o]))}}).join(t)}if(!a)return"";return encodeURIComponent(n(a))+r+encodeURIComponent(n(e))};var i=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"};function s(e,t){if(e.map)return e.map(t);var r=[];for(var n=0;n<e.length;n++){r.push(t(e[n],n))}return r}var o=Object.keys||function(e){var t=[];for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t.push(r)}return t}},{}],22:[function(e,t,r){"use strict";r.decode=r.parse=e("./decode");r.encode=r.stringify=e("./encode")},{"./decode":20,"./encode":21}],23:[function(e,t,r){"use strict";var n=e("punycode");var i=e("./util");r.parse=w;r.resolve=_;r.resolveObject=T;r.format=k;r.Url=s;function s(){this.protocol=null;this.slashes=null;this.auth=null;this.host=null;this.port=null;this.hostname=null;this.hash=null;this.search=null;this.query=null;this.pathname=null;this.path=null;this.href=null}var o=/^([a-z0-9.+-]+:)/i,a=/:[0-9]*$/,l=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,u=["<",">",'"',"`"," ","\r","\n","\t"],c=["{","}","|","\\","^","`"].concat(u),f=["'"].concat(c),p=["%","/","?",";","#"].concat(f),h=["/","?","#"],d=255,v=/^[+a-z0-9A-Z_-]{0,63}$/,y=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,m={javascript:true,"javascript:":true},g={javascript:true,"javascript:":true},b={http:true,https:true,ftp:true,gopher:true,file:true,"http:":true,"https:":true,"ftp:":true,"gopher:":true,"file:":true},x=e("querystring");function w(e,t,r){if(e&&i.isObject(e)&&e instanceof s)return e;var n=new s;n.parse(e,t,r);return n}s.prototype.parse=function(e,t,r){if(!i.isString(e)){throw new TypeError("Parameter 'url' must be a string, not "+typeof e)}var s=e.indexOf("?"),a=s!==-1&&s<e.indexOf("#")?"?":"#",u=e.split(a),c=/\\/g;u[0]=u[0].replace(c,"/");e=u.join(a);var w=e;w=w.trim();if(!r&&e.split("#").length===1){var k=l.exec(w);if(k){this.path=w;this.href=w;this.pathname=k[1];if(k[2]){this.search=k[2];if(t){this.query=x.parse(this.search.substr(1))}else{this.query=this.search.substr(1)}}else if(t){this.search="";this.query={}}return this}}var _=o.exec(w);if(_){_=_[0];var T=_.toLowerCase();this.protocol=T;w=w.substr(_.length)}if(r||_||w.match(/^\/\/[^@\/]+@[^@\/]+/)){var A=w.substr(0,2)==="//";if(A&&!(_&&g[_])){w=w.substr(2);this.slashes=true}}if(!g[_]&&(A||_&&!b[_])){var j=-1;for(var C=0;C<h.length;C++){var E=w.indexOf(h[C]);if(E!==-1&&(j===-1||E<j))j=E}var L,S;if(j===-1){S=w.lastIndexOf("@")}else{S=w.lastIndexOf("@",j)}if(S!==-1){L=w.slice(0,S);w=w.slice(S+1);this.auth=decodeURIComponent(L)}j=-1;for(var C=0;C<p.length;C++){var E=w.indexOf(p[C]);if(E!==-1&&(j===-1||E<j))j=E}if(j===-1)j=w.length;this.host=w.slice(0,j);w=w.slice(j);this.parseHost();this.hostname=this.hostname||"";var O=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!O){var M=this.hostname.split(/\./);for(var C=0,I=M.length;C<I;C++){var N=M[C];if(!N)continue;if(!N.match(v)){var P="";for(var F=0,D=N.length;F<D;F++){if(N.charCodeAt(F)>127){P+="x"}else{P+=N[F]}}if(!P.match(v)){var R=M.slice(0,C);var U=M.slice(C+1);var q=N.match(y);if(q){R.push(q[1]);U.unshift(q[2])}if(U.length){w="/"+U.join(".")+w}this.hostname=R.join(".");break}}}}if(this.hostname.length>d){this.hostname=""}else{this.hostname=this.hostname.toLowerCase()}if(!O){this.hostname=n.toASCII(this.hostname)}var H=this.port?":"+this.port:"";var G=this.hostname||"";this.host=G+H;this.href+=this.host;if(O){this.hostname=this.hostname.substr(1,this.hostname.length-2);if(w[0]!=="/"){w="/"+w}}}if(!m[T]){for(var C=0,I=f.length;C<I;C++){var W=f[C];if(w.indexOf(W)===-1)continue;var K=encodeURIComponent(W);if(K===W){K=escape(W)}w=w.split(W).join(K)}}var J=w.indexOf("#");if(J!==-1){this.hash=w.substr(J);w=w.slice(0,J)}var B=w.indexOf("?");if(B!==-1){this.search=w.substr(B);this.query=w.substr(B+1);if(t){this.query=x.parse(this.query)}w=w.slice(0,B)}else if(t){this.search="";this.query={}}if(w)this.pathname=w;if(b[T]&&this.hostname&&!this.pathname){this.pathname="/"}if(this.pathname||this.search){var H=this.pathname||"";var V=this.search||"";this.path=H+V}this.href=this.format();return this};function k(e){if(i.isString(e))e=w(e);if(!(e instanceof s))return s.prototype.format.call(e);return e.format()}s.prototype.format=function(){var e=this.auth||"";if(e){e=encodeURIComponent(e);e=e.replace(/%3A/i,":");e+="@"}var t=this.protocol||"",r=this.pathname||"",n=this.hash||"",s=false,o="";if(this.host){s=e+this.host}else if(this.hostname){s=e+(this.hostname.indexOf(":")===-1?this.hostname:"["+this.hostname+"]");if(this.port){s+=":"+this.port}}if(this.query&&i.isObject(this.query)&&Object.keys(this.query).length){o=x.stringify(this.query)}var a=this.search||o&&"?"+o||"";if(t&&t.substr(-1)!==":")t+=":";if(this.slashes||(!t||b[t])&&s!==false){s="//"+(s||"");if(r&&r.charAt(0)!=="/")r="/"+r}else if(!s){s=""}if(n&&n.charAt(0)!=="#")n="#"+n;if(a&&a.charAt(0)!=="?")a="?"+a;r=r.replace(/[?#]/g,function(e){return encodeURIComponent(e)});a=a.replace("#","%23");return t+s+r+a+n};function _(e,t){return w(e,false,true).resolve(t)}s.prototype.resolve=function(e){return this.resolveObject(w(e,false,true)).format()};function T(e,t){if(!e)return t;return w(e,false,true).resolveObject(t)}s.prototype.resolveObject=function(e){if(i.isString(e)){var t=new s;t.parse(e,false,true);e=t}var r=new s;var n=Object.keys(this);for(var o=0;o<n.length;o++){var a=n[o];r[a]=this[a]}r.hash=e.hash;if(e.href===""){r.href=r.format();return r}if(e.slashes&&!e.protocol){var l=Object.keys(e);for(var u=0;u<l.length;u++){var c=l[u];if(c!=="protocol")r[c]=e[c]}if(b[r.protocol]&&r.hostname&&!r.pathname){r.path=r.pathname="/"}r.href=r.format();return r}if(e.protocol&&e.protocol!==r.protocol){if(!b[e.protocol]){var f=Object.keys(e);for(var p=0;p<f.length;p++){var h=f[p];r[h]=e[h]}r.href=r.format();return r}r.protocol=e.protocol;if(!e.host&&!g[e.protocol]){var d=(e.pathname||"").split("/");while(d.length&&!(e.host=d.shift()));if(!e.host)e.host="";if(!e.hostname)e.hostname="";if(d[0]!=="")d.unshift("");if(d.length<2)d.unshift("");r.pathname=d.join("/")}else{r.pathname=e.pathname}r.search=e.search;r.query=e.query;r.host=e.host||"";r.auth=e.auth;r.hostname=e.hostname||e.host;r.port=e.port;if(r.pathname||r.search){var v=r.pathname||"";var y=r.search||"";r.path=v+y}r.slashes=r.slashes||e.slashes;r.href=r.format();return r}var m=r.pathname&&r.pathname.charAt(0)==="/",x=e.host||e.pathname&&e.pathname.charAt(0)==="/",w=x||m||r.host&&e.pathname,k=w,_=r.pathname&&r.pathname.split("/")||[],d=e.pathname&&e.pathname.split("/")||[],T=r.protocol&&!b[r.protocol];if(T){r.hostname="";r.port=null;if(r.host){if(_[0]==="")_[0]=r.host;else _.unshift(r.host)}r.host="";if(e.protocol){e.hostname=null;e.port=null;if(e.host){if(d[0]==="")d[0]=e.host;else d.unshift(e.host)}e.host=null}w=w&&(d[0]===""||_[0]==="")}if(x){r.host=e.host||e.host===""?e.host:r.host;r.hostname=e.hostname||e.hostname===""?e.hostname:r.hostname;r.search=e.search;r.query=e.query;_=d}else if(d.length){if(!_)_=[];_.pop();_=_.concat(d);r.search=e.search;r.query=e.query}else if(!i.isNullOrUndefined(e.search)){if(T){r.hostname=r.host=_.shift();var A=r.host&&r.host.indexOf("@")>0?r.host.split("@"):false;if(A){r.auth=A.shift();r.host=r.hostname=A.shift()}}r.search=e.search;r.query=e.query;if(!i.isNull(r.pathname)||!i.isNull(r.search)){r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")}r.href=r.format();return r}if(!_.length){r.pathname=null;if(r.search){r.path="/"+r.search}else{r.path=null}r.href=r.format();return r}var j=_.slice(-1)[0];var C=(r.host||e.host||_.length>1)&&(j==="."||j==="..")||j==="";var E=0;for(var L=_.length;L>=0;L--){j=_[L];if(j==="."){_.splice(L,1)}else if(j===".."){_.splice(L,1);E++}else if(E){_.splice(L,1);E--}}if(!w&&!k){for(;E--;E){_.unshift("..")}}if(w&&_[0]!==""&&(!_[0]||_[0].charAt(0)!=="/")){_.unshift("")}if(C&&_.join("/").substr(-1)!=="/"){_.push("")}var S=_[0]===""||_[0]&&_[0].charAt(0)==="/";if(T){r.hostname=r.host=S?"":_.length?_.shift():"";var A=r.host&&r.host.indexOf("@")>0?r.host.split("@"):false;if(A){r.auth=A.shift();r.host=r.hostname=A.shift()}}w=w||r.host&&_.length;if(w&&!S){_.unshift("")}if(!_.length){r.pathname=null;r.path=null}else{r.pathname=_.join("/")}if(!i.isNull(r.pathname)||!i.isNull(r.search)){r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")}r.auth=e.auth||r.auth;r.slashes=r.slashes||e.slashes;r.href=r.format();return r};s.prototype.parseHost=function(){var e=this.host;var t=a.exec(e);if(t){t=t[0];if(t!==":"){this.port=t.substr(1)}e=e.substr(0,e.length-t.length)}if(e)this.hostname=e}},{"./util":24,punycode:19,querystring:22}],24:[function(e,t,r){"use strict";t.exports={isString:function(e){return typeof e==="string"},isObject:function(e){return typeof e==="object"&&e!==null},isNull:function(e){return e===null},isNullOrUndefined:function(e){return e==null}}},{}],25:[function(e,t,r){"use strict";r.__esModule=true;r.convertChangesToDMP=n;function n(e){var t=[],r=void 0,n=void 0;for(var i=0;i<e.length;i++){r=e[i];if(r.added){n=1}else if(r.removed){n=-1}else{n=0}t.push([n,r.value])}return t}},{}],26:[function(e,t,r){"use strict";r.__esModule=true;r.convertChangesToXML=n;function n(e){var t=[];for(var r=0;r<e.length;r++){var n=e[r];if(n.added){t.push("<ins>")}else if(n.removed){t.push("<del>")}t.push(i(n.value));if(n.added){t.push("</ins>")}else if(n.removed){t.push("</del>")}}return t.join("")}function i(e){var t=e;t=t.replace(/&/g,"&amp;");t=t.replace(/</g,"&lt;");t=t.replace(/>/g,"&gt;");t=t.replace(/"/g,"&quot;");return t}},{}],27:[function(e,t,r){"use strict";r.__esModule=true;r.default=n;function n(){}n.prototype={diff:function e(t,r){var n=arguments.length<=2||arguments[2]===undefined?{}:arguments[2];var o=n.callback;if(typeof n==="function"){o=n;n={}}this.options=n;var a=this;function l(e){if(o){setTimeout(function(){o(undefined,e)},0);return true}else{return e}}t=this.castInput(t);r=this.castInput(r);t=this.removeEmpty(this.tokenize(t));r=this.removeEmpty(this.tokenize(r));var u=r.length,c=t.length;var f=1;var p=u+c;var h=[{newPos:-1,components:[]}];var d=this.extractCommon(h[0],r,t,0);if(h[0].newPos+1>=u&&d+1>=c){return l([{value:r.join(""),count:r.length}])}function v(){for(var e=-1*f;e<=f;e+=2){var n=void 0;var o=h[e-1],p=h[e+1],d=(p?p.newPos:0)-e;if(o){h[e-1]=undefined}var v=o&&o.newPos+1<u,y=p&&0<=d&&d<c;if(!v&&!y){h[e]=undefined;continue}if(!v||y&&o.newPos<p.newPos){n=s(p);a.pushComponent(n.components,undefined,true)}else{n=o;n.newPos++;a.pushComponent(n.components,true,undefined)}d=a.extractCommon(n,r,t,e);if(n.newPos+1>=u&&d+1>=c){return l(i(a,n.components,r,t,a.useLongestToken))}else{h[e]=n}}f++}if(o){(function e(){setTimeout(function(){if(f>p){return o()}if(!v()){e()}},0)})()}else{while(f<=p){var y=v();if(y){return y}}}},pushComponent:function e(t,r,n){var i=t[t.length-1];if(i&&i.added===r&&i.removed===n){t[t.length-1]={count:i.count+1,added:r,removed:n}}else{t.push({count:1,added:r,removed:n})}},extractCommon:function e(t,r,n,i){var s=r.length,o=n.length,a=t.newPos,l=a-i,u=0;while(a+1<s&&l+1<o&&this.equals(r[a+1],n[l+1])){a++;l++;u++}if(u){t.components.push({count:u})}t.newPos=a;return l},equals:function e(t,r){return t===r},removeEmpty:function e(t){var r=[];for(var n=0;n<t.length;n++){if(t[n]){r.push(t[n])}}return r},castInput:function e(t){return t},tokenize:function e(t){return t.split("")}};function i(e,t,r,n,i){var s=0,o=t.length,a=0,l=0;for(;s<o;s++){var u=t[s];if(!u.removed){if(!u.added&&i){var c=r.slice(a,a+u.count);c=c.map(function(e,t){var r=n[l+t];return r.length>e.length?r:e});u.value=c.join("")}else{u.value=r.slice(a,a+u.count).join("")}a+=u.count;if(!u.added){l+=u.count}}else{u.value=n.slice(l,l+u.count).join("");l+=u.count;if(s&&t[s-1].added){var f=t[s-1];t[s-1]=t[s];t[s]=f}}}var p=t[o-1];if(o>1&&(p.added||p.removed)&&e.equals("",p.value)){t[o-2].value+=p.value;t.pop()}return t}function s(e){return{newPos:e.newPos,components:e.components.slice(0)}}},{}],28:[function(e,t,r){"use strict";r.__esModule=true;r.characterDiff=undefined;r.diffChars=a;var n=e("./base");var i=s(n);function s(e){return e&&e.__esModule?e:{default:e}}var o=r.characterDiff=new i.default;function a(e,t,r){return o.diff(e,t,r)}},{"./base":27}],29:[function(e,t,r){"use strict";r.__esModule=true;r.cssDiff=undefined;r.diffCss=a;var n=e("./base");var i=s(n);function s(e){return e&&e.__esModule?e:{default:e}}var o=r.cssDiff=new i.default;o.tokenize=function(e){return e.split(/([{}:;,]|\s+)/)};function a(e,t,r){return o.diff(e,t,r)}},{"./base":27}],30:[function(e,t,r){"use strict";r.__esModule=true;r.jsonDiff=undefined;var n=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol?"symbol":typeof e};r.diffJson=c;r.canonicalize=f;var i=e("./base");var s=a(i);var o=e("./line");function a(e){return e&&e.__esModule?e:{default:e}}var l=Object.prototype.toString;var u=r.jsonDiff=new s.default;u.useLongestToken=true;u.tokenize=o.lineDiff.tokenize;u.castInput=function(e){return typeof e==="string"?e:JSON.stringify(f(e),undefined,"  ")};u.equals=function(e,t){return s.default.prototype.equals(e.replace(/,([\r\n])/g,"$1"),t.replace(/,([\r\n])/g,"$1"))};function c(e,t,r){return u.diff(e,t,r)}function f(e,t,r){t=t||[];r=r||[];var i=void 0;for(i=0;i<t.length;i+=1){if(t[i]===e){return r[i]}}var s=void 0;if("[object Array]"===l.call(e)){t.push(e);s=new Array(e.length);r.push(s);for(i=0;i<e.length;i+=1){s[i]=f(e[i],t,r)}t.pop();r.pop();return s}if(e&&e.toJSON){e=e.toJSON()}if((typeof e==="undefined"?"undefined":n(e))==="object"&&e!==null){t.push(e);s={};r.push(s);var o=[],a=void 0;for(a in e){if(e.hasOwnProperty(a)){o.push(a)}}o.sort();for(i=0;i<o.length;i+=1){a=o[i];s[a]=f(e[a],t,r)}t.pop();r.pop()}else{s=e}return s}},{"./base":27,"./line":31}],31:[function(e,t,r){"use strict";r.__esModule=true;r.lineDiff=undefined;r.diffLines=l;r.diffTrimmedLines=u;var n=e("./base");var i=o(n);var s=e("../util/params");function o(e){return e&&e.__esModule?e:{default:e}}var a=r.lineDiff=new i.default;a.tokenize=function(e){var t=[],r=e.split(/(\n|\r\n)/);if(!r[r.length-1]){r.pop()}for(var n=0;n<r.length;n++){var i=r[n];if(n%2&&!this.options.newlineIsToken){t[t.length-1]+=i}else{if(this.options.ignoreWhitespace){i=i.trim()}t.push(i)}}return t};function l(e,t,r){return a.diff(e,t,r)}function u(e,t,r){var n=(0,s.generateOptions)(r,{ignoreWhitespace:true});return a.diff(e,t,n)}},{"../util/params":39,"./base":27}],32:[function(e,t,r){"use strict";r.__esModule=true;r.sentenceDiff=undefined;r.diffSentences=a;var n=e("./base");var i=s(n);function s(e){return e&&e.__esModule?e:{default:e}}var o=r.sentenceDiff=new i.default;o.tokenize=function(e){return e.split(/(\S.+?[.!?])(?=\s+|$)/)};function a(e,t,r){return o.diff(e,t,r)}},{"./base":27}],33:[function(e,t,r){"use strict";r.__esModule=true;r.wordDiff=undefined;r.diffWords=c;r.diffWordsWithSpace=f;var n=e("./base");var i=o(n);var s=e("../util/params");function o(e){return e&&e.__esModule?e:{default:e}}var a=/^[A-Za-z\xC0-\u02C6\u02C8-\u02D7\u02DE-\u02FF\u1E00-\u1EFF]+$/;var l=/\S/;var u=r.wordDiff=new i.default;u.equals=function(e,t){return e===t||this.options.ignoreWhitespace&&!l.test(e)&&!l.test(t)};u.tokenize=function(e){var t=e.split(/(\s+|\b)/);for(var r=0;r<t.length-1;r++){if(!t[r+1]&&t[r+2]&&a.test(t[r])&&a.test(t[r+2])){t[r]+=t[r+2];t.splice(r+1,2);r--}}return t};function c(e,t,r){var n=(0,s.generateOptions)(r,{ignoreWhitespace:true});return u.diff(e,t,n)}function f(e,t,r){return u.diff(e,t,r)}},{"../util/params":39,"./base":27}],34:[function(e,t,r){"use strict";r.__esModule=true;r.canonicalize=r.convertChangesToXML=r.convertChangesToDMP=r.parsePatch=r.applyPatches=r.applyPatch=r.createPatch=r.createTwoFilesPatch=r.structuredPatch=r.diffJson=r.diffCss=r.diffSentences=r.diffTrimmedLines=r.diffLines=r.diffWordsWithSpace=r.diffWords=r.diffChars=r.Diff=undefined;var n=e("./diff/base");var i=y(n);var s=e("./diff/character");var o=e("./diff/word");var a=e("./diff/line");var l=e("./diff/sentence");var u=e("./diff/css");var c=e("./diff/json");var f=e("./patch/apply");var p=e("./patch/parse");var h=e("./patch/create");var d=e("./convert/dmp");var v=e("./convert/xml");function y(e){return e&&e.__esModule?e:{default:e}}r.Diff=i.default;r.diffChars=s.diffChars;r.diffWords=o.diffWords;r.diffWordsWithSpace=o.diffWordsWithSpace;r.diffLines=a.diffLines;r.diffTrimmedLines=a.diffTrimmedLines;r.diffSentences=l.diffSentences;r.diffCss=u.diffCss;r.diffJson=c.diffJson;r.structuredPatch=h.structuredPatch;r.createTwoFilesPatch=h.createTwoFilesPatch;r.createPatch=h.createPatch;r.applyPatch=f.applyPatch;r.applyPatches=f.applyPatches;r.parsePatch=p.parsePatch;r.convertChangesToDMP=d.convertChangesToDMP;r.convertChangesToXML=v.convertChangesToXML;r.canonicalize=c.canonicalize},{"./convert/dmp":25,"./convert/xml":26,"./diff/base":27,"./diff/character":28,"./diff/css":29,"./diff/json":30,"./diff/line":31,"./diff/sentence":32,"./diff/word":33,"./patch/apply":35,"./patch/create":36,"./patch/parse":37}],35:[function(e,t,r){"use strict";r.__esModule=true;r.applyPatch=a;r.applyPatches=l;var n=e("./parse");var i=e("../util/distance-iterator");var s=o(i);function o(e){return e&&e.__esModule?e:{default:e}}function a(e,t){var r=arguments.length<=2||arguments[2]===undefined?{}:arguments[2];if(typeof t==="string"){t=(0,n.parsePatch)(t)}if(Array.isArray(t)){if(t.length>1){throw new Error("applyPatch only works with a single input.")}t=t[0]}var i=e.split("\n"),o=t.hunks,a=r.compareLine||function(e,t,r,n){return t===n},l=0,u=r.fuzzFactor||0,c=0,f=0,p=void 0,h=void 0;function d(e,t){for(var r=0;r<e.lines.length;r++){var n=e.lines[r],s=n[0],o=n.substr(1);if(s===" "||s==="-"){if(!a(t+1,i[t],s,o)){l++;if(l>u){return false}}t++}}return true}for(var v=0;v<o.length;v++){var y=o[v],m=i.length-y.oldLines,g=0,b=f+y.oldStart-1;var x=(0,s.default)(b,c,m);for(;g!==undefined;g=x()){if(d(y,b+g)){y.offset=f+=g;break}}if(g===undefined){return false}c=y.offset+y.oldStart+y.oldLines}for(var w=0;w<o.length;w++){var k=o[w],_=k.offset+k.newStart-1;if(k.newLines==0){_++}for(var T=0;T<k.lines.length;T++){var A=k.lines[T],j=A[0],C=A.substr(1);if(j===" "){_++}else if(j==="-"){i.splice(_,1)}else if(j==="+"){i.splice(_,0,C);_++}else if(j==="\\"){var E=k.lines[T-1]?k.lines[T-1][0]:null;if(E==="+"){p=true}else if(E==="-"){h=true}}}}if(p){while(!i[i.length-1]){i.pop()}}else if(h){i.push("")}return i.join("\n")}function l(e,t){if(typeof e==="string"){e=(0,n.parsePatch)(e)}var r=0;function i(){var n=e[r++];if(!n){return t.complete()}t.loadFile(n,function(e,r){if(e){return t.complete(e)}var s=a(r,n,t);t.patched(n,s,function(e){if(e){return t.complete(e)}i()})})}i()}},{"../util/distance-iterator":38,"./parse":37}],36:[function(e,t,r){"use strict";r.__esModule=true;r.structuredPatch=s;r.createTwoFilesPatch=o;r.createPatch=a;var n=e("../diff/line");function i(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++){r[t]=e[t]}return r}else{return Array.from(e)}}function s(e,t,r,s,o,a,l){if(!l){l={context:4}}var u=(0,n.diffLines)(r,s);u.push({value:"",lines:[]});function c(e){return e.map(function(e){return" "+e})}var f=[];var p=0,h=0,d=[],v=1,y=1;var m=function e(t){var n=u[t],o=n.lines||n.value.replace(/\n$/,"").split("\n");n.lines=o;if(n.added||n.removed){var a;if(!p){var m=u[t-1];p=v;h=y;if(m){d=l.context>0?c(m.lines.slice(-l.context)):[];p-=d.length;h-=d.length}}(a=d).push.apply(a,i(o.map(function(e){return(n.added?"+":"-")+e})));if(n.added){y+=o.length}else{v+=o.length}}else{if(p){if(o.length<=l.context*2&&t<u.length-2){var g;(g=d).push.apply(g,i(c(o)))}else{var b;var x=Math.min(o.length,l.context);(b=d).push.apply(b,i(c(o.slice(0,x))));var w={oldStart:p,oldLines:v-p+x,newStart:h,newLines:y-h+x,lines:d};if(t>=u.length-2&&o.length<=l.context){var k=/\n$/.test(r);var _=/\n$/.test(s);if(o.length==0&&!k){d.splice(w.oldLines,0,"\\ No newline at end of file")}else if(!k||!_){d.push("\\ No newline at end of file")}}f.push(w);p=0;h=0;d=[]}}v+=o.length;y+=o.length}};for(var g=0;g<u.length;g++){m(g)}return{oldFileName:e,newFileName:t,oldHeader:o,newHeader:a,hunks:f}}function o(e,t,r,n,i,o,a){var l=s(e,t,r,n,i,o,a);var u=[];if(e==t){u.push("Index: "+e)}u.push("===================================================================");u.push("--- "+l.oldFileName+(typeof l.oldHeader==="undefined"?"":"\t"+l.oldHeader));u.push("+++ "+l.newFileName+(typeof l.newHeader==="undefined"?"":"\t"+l.newHeader));for(var c=0;c<l.hunks.length;c++){var f=l.hunks[c];u.push("@@ -"+f.oldStart+","+f.oldLines+" +"+f.newStart+","+f.newLines+" @@");u.push.apply(u,f.lines)}return u.join("\n")+"\n"}function a(e,t,r,n,i,s){return o(e,e,t,r,n,i,s)}},{"../diff/line":31}],37:[function(e,t,r){"use strict";r.__esModule=true;r.parsePatch=n;function n(e){var t=arguments.length<=1||arguments[1]===undefined?{}:arguments[1];var r=e.split("\n"),n=[],i=0;function s(){var e={};n.push(e);while(i<r.length){var s=r[i];if(/^(\-\-\-|\+\+\+|@@)\s/.test(s)){break}var l=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(s);if(l){e.index=l[1]}i++}o(e);o(e);e.hunks=[];while(i<r.length){var u=r[i];if(/^(Index:|diff|\-\-\-|\+\+\+)\s/.test(u)){break}else if(/^@@/.test(u)){e.hunks.push(a())}else if(u&&t.strict){throw new Error("Unknown line "+(i+1)+" "+JSON.stringify(u))}else{i++}}}function o(e){var t=/^(---|\+\+\+)\s+([\S ]*)(?:\t(.*?)\s*)?$/;var n=t.exec(r[i]);if(n){var s=n[1]==="---"?"old":"new";e[s+"FileName"]=n[2];e[s+"Header"]=n[3];i++}}function a(){var e=i,n=r[i++],s=n.split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/);var o={oldStart:+s[1],oldLines:+s[2]||1,newStart:+s[3],newLines:+s[4]||1,lines:[]};var a=0,l=0;for(;i<r.length;i++){var u=r[i][0];if(u==="+"||u==="-"||u===" "||u==="\\"){o.lines.push(r[i]);if(u==="+"){a++}else if(u==="-"){l++}else if(u===" "){a++;l++}}else{break}}if(!a&&o.newLines===1){o.newLines=0}if(!l&&o.oldLines===1){o.oldLines=0}if(t.strict){if(a!==o.newLines){throw new Error("Added line count did not match for hunk at line "+(e+1))}if(l!==o.oldLines){throw new Error("Removed line count did not match for hunk at line "+(e+1))}}return o}while(i<r.length){s()}return n}},{}],38:[function(e,t,r){"use strict";r.__esModule=true;r.default=function(e,t,r){var n=true,i=false,s=false,o=1;return function a(){if(n&&!s){if(i){o++}else{n=false}if(e+o<=r){return o}s=true}if(!i){if(!s){n=true}if(t<=e-o){return-o++}i=true;return a()}}}},{}],39:[function(e,t,r){"use strict";r.__esModule=true;r.generateOptions=n;function n(e,t){if(typeof e==="function"){t.callback=e}else if(e){for(var r in e){if(e.hasOwnProperty(r)){t[r]=e[r]}}}return t}},{}],40:[function(e,t,r){var n={};t.exports=n;n.svg=e("./svg.js");n.ready=function e(t){document.addEventListener("DOMContentLoaded",function e(){document.removeEventListener("DOMContentLoaded",e,false);t()},false)};n.fromHtml=function e(t){var r,n,i;i=document.createDocumentFragment();n=document.createElement("div");n.insertAdjacentHTML("beforeend",t);for(r=0;r<n.children.length;r++){i.appendChild(n.children[r])}return i};n.batch=function e(t,r){var n,i=Array.prototype.slice.call(arguments,1);if(r instanceof Element){i[0]=r;t.apply(this,i)}else if(Array.isArray(r)){for(n=0;n<r.length;n++){i[0]=r[n];t.apply(this,i)}}else if(r instanceof NodeList||r instanceof NamedNodeMap){for(n=0;n<r.length;n++){i[0]=r[n];t.apply(this,i)}}};n.css=function e(t,r){var n;for(n in r){t.style[n]=r[n]}};n.attr=function e(t,r){var n;for(n in r){if(r[n]===null){t.removeAttribute(n)}else{t.setAttribute(n,r[n])}}};n.remove=function e(t){t.parentNode.removeChild(t)};n.empty=function e(t){while(t.firstChild){t.removeChild(t.firstChild)}};n.cloneInto=function e(t,r){n.empty(t);t.appendChild(r.cloneNode(true))};n.insertInto=function e(t,r){n.empty(t);t.appendChild(r)};n.idNamespace=function e(t,r){var i,s={};i=t.querySelectorAll("*");n.batch(n.idNamespace.idAttributePass,i,r,s);n.batch(n.idNamespace.otherAttributesPass,i,s)};n.idNamespace.idAttributePass=function e(t,r,n){n[t.id]=r+"."+t.id;t.id=n[t.id]};n.idNamespace.otherAttributesPass=function e(t,r){n.batch(n.idNamespace.oneAttributeSubPass,t.attributes,r)};n.idNamespace.oneAttributeSubPass=function e(t,r){t.value=t.value.replace(/url\(#([^)]+)\)/g,function(e,t){if(!r[t]){return e}return"url(#"+r[t]+")"})};n.id=function e(t,e){t.id=e};n.text=function e(t,e){t.textContent=e};n.html=function e(t,e){t.innerHTML=e}},{"./svg.js":41}],41:[function(e,t,r){var n=e("fs");var i=e("./dom.js");var s={};t.exports=s;s.load=function e(t,r,i,o){if(typeof i==="function"){o=i}if(!i||typeof i!=="object"){i={}}if(r.substring(0,7)==="file://"){n.readFile(r.slice(7),function(e,r){if(e){o(e);return}var n=new DOMParser;var a=n.parseFromString(r.toString(),"application/xml").documentElement;try{s.attachXmlTo(t,a,i)}catch(e){o(e);return}o(undefined,a)})}else{s.ajax(r,function(e,r){var n=r.documentElement;if(e){o(e);return}try{s.attachXmlTo(t,n,i)}catch(e){o(e);return}o(undefined,n)})}};s.attachXmlTo=function e(t,r,n){s.lightCleanup(r);if(n.id!==undefined){if(typeof n.id==="string"){r.setAttribute("id",n.id)}else if(!n.id){r.removeAttribute("id")}}if(typeof n.class==="string"){r.setAttribute("class",n.class)}if(n.idNamespace){i.idNamespace(r,n.idNamespace)}if(n.hidden){r.style.visibility="hidden"}t.appendChild(r)};s.lightCleanup=function e(t){o(t,"metadata");o(t,"script")};function o(e,t){var r,n,i;n=e.getElementsByTagName(t);for(r=0;r<n.length;r++){i=n.item(r);i.parentNode.removeChild(i)}}s.ajax=function e(t,r){var n=new XMLHttpRequest;n.responseType="document";n.onreadystatechange=s.ajax.ajaxStatus.bind(n,r);n.open("GET",t);n.send()};s.ajax.ajaxStatus=function e(t){try{if(this.readyState===4){if(this.status===200){t(undefined,this.responseXML)}else if(this.status===0&&this.responseXML){t(undefined,this.responseXML)}else{if(this.status){t(this.status)}else{t(new Error("[dom-kit.svg] ajaxStatus(): Error with falsy status"))}}}}catch(e){t(e)}}},{"./dom.js":40,fs:15}],42:[function(e,t,r){(function(r){"use strict";function n(){return Object.create(n.prototype)}t.exports=n;n.prototype.__prototypeUID__="nextgen-events/NextGenEvents";n.prototype.__prototypeVersion__=e("../package.json").version;n.SYNC=-Infinity;n.init=function e(){Object.defineProperty(this,"__ngev",{configurable:true,value:{nice:n.SYNC,interruptible:false,recursion:0,contexts:{},states:{},stateGroups:{},listeners:{error:[],interrupt:[],newListener:[],removeListener:[]}}})};n.filterOutCallback=function(e,t){return e!==t};n.prototype.addListener=function e(t,r,i){var s={},o;if(!this.__ngev){n.init.call(this)}if(!this.__ngev.listeners[t]){this.__ngev.listeners[t]=[]}if(!t||typeof t!=="string"){throw new TypeError(".addListener(): argument #0 should be a non-empty string")}if(typeof r!=="function"){i=r;r=undefined}if(!i||typeof i!=="object"){i={}}s.fn=r||i.fn;s.id=i.id!==undefined?i.id:s.fn;s.once=!!i.once;s.async=!!i.async;s.eventObject=!!i.eventObject;s.nice=i.nice!==undefined?Math.floor(i.nice):n.SYNC;s.context=typeof i.context==="string"?i.context:null;if(typeof s.fn!=="function"){throw new TypeError(".addListener(): a function or an object with a 'fn' property which value is a function should be provided")}if(s.context&&typeof s.context==="string"&&!this.__ngev.contexts[s.context]){this.addListenerContext(s.context)}s.event=t;if(this.__ngev.listeners.newListener.length){o=this.__ngev.listeners.newListener.slice();this.__ngev.listeners[t].push(s);n.emitEvent({emitter:this,name:"newListener",args:[[s]],listeners:o});if(this.__ngev.states[t]){n.emitToOneListener(this.__ngev.states[t],s)}return this}this.__ngev.listeners[t].push(s);if(this.__ngev.states[t]){n.emitToOneListener(this.__ngev.states[t],s)}return this};n.prototype.on=n.prototype.addListener;n.prototype.once=function e(t,r,n){if(r&&typeof r==="object"){r.once=true}else if(n&&typeof n==="object"){n.once=true}else{n={once:true}}return this.addListener(t,r,n)};n.prototype.removeListener=function e(t,r){
var i,s,o=[],a=[];if(!t||typeof t!=="string"){throw new TypeError(".removeListener(): argument #0 should be a non-empty string")}if(!this.__ngev){n.init.call(this)}if(!this.__ngev.listeners[t]){this.__ngev.listeners[t]=[]}s=this.__ngev.listeners[t].length;for(i=0;i<s;i++){if(this.__ngev.listeners[t][i].id===r){a.push(this.__ngev.listeners[t][i])}else{o.push(this.__ngev.listeners[t][i])}}this.__ngev.listeners[t]=o;if(a.length&&this.__ngev.listeners.removeListener.length){this.emit("removeListener",a)}return this};n.prototype.off=n.prototype.removeListener;n.prototype.removeAllListeners=function e(t){var r;if(!this.__ngev){n.init.call(this)}if(t){if(!t||typeof t!=="string"){throw new TypeError(".removeAllListeners(): argument #0 should be undefined or a non-empty string")}if(!this.__ngev.listeners[t]){this.__ngev.listeners[t]=[]}r=this.__ngev.listeners[t];this.__ngev.listeners[t]=[];if(r.length&&this.__ngev.listeners.removeListener.length){this.emit("removeListener",r)}}else{this.__ngev.listeners={}}return this};n.listenerWrapper=function e(t,r,i){var s,o,a;if(r.interrupt){return}if(t.async){if(i){o=i.serial;i.ready=!o}a=function(e){r.listenersDone++;if(e&&r.emitter.__ngev.interruptible&&!r.interrupt&&r.name!=="interrupt"){r.interrupt=e;if(r.callback){r.callback(r.interrupt,r);delete r.callback}r.emitter.emit("interrupt",r.interrupt)}else if(r.listenersDone>=r.listeners.length&&r.callback){r.callback(undefined,r);delete r.callback}if(o){n.processQueue.call(r.emitter,t.context,true)}};if(t.eventObject){t.fn(r,a)}else{s=t.fn.apply(undefined,r.args.concat(a))}}else{if(t.eventObject){t.fn(r)}else{s=t.fn.apply(undefined,r.args)}r.listenersDone++}if(s&&r.emitter.__ngev.interruptible&&!r.interrupt&&r.name!=="interrupt"){r.interrupt=s;if(r.callback){r.callback(r.interrupt,r);delete r.callback}r.emitter.emit("interrupt",r.interrupt)}else if(r.listenersDone>=r.listeners.length&&r.callback){r.callback(undefined,r);delete r.callback}};var i=0;n.prototype.emit=function e(){var t;t={emitter:this};if(typeof arguments[0]==="number"){t.nice=Math.floor(arguments[0]);t.name=arguments[1];if(!t.name||typeof t.name!=="string"){throw new TypeError(".emit(): when argument #0 is a number, argument #1 should be a non-empty string")}if(typeof arguments[arguments.length-1]==="function"){t.callback=arguments[arguments.length-1];t.args=Array.prototype.slice.call(arguments,2,-1)}else{t.args=Array.prototype.slice.call(arguments,2)}}else{t.name=arguments[0];if(!t.name||typeof t.name!=="string"){throw new TypeError(".emit(): argument #0 should be an number or a non-empty string")}t.args=Array.prototype.slice.call(arguments,1);if(typeof arguments[arguments.length-1]==="function"){t.callback=arguments[arguments.length-1];t.args=Array.prototype.slice.call(arguments,1,-1)}else{t.args=Array.prototype.slice.call(arguments,1)}}return n.emitEvent(t)};n.emitEvent=function e(t){var r=t.emitter,s,o,a=0,l,u;if(!r.__ngev){n.init.call(r)}l=r.__ngev.states[t.name];if(l!==undefined){if(l&&t.args.length===l.args.length&&t.args.every(function(e,t){return e===l.args[t]})){return}r.__ngev.stateGroups[t.name].forEach(function(e){r.__ngev.states[e]=null});r.__ngev.states[t.name]=t}if(!r.__ngev.listeners[t.name]){r.__ngev.listeners[t.name]=[]}t.id=i++;t.listenersDone=0;t.once=!!t.once;if(t.nice===undefined||t.nice===null){t.nice=r.__ngev.nice}if(!t.listeners){t.listeners=r.__ngev.listeners[t.name].slice()}r.__ngev.recursion++;u=[];for(s=0,o=t.listeners.length;s<o;s++){a++;n.emitToOneListener(t,t.listeners[s],u)}r.__ngev.recursion--;if(u.length&&r.__ngev.listeners.removeListener.length){r.emit("removeListener",u)}if(!a){if(t.name==="error"){if(t.args[0]){throw t.args[0]}else{throw Error("Uncaught, unspecified 'error' event.")}}if(t.callback){t.callback(undefined,t);delete t.callback}}return t};n.emitToOneListener=function e(t,r,i){var s=t.emitter,o,a,l=false;o=r.context&&s.__ngev.contexts[r.context];if(o&&o.status===n.CONTEXT_DISABLED){return}if(o){a=Math.max(t.nice,r.nice,o.nice)}else{a=Math.max(t.nice,r.nice)}if(r.once){s.__ngev.listeners[t.name]=s.__ngev.listeners[t.name].filter(n.filterOutCallback.bind(undefined,r));if(i){i.push(r)}else{l=true}}if(o&&(o.status===n.CONTEXT_QUEUED||!o.ready)){o.queue.push({event:t,listener:r,nice:a})}else{try{if(a<0){if(s.__ngev.recursion>=-a){setImmediate(n.listenerWrapper.bind(s,r,t,o))}else{n.listenerWrapper.call(s,r,t,o)}}else{setTimeout(n.listenerWrapper.bind(s,r,t,o),a)}}catch(e){s.__ngev.recursion--;throw e}}if(l&&s.__ngev.listeners.removeListener.length){s.emit("removeListener",[r])}};n.prototype.listeners=function e(t){if(!t||typeof t!=="string"){throw new TypeError(".listeners(): argument #0 should be a non-empty string")}if(!this.__ngev){n.init.call(this)}if(!this.__ngev.listeners[t]){this.__ngev.listeners[t]=[]}return this.__ngev.listeners[t].slice()};n.listenerCount=function(e,t){if(!e||!(e instanceof n)){throw new TypeError(".listenerCount(): argument #0 should be an instance of NextGenEvents")}return e.listenerCount(t)};n.prototype.listenerCount=function(e){if(!e||typeof e!=="string"){throw new TypeError(".listenerCount(): argument #1 should be a non-empty string")}if(!this.__ngev){n.init.call(this)}if(!this.__ngev.listeners[e]){this.__ngev.listeners[e]=[]}return this.__ngev.listeners[e].length};n.prototype.setNice=function e(t){if(!this.__ngev){n.init.call(this)}this.__ngev.nice=Math.floor(+t||0)};n.prototype.setInterruptible=function e(t){if(!this.__ngev){n.init.call(this)}this.__ngev.interruptible=!!t};n.share=function(e,t){if(!(e instanceof n)||!(t instanceof n)){throw new TypeError("NextGenEvents.share() arguments should be instances of NextGenEvents")}if(!e.__ngev){n.init.call(e)}Object.defineProperty(t,"__ngev",{configurable:true,value:e.__ngev})};n.reset=function e(t){Object.defineProperty(t,"__ngev",{configurable:true,value:null})};n.prototype.setMaxListeners=function(){};n.noop=function(){};n.prototype.defineStates=function e(){var t=this,r=Array.prototype.slice.call(arguments);if(!this.__ngev){n.init.call(this)}r.forEach(function(e){t.__ngev.states[e]=null;t.__ngev.stateGroups[e]=r})};n.prototype.hasState=function e(t){if(!this.__ngev){n.init.call(this)}return!!this.__ngev.states[t]};n.prototype.getAllStates=function e(){var t=this;if(!this.__ngev){n.init.call(this)}return Object.keys(this.__ngev.states).filter(function(e){return t.__ngev.states[e]})};n.groupAddListener=function e(t,r,n,i){if(typeof n!=="function"){i=n;n=undefined}if(!i||typeof i!=="object"){i={}}n=n||i.fn;delete i.fn;i.id=i.id||n;t.forEach(function(e){e.addListener(r,n.bind(undefined,e),i)})};n.groupOn=n.groupAddListener;n.groupOnce=function e(t,r,n,i){if(n&&typeof n==="object"){n.once=true}else if(i&&typeof i==="object"){i.once=true}else{i={once:true}}return this.groupAddListener(t,r,n,i)};n.groupGlobalOnce=function e(t,r,i,s){var o,a=false;if(typeof i!=="function"){s=i;i=undefined}if(!s||typeof s!=="object"){s={}}i=i||s.fn;delete s.fn;s.id=s.id||i;o=function(){if(a){return}a=true;n.groupRemoveListener(t,r,s.id);i.apply(undefined,arguments)};t.forEach(function(e){e.once(r,o.bind(undefined,e),s)})};n.groupGlobalOnceAll=function e(t,r,n,i){var s,o=false,a=t.length;if(typeof n!=="function"){i=n;n=undefined}if(!i||typeof i!=="object"){i={}}n=n||i.fn;delete i.fn;i.id=i.id||n;s=function(){if(o){return}if(--a){return}o=true;n.apply(undefined,arguments)};t.forEach(function(e){e.once(r,s.bind(undefined,e),i)})};n.groupRemoveListener=function e(t,r,n){t.forEach(function(e){e.removeListener(r,n)})};n.groupOff=n.groupRemoveListener;n.groupRemoveAllListeners=function e(t,r){t.forEach(function(e){e.removeAllListeners(r)})};n.groupEmit=function e(t){var r,i,s=2,o,a,l=t.length,u,c,f=false;if(typeof arguments[arguments.length-1]==="function"){o=-1;u=arguments[arguments.length-1];c=function(e){if(f){return}if(e){f=true;u(e)}else if(!--l){f=true;u()}}}if(typeof arguments[1]==="number"){s=3;i=typeof arguments[1]}r=arguments[s-1];a=Array.prototype.slice.call(arguments,s,o);t.forEach(function(e){n.emitEvent({emitter:e,name:r,args:a,nice:i,callback:c})})};n.groupDefineStates=function e(t){var r=Array.prototype.slice.call(arguments,1);t.forEach(function(e){e.defineStates.apply(e,r)})};n.CONTEXT_ENABLED=0;n.CONTEXT_DISABLED=1;n.CONTEXT_QUEUED=2;n.prototype.addListenerContext=function e(t,r){if(!this.__ngev){n.init.call(this)}if(!t||typeof t!=="string"){throw new TypeError(".addListenerContext(): argument #0 should be a non-empty string")}if(!r||typeof r!=="object"){r={}}if(!this.__ngev.contexts[t]){this.__ngev.contexts[t]=Object.create(n.prototype);this.__ngev.contexts[t].nice=n.SYNC;this.__ngev.contexts[t].ready=true;this.__ngev.contexts[t].status=n.CONTEXT_ENABLED;this.__ngev.contexts[t].serial=false;this.__ngev.contexts[t].queue=[]}if(r.nice!==undefined){this.__ngev.contexts[t].nice=Math.floor(r.nice)}if(r.status!==undefined){this.__ngev.contexts[t].status=r.status}if(r.serial!==undefined){this.__ngev.contexts[t].serial=!!r.serial}return this};n.prototype.disableListenerContext=function e(t){if(!this.__ngev){n.init.call(this)}if(!t||typeof t!=="string"){throw new TypeError(".disableListenerContext(): argument #0 should be a non-empty string")}if(!this.__ngev.contexts[t]){this.addListenerContext(t)}this.__ngev.contexts[t].status=n.CONTEXT_DISABLED;return this};n.prototype.enableListenerContext=function e(t){if(!this.__ngev){n.init.call(this)}if(!t||typeof t!=="string"){throw new TypeError(".enableListenerContext(): argument #0 should be a non-empty string")}if(!this.__ngev.contexts[t]){this.addListenerContext(t)}this.__ngev.contexts[t].status=n.CONTEXT_ENABLED;if(this.__ngev.contexts[t].queue.length>0){n.processQueue.call(this,t)}return this};n.prototype.queueListenerContext=function e(t){if(!this.__ngev){n.init.call(this)}if(!t||typeof t!=="string"){throw new TypeError(".queueListenerContext(): argument #0 should be a non-empty string")}if(!this.__ngev.contexts[t]){this.addListenerContext(t)}this.__ngev.contexts[t].status=n.CONTEXT_QUEUED;return this};n.prototype.serializeListenerContext=function e(t,r){if(!this.__ngev){n.init.call(this)}if(!t||typeof t!=="string"){throw new TypeError(".serializeListenerContext(): argument #0 should be a non-empty string")}if(!this.__ngev.contexts[t]){this.addListenerContext(t)}this.__ngev.contexts[t].serial=r===undefined?true:!!r;return this};n.prototype.setListenerContextNice=function e(t,r){if(!this.__ngev){n.init.call(this)}if(!t||typeof t!=="string"){throw new TypeError(".setListenerContextNice(): argument #0 should be a non-empty string")}if(!this.__ngev.contexts[t]){this.addListenerContext(t)}this.__ngev.contexts[t].nice=Math.floor(r);return this};n.prototype.destroyListenerContext=function e(t){var r,i,s,o,a=[];if(!t||typeof t!=="string"){throw new TypeError(".disableListenerContext(): argument #0 should be a non-empty string")}if(!this.__ngev){n.init.call(this)}for(s in this.__ngev.listeners){o=null;i=this.__ngev.listeners[s].length;for(r=0;r<i;r++){if(this.__ngev.listeners[s][r].context===t){o=[];a.push(this.__ngev.listeners[s][r])}else if(o){o.push(this.__ngev.listeners[s][r])}}if(o){this.__ngev.listeners[s]=o}}if(this.__ngev.contexts[t]){delete this.__ngev.contexts[t]}if(a.length&&this.__ngev.listeners.removeListener.length){this.emit("removeListener",a)}return this};n.processQueue=function e(t,r){var i,s;if(!this.__ngev.contexts[t]){return}i=this.__ngev.contexts[t];if(r){i.ready=true}this.__ngev.recursion++;while(i.ready&&i.queue.length){s=i.queue.shift();if(s.event.interrupt){continue}try{if(s.nice<0){if(this.__ngev.recursion>=-s.nice){setImmediate(n.listenerWrapper.bind(this,s.listener,s.event,i))}else{n.listenerWrapper.call(this,s.listener,s.event,i)}}else{setTimeout(n.listenerWrapper.bind(this,s.listener,s.event,i),s.nice)}}catch(e){this.__ngev.recursion--;throw e}}this.__ngev.recursion--};n.on=n.prototype.on;n.once=n.prototype.once;n.off=n.prototype.off;if(r.AsyncTryCatch){n.prototype.asyncTryCatchId=r.AsyncTryCatch.NextGenEvents.length;r.AsyncTryCatch.NextGenEvents.push(n);if(r.AsyncTryCatch.substituted){r.AsyncTryCatch.substitute()}}n.Proxy=e("./Proxy.js")}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"../package.json":44,"./Proxy.js":43}],43:[function(e,t,r){"use strict";function n(){return n.create()}t.exports=n;var i=e("./NextGenEvents.js");var s="NextGenEvents/message";function o(){}n.create=function e(){var t=Object.create(n.prototype,{localServices:{value:{},enumerable:true},remoteServices:{value:{},enumerable:true},nextAckId:{value:1,writable:true,enumerable:true}});return t};n.prototype.addLocalService=function e(t,r,n){this.localServices[t]=a.create(this,t,r,n);return this.localServices[t]};n.prototype.addRemoteService=function e(t){this.remoteServices[t]=l.create(this,t);return this.remoteServices[t]};n.prototype.destroy=function e(){var t=this;Object.keys(this.localServices).forEach(function(e){t.localServices[e].destroy();delete t.localServices[e]});Object.keys(this.remoteServices).forEach(function(e){t.remoteServices[e].destroy();delete t.remoteServices[e]});this.receive=this.send=o};n.prototype.push=function e(t){if(t.__type!==s||!t.service||typeof t.service!=="string"||!t.event||typeof t.event!=="string"||!t.method){return}switch(t.method){case"event":return this.remoteServices[t.service]&&this.remoteServices[t.service].receiveEvent(t);case"ackEmit":return this.remoteServices[t.service]&&this.remoteServices[t.service].receiveAckEmit(t);case"emit":return this.localServices[t.service]&&this.localServices[t.service].receiveEmit(t);case"listen":return this.localServices[t.service]&&this.localServices[t.service].receiveListen(t);case"ignore":return this.localServices[t.service]&&this.localServices[t.service].receiveIgnore(t);case"ackEvent":return this.localServices[t.service]&&this.localServices[t.service].receiveAckEvent(t);default:return}};n.prototype.receive=function e(t){this.push(t)};n.prototype.send=function e(){throw new Error("The send() method of the Proxy MUST be extended/overwritten")};function a(e,t,r,n){return a.create(e,t,r,n)}n.LocalService=a;a.create=function e(t,r,n,s){var o=Object.create(a.prototype,{proxy:{value:t,enumerable:true},id:{value:r,enumerable:true},emitter:{value:n,writable:true,enumerable:true},internalEvents:{value:Object.create(i.prototype),writable:true,enumerable:true},events:{value:{},enumerable:true},canListen:{value:!!s.listen,writable:true,enumerable:true},canEmit:{value:!!s.emit,writable:true,enumerable:true},canAck:{value:!!s.ack,writable:true,enumerable:true},canRpc:{value:!!s.rpc,writable:true,enumerable:true},destroyed:{value:false,writable:true,enumerable:true}});return o};a.prototype.destroy=function e(){var t=this;Object.keys(this.events).forEach(function(e){t.emitter.off(e,t.events[e]);delete t.events[e]});this.emitter=null;this.destroyed=true};a.prototype.receiveEmit=function e(t){if(this.destroyed||!this.canEmit||t.ack&&!this.canAck){return}var r=this;var n={emitter:this.emitter,name:t.event,args:t.args||[]};if(t.ack){n.callback=function e(n){r.proxy.send({__type:s,service:r.id,method:"ackEmit",ack:t.ack,event:t.event,interruption:n})}}i.emitEvent(n)};a.prototype.receiveListen=function e(t){if(this.destroyed||!this.canListen||t.ack&&!this.canAck){return}if(t.ack){if(this.events[t.event]){if(this.events[t.event].ack){return}this.emitter.off(t.event,this.events[t.event])}this.events[t.event]=a.forwardWithAck.bind(this);this.events[t.event].ack=true;this.emitter.on(t.event,this.events[t.event],{eventObject:true,async:true})}else{if(this.events[t.event]){if(!this.events[t.event].ack){return}this.emitter.off(t.event,this.events[t.event])}this.events[t.event]=a.forward.bind(this);this.events[t.event].ack=false;this.emitter.on(t.event,this.events[t.event],{eventObject:true})}};a.prototype.receiveIgnore=function e(t){if(this.destroyed||!this.canListen){return}if(!this.events[t.event]){return}this.emitter.off(t.event,this.events[t.event]);this.events[t.event]=null};a.prototype.receiveAckEvent=function e(t){if(this.destroyed||!this.canListen||!this.canAck||!t.ack||!this.events[t.event]||!this.events[t.event].ack){return}this.internalEvents.emit("ack",t)};a.forward=function e(t){if(this.destroyed){return}this.proxy.send({__type:s,service:this.id,method:"event",event:t.name,args:t.args})};a.forward.ack=false;a.forwardWithAck=function e(t,r){if(this.destroyed){return}var n=this;if(!t.callback){this.proxy.send({__type:s,service:this.id,method:"event",event:t.name,args:t.args});r();return}var i=false;var o=this.proxy.nextAckId++;var a=function e(t){if(i||t.ack!==o){return}i=true;n.internalEvents.off("ack",e);r()};this.internalEvents.on("ack",a);this.proxy.send({__type:s,service:this.id,method:"event",event:t.name,ack:o,args:t.args})};a.forwardWithAck.ack=true;function l(e,t){return l.create(e,t)}n.RemoteService=l;var u=1;var c=2;l.create=function e(t,r){var n=Object.create(l.prototype,{proxy:{value:t,enumerable:true},id:{value:r,enumerable:true},emitter:{value:Object.create(i.prototype),writable:true,enumerable:true},internalEvents:{value:Object.create(i.prototype),writable:true,enumerable:true},events:{value:{},enumerable:true},destroyed:{value:false,writable:true,enumerable:true}});return n};l.prototype.destroy=function e(){var t=this;this.emitter.removeAllListeners();this.emitter=null;Object.keys(this.events).forEach(function(e){delete t.events[e]});this.destroyed=true};l.prototype.emit=function e(t){if(this.destroyed){return}var r=this,n,i,o,a;if(typeof t==="number"){throw new TypeError("Cannot emit with a nice value on a remote service")}if(typeof arguments[arguments.length-1]!=="function"){n=Array.prototype.slice.call(arguments,1);this.proxy.send({__type:s,service:this.id,method:"emit",event:t,args:n});return}n=Array.prototype.slice.call(arguments,1,-1);i=arguments[arguments.length-1];o=this.proxy.nextAckId++;a=false;var l=function e(t){if(a||t.ack!==o){return}a=true;r.internalEvents.off("ack",e);i(t.interruption)};this.internalEvents.on("ack",l);this.proxy.send({__type:s,service:this.id,method:"emit",ack:o,event:t,args:n})};l.prototype.addListener=function e(t,r,n){if(this.destroyed){return}if(typeof r!=="function"){n=r;r=undefined}if(!n||typeof n!=="object"){n={}}n.fn=r||n.fn;this.emitter.addListener(t,n);if(!this.emitter.__ngev.listeners[t]||!this.emitter.__ngev.listeners[t].length){return}if(n.async&&this.events[t]!==c){this.events[t]=c;this.proxy.send({__type:s,service:this.id,method:"listen",ack:true,event:t})}else if(!n.async&&!this.events[t]){this.events[t]=u;this.proxy.send({__type:s,service:this.id,method:"listen",event:t})}};l.prototype.on=l.prototype.addListener;l.prototype.once=i.prototype.once;l.prototype.removeListener=function e(t,r){if(this.destroyed){return}this.emitter.removeListener(t,r);if((!this.emitter.__ngev.listeners[t]||!this.emitter.__ngev.listeners[t].length)&&this.events[t]){this.events[t]=0;this.proxy.send({__type:s,service:this.id,method:"ignore",event:t})}};l.prototype.off=l.prototype.removeListener;l.prototype.receiveEvent=function e(t){var r=this;if(this.destroyed||!this.events[t.event]){return}var n={emitter:this.emitter,name:t.event,args:t.args||[]};if(t.ack){n.callback=function e(){r.proxy.send({__type:s,service:r.id,method:"ackEvent",ack:t.ack,event:t.event})}}i.emitEvent(n);var o=n.name;if(!this.emitter.__ngev.listeners[o]||!this.emitter.__ngev.listeners[o].length){this.events[o]=0;this.proxy.send({__type:s,service:this.id,method:"ignore",event:o})}};l.prototype.receiveAckEmit=function e(t){if(this.destroyed||!t.ack||this.events[t.event]!==c){return}this.internalEvents.emit("ack",t)}},{"./NextGenEvents.js":42}],44:[function(e,t,r){t.exports={name:"nextgen-events",version:"0.9.7",description:"The next generation of events handling for javascript! New: abstract away the network!",main:"lib/NextGenEvents.js",directories:{test:"test"},dependencies:{},devDependencies:{browserify:"^13.0.1","expect.js":"^0.3.1",jshint:"^2.9.2",mocha:"^2.5.3","uglify-js":"^2.6.2",ws:"^1.1.1"},scripts:{test:"mocha -R dot"},repository:{type:"git",url:"git+https://github.com/cronvel/nextgen-events.git"},keywords:["events","async","emit","listener","context","series","serialize","namespace","proxy","network"],author:{name:"Cédric Ronvel"},license:"MIT",bugs:{url:"https://github.com/cronvel/nextgen-events/issues"},copyright:{title:"Next-Gen Events",years:[2015,2016],owner:"Cédric Ronvel"},gitHead:"2175ae73d8dd5d66a6f7429e51a54de3117f9aa3",homepage:"https://github.com/cronvel/nextgen-events#readme",_id:"nextgen-events@0.9.7",_shasum:"4029428094d6e5fa7f33cb91c232c316468cd614",_from:"nextgen-events@0.9.7",_npmVersion:"2.15.9",_nodeVersion:"4.5.0",_npmUser:{name:"cronvel",email:"<EMAIL>"},maintainers:[{name:"cronvel",email:"<EMAIL>"}],dist:{shasum:"4029428094d6e5fa7f33cb91c232c316468cd614",tarball:"https://registry.npmjs.org/nextgen-events/-/nextgen-events-0.9.7.tgz"},_npmOperationalInternal:{host:"packages-12-west.internal.npmjs.com",tmp:"tmp/nextgen-events-0.9.7.tgz_1473940784348_0.5092023052275181"},_resolved:"https://registry.npmjs.org/nextgen-events/-/nextgen-events-0.9.7.tgz",readme:"ERROR: No README data found!"}},{}],45:[function(e,t,r){"use strict";t.exports={reset:"[0m",bold:"[1m",dim:"[2m",italic:"[3m",underline:"[4m",inverse:"[7m",defaultColor:"[39m",black:"[30m",red:"[31m",green:"[32m",yellow:"[33m",blue:"[34m",magenta:"[35m",cyan:"[36m",white:"[37m",brightBlack:"[90m",brightRed:"[91m",brightGreen:"[92m",brightYellow:"[93m",brightBlue:"[94m",brightMagenta:"[95m",brightCyan:"[96m",brightWhite:"[97m"}},{}],46:[function(e,t,r){"use strict";r.regExp=r.regExpPattern=function e(t){return t.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1")};r.regExpReplacement=function e(t){return t.replace(/\$/g,"$$$$")};r.format=function e(t){return t.replace(/%/g,"%%")};r.shellArg=function e(t){return"'"+t.replace(/\'/g,"'\\''")+"'"};var n={"\r":"\\r","\n":"\\n","\t":"\\t","":"\\x7f"};r.control=function e(t){return t.replace(/[\x00-\x1f\x7f]/g,function(e){if(n[e]!==undefined){return n[e]}var t=e.charCodeAt(0).toString(16);if(t.length%2){t="0"+t}return"\\x"+t})};var i={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#039;"};r.html=function e(t){return t.replace(/[&<>]/g,function(e){return i[e]})};r.htmlAttr=function e(t){return t.replace(/[&<>"]/g,function(e){return i[e]})};r.htmlSpecialChars=function e(t){return t.replace(/[&<>"']/g,function(e){return i[e]})}},{}],47:[function(e,t,r){(function(t,n){"use strict";var i=e("tree-kit/lib/extend.js");var s=e("./escape.js");var o=e("./ansi.js");function a(e,t){if(arguments.length<2){t=e;e={}}else if(!e||typeof e!=="object"){e={}}var r={depth:0,ancestors:[]};if(!e.style){e.style=h.none}else if(typeof e.style==="string"){e.style=h[e.style]}if(e.depth===undefined){e.depth=3}if(e.nofunc){e.noFunc=true}if(e.minimal){e.noFunc=true;e.noDescriptor=true;e.noType=true;e.enumOnly=true;e.funcDetails=false;e.proto=false}return l(r,e,t)}function l(e,r,n){var i,o,a,f,p,h,d,v,y,m,g,b,x="",w="",k="",_,T;d=typeof n;y=r.style.tab.repeat(e.depth);if(d==="function"&&r.noFunc){return""}if(e.key!==undefined){if(e.descriptor){k=[];if(!e.descriptor.configurable){k.push("-conf")}if(!e.descriptor.enumerable){k.push("-enum")}if(!e.descriptor.writable){k.push("-w")}if(k.length){k=k.join(" ")}else{k=""}}if(e.keyIsProperty){if(u(e.key)){w='"'+r.style.key(e.key)+'": '}else{w=r.style.key(e.key)+": "}}else{w="["+r.style.index(e.key)+"] "}if(k){k=" "+r.style.type(k)}}v=e.noPre?"":y+w;if(n===undefined){x+=v+r.style.constant("undefined")+k+r.style.nl}else if(n===null){x+=v+r.style.constant("null")+k+r.style.nl}else if(n===false){x+=v+r.style.constant("false")+k+r.style.nl}else if(n===true){x+=v+r.style.constant("true")+k+r.style.nl}else if(d==="number"){x+=v+r.style.number(n.toString())+(r.noType?"":" "+r.style.type("number"))+k+r.style.nl}else if(d==="string"){x+=v+'"'+r.style.string(s.control(n))+'" '+(r.noType?"":r.style.type("string")+r.style.length("("+n.length+")"))+k+r.style.nl}else if(t.isBuffer(n)){x+=v+r.style.inspect(n.inspect())+" "+(r.noType?"":r.style.type("Buffer")+r.style.length("("+n.length+")"))+k+r.style.nl}else if(d==="object"||d==="function"){o=a="";g=false;if(d==="function"){g=true;o=" "+r.style.funcName(n.name?n.name:"(anonymous)");a=r.style.length("("+n.length+")")}m=false;if(Array.isArray(n)){m=true;a=r.style.length("("+n.length+")")}if(!n.constructor){p="(no constructor)"}else if(!n.constructor.name){p="(anonymous)"}else{p=n.constructor.name}p=r.style.constructorName(p);x+=v;if(!r.noType){if(e.forceType){x+=r.style.type(e.forceType)}else{x+=p+o+a+" "+r.style.type(d)+k}if(!g||r.funcDetails){x+=" "}}f=Object.getOwnPropertyNames(n);if(r.sort){f.sort()}b=c(n);if(b!==undefined){x+="=> "+l({depth:e.depth,ancestors:e.ancestors,noPre:true},r,b)}else if(g&&!r.funcDetails){x+=r.style.nl}else if(!f.length&&!r.proto){x+="{}"+r.style.nl}else if(e.depth>=r.depth){x+=r.style.limit("[depth limit]")+r.style.nl}else if(e.ancestors.indexOf(n)!==-1){x+=r.style.limit("[circular]")+r.style.nl}else{x+=(m&&r.noType?"[":"{")+r.style.nl;T=e.ancestors.slice();T.push(n);for(i=0;i<f.length;i++){try{_=Object.getOwnPropertyDescriptor(n,f[i]);if(!_.enumerable&&r.enumOnly){continue}h=!m||!_.enumerable||isNaN(f[i]);if(!r.noDescriptor&&(_.get||_.set)){x+=l({depth:e.depth+1,ancestors:T,key:f[i],keyIsProperty:h,descriptor:_,forceType:"getter/setter"},r,{get:_.get,set:_.set})}else{x+=l({depth:e.depth+1,ancestors:T,key:f[i],keyIsProperty:h,descriptor:r.noDescriptor?undefined:_},r,n[f[i]])}}catch(t){x+=l({depth:e.depth+1,ancestors:T,key:f[i],keyIsProperty:h,descriptor:r.noDescriptor?undefined:_},r,t)}}if(r.proto){x+=l({depth:e.depth+1,ancestors:T,key:"__proto__",keyIsProperty:true},r,n.__proto__)}x+=y+(m&&r.noType?"]":"}")+r.style.nl}}if(e.depth===0){if(r.style==="html"){x=s.html(x)}}return x}r.inspect=a;function u(e){if(!e.length){return true}return false}function c(e){switch(e.constructor.name){case"Date":if(e instanceof Date){return e.toString()+" ["+e.getTime()+"]"}break;case"Set":if(typeof Set==="function"&&e instanceof Set){return Array.from(e)}break;case"Map":if(typeof Map==="function"&&e instanceof Map){return Array.from(e)}break;case"ObjectID":if(e._bsontype){return e.toString()}break}return}function f(e,t){var r="",n,i,s;if(arguments.length<2){t=e;e={}}else if(!e||typeof e!=="object"){e={}}if(!(t instanceof Error)){return}if(!e.style){e.style=h.none}else if(typeof e.style==="string"){e.style=h[e.style]}if(t.stack){n=p(e,t.stack)}i=t.type||t.constructor.name;s=t.code||t.name||t.errno||t.number;r+=e.style.errorType(i)+(s?" ["+e.style.errorType(s)+"]":"")+": ";r+=e.style.errorMessage(t.message)+"\n";if(n){r+=e.style.errorStack(n)+"\n"}return r}r.inspectError=f;function p(e,t){if(arguments.length<2){t=e;e={}}else if(!e||typeof e!=="object"){e={}}if(!e.style){e.style=h.none}else if(typeof e.style==="string"){e.style=h[e.style]}if(!t){return}if((e.browser||n.browser)&&t.indexOf("@")!==-1){t=t.replace(/[<\/]*(?=@)/g,"").replace(/^\s*([^@]*)\s*@\s*([^\n]*)(?::([0-9]+):([0-9]+))?$/gm,function(t,r,n,i,s){return e.style.errorStack("    at ")+(r?e.style.errorStackMethod(r)+" ":"")+e.style.errorStack("(")+(n?e.style.errorStackFile(n):e.style.errorStack("unknown"))+(i?e.style.errorStack(":")+e.style.errorStackLine(i):"")+(s?e.style.errorStack(":")+e.style.errorStackColumn(s):"")+e.style.errorStack(")")})}else{t=t.replace(/^[^\n]*\n/,"");t=t.replace(/^\s*(at)\s+(?:([^\s:\(\)\[\]\n]+(?:\([^\)]+\))?)\s)?(?:\[as ([^\s:\(\)\[\]\n]+)\]\s)?(?:\(?([^:\(\)\[\]\n]+):([0-9]+):([0-9]+)\)?)?$/gm,function(t,r,n,i,s,o,a){return e.style.errorStack("    at ")+(n?e.style.errorStackMethod(n)+" ":"")+(i?e.style.errorStack("[as ")+e.style.errorStackMethodAs(i)+e.style.errorStack("] "):"")+e.style.errorStack("(")+(s?e.style.errorStackFile(s):e.style.errorStack("unknown"))+(o?e.style.errorStack(":")+e.style.errorStackLine(o):"")+(a?e.style.errorStack(":")+e.style.errorStackColumn(a):"")+e.style.errorStack(")")})}return t}r.inspectStack=p;var h={};var d=function(e){return e};h.none={tab:"    ",nl:"\n",limit:d,type:function(e){return"<"+e+">"},constant:d,funcName:d,constructorName:function(e){return"<"+e+">"},length:d,key:d,index:d,number:d,inspect:d,string:d,errorType:d,errorMessage:d,errorStack:d,errorStackMethod:d,errorStackMethodAs:d,errorStackFile:d,errorStackLine:d,errorStackColumn:d};h.color=i(null,{},h.none,{limit:function(e){return o.bold+o.brightRed+e+o.reset},type:function(e){return o.italic+o.brightBlack+e+o.reset},constant:function(e){return o.cyan+e+o.reset},funcName:function(e){return o.italic+o.magenta+e+o.reset},constructorName:function(e){return o.magenta+e+o.reset},length:function(e){return o.italic+o.brightBlack+e+o.reset},key:function(e){return o.green+e+o.reset},index:function(e){return o.blue+e+o.reset},number:function(e){return o.cyan+e+o.reset},inspect:function(e){return o.cyan+e+o.reset},string:function(e){return o.blue+e+o.reset},errorType:function(e){return o.red+o.bold+e+o.reset},errorMessage:function(e){return o.red+o.italic+e+o.reset},errorStack:function(e){return o.brightBlack+e+o.reset},errorStackMethod:function(e){return o.brightYellow+e+o.reset},errorStackMethodAs:function(e){return o.yellow+e+o.reset},errorStackFile:function(e){return o.brightCyan+e+o.reset},errorStackLine:function(e){return o.blue+e+o.reset},errorStackColumn:function(e){return o.magenta+e+o.reset}});h.html=i(null,{},h.none,{tab:"&nbsp;&nbsp;&nbsp;&nbsp;",nl:"<br />",limit:function(e){return'<span style="color:red">'+e+"</span>"},type:function(e){return'<i style="color:gray">'+e+"</i>"},constant:function(e){return'<span style="color:cyan">'+e+"</span>"},funcName:function(e){return'<i style="color:magenta">'+e+"</i>"},constructorName:function(e){return'<span style="color:magenta">'+e+"</span>"},length:function(e){return'<i style="color:gray">'+e+"</i>"},key:function(e){return'<span style="color:green">'+e+"</span>"},index:function(e){return'<span style="color:blue">'+e+"</span>"},number:function(e){return'<span style="color:cyan">'+e+"</span>"},inspect:function(e){return'<span style="color:cyan">'+e+"</span>"},string:function(e){return'<span style="color:blue">'+e+"</span>"},errorType:function(e){return'<span style="color:red">'+e+"</span>"},errorMessage:function(e){return'<span style="color:red">'+e+"</span>"},errorStack:function(e){return'<span style="color:gray">'+e+"</span>"},errorStackMethod:function(e){return'<span style="color:yellow">'+e+"</span>"},errorStackMethodAs:function(e){return'<span style="color:yellow">'+e+"</span>"},errorStackFile:function(e){return'<span style="color:cyan">'+e+"</span>"},errorStackLine:function(e){return'<span style="color:blue">'+e+"</span>"},errorStackColumn:function(e){return'<span style="color:gray">'+e+"</span>"}})}).call(this,{isBuffer:e("../../browserify/node_modules/insert-module-globals/node_modules/is-buffer/index.js")},e("_process"))},{"../../browserify/node_modules/insert-module-globals/node_modules/is-buffer/index.js":17,"./ansi.js":45,"./escape.js":46,_process:18,"tree-kit/lib/extend.js":48}],48:[function(e,t,r){"use strict";function n(e,t){var r,n,s=false,o=arguments.length;if(o<3){return t}var a=Array.prototype.slice.call(arguments,2);o=a.length;if(!e||typeof e!=="object"){e={}}var l={depth:0,prefix:""};if(!e.maxDepth&&e.deep&&!e.circular){e.maxDepth=100}if(e.deepFunc){e.deep=true}if(e.deepFilter&&typeof e.deepFilter==="object"){if(e.deepFilter.whitelist&&(!Array.isArray(e.deepFilter.whitelist)||!e.deepFilter.whitelist.length)){delete e.deepFilter.whitelist}if(e.deepFilter.blacklist&&(!Array.isArray(e.deepFilter.blacklist)||!e.deepFilter.blacklist.length)){delete e.deepFilter.blacklist}if(!e.deepFilter.whitelist&&!e.deepFilter.blacklist){delete e.deepFilter}}if(e.flat){e.deep=true;e.proto=false;e.inherit=false;e.unflat=false;if(typeof e.flat!=="string"){e.flat="."}}if(e.unflat){e.deep=false;e.proto=false;e.inherit=false;e.flat=false;if(typeof e.unflat!=="string"){e.unflat="."}}if(e.inherit){
e.own=true;e.proto=false}else if(e.proto){e.own=true}if(!t||typeof t!=="object"&&typeof t!=="function"){s=true}if(!e.skipRoot&&(e.inherit||e.proto)){for(r=o-1;r>=0;r--){n=a[r];if(n&&(typeof n==="object"||typeof n==="function")){if(e.inherit){if(s){t=Object.create(n)}else{Object.setPrototypeOf(t,n)}}else if(e.proto){if(s){t=Object.create(Object.getPrototypeOf(n))}else{Object.setPrototypeOf(t,Object.getPrototypeOf(n))}}break}}}else if(s){t={}}l.references={sources:[],targets:[]};for(r=0;r<o;r++){n=a[r];if(!n||typeof n!=="object"&&typeof n!=="function"){continue}i(l,e,t,n)}return t}t.exports=n;function i(e,t,r,n){var s,o,a,l,u,c,f,p,h,d,v,y=-1;if(t.maxDepth&&e.depth>t.maxDepth){throw new Error("[tree] extend(): max depth reached("+t.maxDepth+")")}if(t.circular){e.references.sources.push(n);e.references.targets.push(r)}if(t.own){if(t.nonEnum){a=Object.getOwnPropertyNames(n)}else{a=Object.keys(n)}}else{a=n}for(l in a){if(t.own){l=a[l]}if(t.descriptor){p=Object.getOwnPropertyDescriptor(n,l);u=p.value}else{u=n[l]}d=r;h=e.prefix+l;if(t.nofunc&&typeof u==="function"){continue}if(t.unflat&&e.depth===0){v=l.split(t.unflat);o=v.length-1;if(o){for(s=0;s<o;s++){if(!d[v[s]]||typeof d[v[s]]!=="object"&&typeof d[v[s]]!=="function"){d[v[s]]={}}d=d[v[s]]}h=e.prefix+v[o]}}if(t.deep&&u&&(typeof u==="object"||t.deepFunc&&typeof u==="function")&&(!t.descriptor||!p.get)&&((c=Object.getPrototypeOf(u))||true)&&(!t.deepFilter||(!t.deepFilter.whitelist||t.deepFilter.whitelist.indexOf(c)!==-1)&&(!t.deepFilter.blacklist||t.deepFilter.blacklist.indexOf(c)===-1))){if(t.circular){y=e.references.sources.indexOf(u)}if(t.flat){if(y>=0){continue}i({depth:e.depth+1,prefix:e.prefix+l+t.flat,references:e.references},t,d,u)}else{if(y>=0){if(t.descriptor){Object.defineProperty(d,h,{value:e.references.targets[y],enumerable:p.enumerable,writable:p.writable,configurable:p.configurable})}else{d[h]=e.references.targets[y]}continue}if(!d[h]||!d.hasOwnProperty(h)||typeof d[h]!=="object"&&typeof d[h]!=="function"){if(Array.isArray(u)){f=[]}else if(t.proto){f=Object.create(c)}else if(t.inherit){f=Object.create(u)}else{f={}}if(t.descriptor){Object.defineProperty(d,h,{value:f,enumerable:p.enumerable,writable:p.writable,configurable:p.configurable})}else{d[h]=f}}else if(t.proto&&Object.getPrototypeOf(d[h])!==c){Object.setPrototypeOf(d[h],c)}else if(t.inherit&&Object.getPrototypeOf(d[h])!==u){Object.setPrototypeOf(d[h],u)}if(t.circular){e.references.sources.push(u);e.references.targets.push(d[h])}i({depth:e.depth+1,prefix:"",references:e.references},t,d[h],u)}}else if(t.preserve&&d[h]!==undefined){continue}else if(!t.inherit){if(t.descriptor){Object.defineProperty(d,h,p)}else{d[h]=u}}if(t.move){delete n[l]}}}},{}]},{},[4])(4)});