import type { ToolCall, RiskAssessment } from '../types';
export declare class RiskValidator {
    private criticalPatterns;
    private highRiskPatterns;
    private mediumRiskPatterns;
    private safeCommands;
    constructor();
    private initializePatterns;
    assessRisk(toolCall: ToolCall): Promise<RiskAssessment>;
    private assessShellCommand;
    private checkCommandSpecifics;
    private assessFileDelete;
    private assessFileWrite;
    private assessFileMove;
    private assessGenericTool;
    private isSystemPath;
    private isExecutableFile;
    private containsSuspiciousContent;
    addCriticalPattern(pattern: RegExp): void;
    addHighRiskPattern(pattern: RegExp): void;
    addMediumRiskPattern(pattern: RegExp): void;
    addSafeCommand(command: string): void;
    removeSafeCommand(command: string): void;
}
//# sourceMappingURL=validator.d.ts.map