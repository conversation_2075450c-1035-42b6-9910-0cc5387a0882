(function(e){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=e()}else if(typeof define==="function"&&define.amd){define([],e)}else{var t;if(typeof window!=="undefined"){t=window}else if(typeof global!=="undefined"){t=global}else if(typeof self!=="undefined"){t=self}else{t=this}t.Promise7th=e()}})(function(){var e,t,n;return function(){function f(s,o,l){function u(n,e){if(!o[n]){if(!s[n]){var t="function"==typeof require&&require;if(!e&&t)return t(n,!0);if(a)return a(n,!0);var r=new Error("Cannot find module '"+n+"'");throw r.code="MODULE_NOT_FOUND",r}var i=o[n]={exports:{}};s[n][0].call(i.exports,function(e){var t=s[n][1][e];return u(t||e)},i,i.exports,f,s,o,l)}return o[n].exports}for(var a="function"==typeof require&&require,e=0;e<l.length;e++)u(l[e]);return u}return f}()({1:[function(e,t,n){"use strict";const i=e("./seventh.js");function r(e,t=4){this.jobRunner=e;this.jobs=new Map;this.pendingJobs=new Map;this.runningJobs=new Map;this.errorJobs=new Map;this.jobsDone=new Map;this.concurrency=+t||1;this.isQueueRunning=true;this.isLoopRunning=false;this.canLoopAgain=false;this.ready=i.resolved;this.startTime=null;this.endTime=null;this.drained=i.resolved;this.idle=i.resolved}i.Queue=r;function s(e,t=null,n=undefined){this.id=e;this.dependencies=t===null?null:[...t];this.data=n===undefined?e:n;this.error=null;this.startTime=null;this.endTime=null}r.Job=s;r.prototype.setConcurrency=function(e){this.concurrency=+e||1};r.prototype.stop=r.prototype.pause=function(){this.isQueueRunning=false};r.prototype.has=function(e){return this.jobs.has(e)};r.prototype.add=r.prototype.addJob=function(e,t,n=null){if(this.jobs.has(e)){return false}var r=new s(e,n,t);this.jobs.set(e,r);this.pendingJobs.set(e,r);this.canLoopAgain=true;if(this.isQueueRunning&&!this.isLoopRunning){this.run()}if(this.drained.isSettled()){this.drained=new i}return r};r.prototype.addBatch=r.prototype.addJobBatch=function(e){var t,n;for(t of e){if(this.jobs.has(t)){return false}n=new s(t);this.jobs.set(t,n);this.pendingJobs.set(t,n)}this.canLoopAgain=true;if(this.isQueueRunning&&!this.isLoopRunning){this.run()}if(this.drained.isSettled()){this.drained=new i}};r.prototype.run=r.prototype.resume=async function(){var e;this.isQueueRunning=true;if(this.isLoopRunning){return}this.isLoopRunning=true;if(!this.startTime){this.startTime=Date.now()}do{this.canLoopAgain=false;for(e of this.pendingJobs.values()){if(e.dependencies&&e.dependencies.some(e=>!this.jobsDone.has(e))){continue}if(this.idle.isSettled()){this.idle=new i}this.canLoopAgain=true;await this.ready;if(!this.isQueueRunning){this.finishRun();return}this.runJob(e)}}while(this.canLoopAgain);this.finishRun()};r.prototype.finishRun=function(){this.isLoopRunning=false;if(!this.pendingJobs.size){this.drained.resolve()}if(!this.runningJobs.size){this.endTime=Date.now();this.idle.resolve()}};r.prototype.runJob=async function(t){this.pendingJobs.delete(t.id);this.runningJobs.set(t.id,t);if(this.runningJobs.size>=this.concurrency){this.ready=new i}try{t.startTime=Date.now();await this.jobRunner(t.data);t.endTime=Date.now();this.jobsDone.set(t.id,t);this.canLoopAgain=true}catch(e){t.endTime=Date.now();t.error=e;this.errorJobs.set(t.id,t)}this.runningJobs.delete(t.id);if(this.runningJobs.size<this.concurrency){this.ready.resolve()}if(!this.isLoopRunning){if(this.isQueueRunning&&this.pendingJobs.size){this.run()}else{this.finishRun()}}};r.prototype.getJobTimes=function(){var e,t={};for(e of this.jobsDone.values()){t[e.id]=e.endTime-e.startTime}return t};r.prototype.getStats=function(){var e,t=0,n={pending:this.pendingJobs.size,running:this.runningJobs.size,failed:this.errorJobs.size,done:this.jobsDone.size,averageJobTime:null,queueTime:null};if(this.jobsDone.size){for(e of this.jobsDone.values()){t+=e.endTime-e.startTime}n.averageJobTime=t/this.jobsDone.size}if(this.endTime){n.queueTime=this.endTime-this.startTime}return n}},{"./seventh.js":8}],2:[function(e,t,n){"use strict";const o=e("./seventh.js");o.promisifyNodeApi=(r,i,s,e,t)=>{var n;i=i||"Async";s=s||"AsyncAll";e=e||(e=>e[0]!=="_"&&!e.endsWith("Sync"));if(t){n=[];for(let e in r){if(typeof r[e]==="function"){n.push(e)}}}else{n=Object.keys(r)}n.filter(t=>{if(typeof r[t]!=="function"){return false}for(let e in r[t].prototype){return false}return e(t,r)}).forEach(e=>{const t=e+i;const n=e+s;if(!r[t]){r[t]=o.promisify(r[e],r)}if(!r[n]){r[n]=o.promisifyAll(r[e],r)}})};o.promisifyAnyNodeApi=(e,t,n,r)=>{o.promisifyNodeApi(e,t,n,r,true)}},{"./seventh.js":8}],3:[function(e,t,n){"use strict";const v=e("./seventh.js");const f={};function c(){}v.all=e=>{var t=-1,n=false,r=0,i=Infinity,s,o=[],l=new v;for(s of e){if(n){break}const u=++t;v.resolve(s).then(e=>{if(n){return}o[u]=e;r++;if(r>=i){n=true;l._resolveValue(o)}},e=>{if(n){return}n=true;l.reject(e)})}i=t+1;if(!i){l._resolveValue(o)}return l};v._allArray=e=>{var t=e.length;if(!t){v._resolveValue([])}var n,r={settled:false,count:0,length:t,values:[],allPromise:new v};for(n=0;!r.settled&&n<t;n++){v._allArrayOne(e[n],n,r)}return r.allPromise};v._allArrayOne=(e,t,n)=>{v._bareThen(e,e=>{if(n.settled){return}n.values[t]=e;n.count++;if(n.count>=n.length){n.settled=true;n.allPromise._resolveValue(n.values)}},e=>{if(n.settled){return}n.settled=true;n.allPromise.reject(e)})};v.allSettled=e=>{var t=-1,n=false,r=0,i=Infinity,s,o=[],l=new v;for(s of e){if(n){break}const u=++t;v.resolve(s).then(e=>{if(n){return}o[u]={status:"fulfilled",value:e};r++;if(r>=i){n=true;l._resolveValue(o)}},e=>{if(n){return}o[u]={status:"rejected",reason:e};r++;if(r>=i){n=true;l._resolveValue(o)}})}i=t+1;if(!i){l._resolveValue(o)}return l};v.every=v.map=(e,t)=>{var n=-1,r=false,i=0,s=Infinity,o,l=[],u=new v;for(o of e){if(r){break}const a=++n;v.resolve(o).then(e=>{if(r){return}return t(e,a)}).then(e=>{if(r){return}l[a]=e;i++;if(i>=s){r=true;u._resolveValue(l)}},e=>{if(r){return}r=true;u.reject(e)})}s=n+1;if(!s){u._resolveValue(l)}return u};v.any=e=>{var t=-1,n=false,r=0,i=Infinity,s,o=[],l=new v;for(s of e){if(n){break}const u=++t;v.resolve(s).then(e=>{if(n){return}n=true;l._resolveValue(e)},e=>{if(n){return}o[u]=e;r++;if(r>=i){n=true;l.reject(new AggregateError(o),"Promise.any(): All promises have rejected")}})}i=t+1;if(!i){l.reject(new RangeError("Promise.any(): empty array"))}return l};v.some=(e,t)=>{var n=-1,r=false,i=0,s=Infinity,o,l=[],u=new v;for(o of e){if(r){break}const a=++n;v.resolve(o).then(e=>{if(r){return}return t(e,a)}).then(e=>{if(r){return}r=true;u._resolveValue(e)},e=>{if(r){return}l[a]=e;i++;if(i>=s){r=true;u.reject(new AggregateError(l,"Promise.some(): All promises have rejected"))}})}s=n+1;if(!s){u.reject(new RangeError("Promise.some(): empty array"))}return u};v.filter=(e,t)=>{var n=-1,r=false,i=0,s=Infinity,o,l=[],u=new v;for(o of e){if(r){break}const a=++n;v.resolve(o).then(e=>{if(r){return}l[a]=e;return t(e,a)}).then(e=>{if(r){return}i++;if(!e){l[a]=f}if(i>=s){r=true;l=l.filter(e=>e!==f);u._resolveValue(l)}},e=>{if(r){return}r=true;u.reject(e)})}s=n+1;if(!s){u._resolveValue(l)}else if(i>=s){r=true;l=l.filter(e=>e!==f);u._resolveValue(l)}return u};v.foreach=v.forEach=function(e,r,t){var i=-1,s=arguments.length>=3,o=e[Symbol.iterator](),l=new v,u=v.resolve(t);if(v.warnUnhandledRejection){v._handleAll(e)}var a=()=>{u.then(t=>{let{value:e,done:n}=o.next();i++;if(n){l.resolve(t)}else{u=v.resolve(e).then(s?e=>r(t,e,i):e=>r(e,i));a()}},e=>{l.reject(e);for(;;){let{value:e,done:t}=o.next();if(t){break}if(v.isThenable(e)){e.then(c,c)}}})};a();return l};v.reduce=(e,t,n)=>{return v.forEach(e,t,n)};v.mapObject=(t,n)=>{var r=false,i=0,s=Object.keys(t),o=s.length,l={},u=new v;for(let e=0;!r&&e<o;e++){const a=s[e];const f=t[a];v.resolve(f).then(e=>{if(r){return}return n(e,a)}).then(e=>{if(r){return}l[a]=e;i++;if(i>=o){r=true;u._resolveValue(l)}},e=>{if(r){return}r=true;u.reject(e)})}if(!o){u._resolveValue(l)}return u};v.concurrent=(n,e,r)=>{var i=-1,s=false,o=0,l=0,u=Infinity,a,f=false,c=[],h=e[Symbol.iterator](),d=new v;if(v.warnUnhandledRejection){v._handleAll(e)}n=+n||1;const p=()=>{while(!f&&o<n){({value:a,done:f}=h.next());if(f){u=i+1;if(l>=u){s=true;d._resolveValue(c);return}break}if(s){break}const t=++i;o++;v.resolve(a).then(e=>{if(s){return}return r(e,t)}).then(e=>{if(s){return}c[t]=e;l++;o--;if(l>=u){s=true;d._resolveValue(c);return}if(o<n){p();return}},e=>{if(s){return}s=true;d.reject(e)})}};p();if(i<0){d._resolveValue(c)}return d};v.race=e=>{var t=false,n,r=new v;for(n of e){if(t){break}v.resolve(n).then(e=>{if(t){return}t=true;r._resolveValue(e)},e=>{if(t){return}t=true;r.reject(e)})}return r}},{"./seventh.js":8}],4:[function(o,l,e){(function(t,n,s){(function(){"use strict";const e=n.Promise;var r;if(!t.browser){r=t.nextTick}else{o("setimmediate");r=s}function i(e){this.fn=e;this._then=i._dormantThen;this.value=null;this.thenHandlers=null;this.handledRejection=null;if(this.fn){this._exec()}}l.exports=i;i.Native=e;i.warnUnhandledRejection=true;i.nextTick=r;i.prototype._exec=function(){this._then=i._pendingThen;try{this.fn(e=>{this.resolve(e)},e=>{this.reject(e)})}catch(e){this.reject(e)}};i.prototype.resolve=i.prototype.fulfill=function(e){if(this._then.settled){return this}if(i.isThenable(e)){this._execThenPromise(e);return this}return this._resolveValue(e)};i.prototype._resolveValue=function(e){this._then=i._fulfilledThen;this.value=e;if(this.thenHandlers&&this.thenHandlers.length){this._execFulfillHandlers()}return this};i.prototype._execThenPromise=function(e){try{e.then(e=>{this.resolve(e)},e=>{this.reject(e)})}catch(e){this.reject(e)}};i.prototype.reject=function(e){if(this._then.settled){return this}this._then=i._rejectedThen;this.value=e;if(this.thenHandlers&&this.thenHandlers.length){this._execRejectionHandlers()}else if(i.warnUnhandledRejection&&!this.handledRejection){this._unhandledRejection()}return this};i.prototype._execFulfillHandlers=function(){var e,t=this.thenHandlers.length;for(e=0;e<t;e+=3){if(this.thenHandlers[e+1]){this._execOneFulfillHandler(this.thenHandlers[e],this.thenHandlers[e+1])}else{this.thenHandlers[e].resolve(this.value)}}};i.prototype._execOneFulfillHandler=function(t,e){try{t.resolve(e(this.value))}catch(e){t.reject(e)}};i.prototype._execRejectionHandlers=function(){var e,t=this.thenHandlers.length;for(e=0;e<t;e+=3){if(this.thenHandlers[e+2]){this._execOneRejectHandler(this.thenHandlers[e],this.thenHandlers[e+2])}else{this.thenHandlers[e].reject(this.value)}}};i.prototype._execOneRejectHandler=function(t,e){try{t.resolve(e(this.value))}catch(e){t.reject(e)}};i.prototype.resolveTimeout=i.prototype.fulfillTimeout=function(e,t){setTimeout(()=>this.resolve(t),e)};i.prototype.rejectTimeout=function(e,t){setTimeout(()=>this.reject(t),e)};i.prototype.resolveNextTick=i.prototype.fulfillNextTick=function(e){r(()=>this.resolve(e))};i.prototype.rejectNextTick=function(e){r(()=>this.reject(e))};i._dormantThen=function(e,t){if(this.fn){this._exec();return this._then(e,t)}var n=new i;if(!this.thenHandlers){this.thenHandlers=[n,e,t]}else{this.thenHandlers[this.thenHandlers.length]=n;this.thenHandlers[this.thenHandlers.length]=e;this.thenHandlers[this.thenHandlers.length]=t}return n};i._dormantThen.settled=false;i._pendingThen=function(e,t){var n=new i;if(!this.thenHandlers){this.thenHandlers=[n,e,t]}else{this.thenHandlers[this.thenHandlers.length]=n;this.thenHandlers[this.thenHandlers.length]=e;this.thenHandlers[this.thenHandlers.length]=t}return n};i._pendingThen.settled=false;i._fulfilledThen=function(e){if(!e){return this}var t=new i;r(()=>{try{t.resolve(e(this.value))}catch(e){t.reject(e)}});return t};i._fulfilledThen.settled=true;i._rejectedThen=function(e,t){if(!t){return this}this.handledRejection=true;var n=new i;r(()=>{try{n.resolve(t(this.value))}catch(e){n.reject(e)}});return n};i._rejectedThen.settled=true;i.prototype.then=function(e,t){return this._then(e,t)};i.prototype.catch=function(e=()=>undefined){return this._then(undefined,e)};i.prototype.finally=function(e){return this._then(e,e)};i.prototype.tap=i.prototype.tapThen=function(e){this._then(e,undefined);return this};i.prototype.tapCatch=function(e){this._then(undefined,e);return this};i.prototype.tapFinally=function(e){this._then(e,e);return this};i.prototype.fatal=function(){this._then(undefined,e=>{r(()=>{throw e})})};i.prototype.done=function(e,t){this._then(e,t).fatal();return this};i.prototype.callback=function(t){this._then(e=>{t(undefined,e)},e=>{t(e)}).fatal();return this};i.prototype.callbackAll=function(t){this._then(e=>{if(Array.isArray(e)){t(undefined,...e)}else{t(undefined,e)}},e=>{t(e)}).fatal();return this};i.callback=function(e){return new i((n,r)=>{e((e,t)=>{if(e){r(e)}else{n(t)}})})};i.callbackAll=function(e){return new i((n,r)=>{e((e,...t)=>{if(e){r(e)}else{n(t)}})})};i.prototype.toPromise=i.prototype.propagate=function(t){this._then(e=>{t.resolve(e)},e=>{t.reject(e)});return this};i.propagate=function(e,t){e.then(e=>{t.resolve(e)},e=>{t.reject(e)});return e};i.finally=function(e,t){return e.then(t,t)};i.resolve=i.fulfill=function(e){if(i.isThenable(e)){return i.fromThenable(e)}return i._resolveValue(e)};i._resolveValue=function(e){var t=new i;t._then=i._fulfilledThen;t.value=e;return t};i.reject=function(e){var t=new i;t._then=i._rejectedThen;t.value=e;return t};i.prototype.resolveTimeout=i.prototype.fulfillTimeout=function(e,t){setTimeout(()=>this.resolve(t),e)};i.resolveTimeout=i.fulfillTimeout=function(t,n){return new i(e=>setTimeout(()=>e(n),t))};i.prototype.rejectTimeout=function(e,t){setTimeout(()=>this.reject(t),e)};i.rejectTimeout=function(n,r){return new i((e,t)=>setTimeout(()=>t(r),n))};i.resolveNextTick=i.fulfillNextTick=function(t){return new i(e=>r(()=>e(t)))};i.rejectNextTick=function(n){return new i((e,t)=>r(()=>t(n)))};i.dormant=function(e){var t=new i;t.fn=e;return t};i.try=function(e){try{return i.resolve(e())}catch(e){return i.reject(e)}};i.isThenable=function(e){return e&&typeof e==="object"&&typeof e.then==="function"};i.fromThenable=function(e){if(e instanceof i){return e}return new i((t,n)=>{e.then(e=>{t(e)},e=>{n(e)})})};i._bareThen=function(e,t,n){if(e&&typeof e==="object"){if(e instanceof i){if(e._then===i._fulfilledThen){t(e.value)}else if(e._then===i._rejectedThen){n(e.value)}else{e._then(t,n)}}else if(typeof e.then==="function"){e.then(t,n)}else{t(e)}}else{t(e)}};i._handleAll=function(e){var t;for(t of e){if(i.isThenable(t)){t.handledRejection=true}}};i.prototype._unhandledRejection=function(){this.handledRejection=false;const t=e=>setTimeout(e,0);if(this.value instanceof Error){t(()=>{if(this.handledRejection===false){this.value.message="Unhandled promise rejection: "+this.value.message;console.error(this.value)}})}else{let e=new Error("Unhandled promise rejection");t(()=>{if(this.handledRejection===false){console.error(e);console.error("Rejection reason:",this.value)}})}};i.prototype.isSettled=function(){return this._then.settled};i.prototype.getStatus=function(){switch(this._then){case i._dormantThen:return"dormant";case i._pendingThen:return"pending";case i._fulfilledThen:return"fulfilled";case i._rejectedThen:return"rejected"}};i.prototype.inspect=function(){switch(this._then){case i._dormantThen:return"Promise { <DORMANT> }";case i._pendingThen:return"Promise { <PENDING> }";case i._fulfilledThen:return"Promise { <FULFILLED> "+this.value+" }";case i._rejectedThen:return"Promise { <REJECTED> "+this.value+" }"}};i.resolved=i.dummy=i.resolve();if(t.browser){i.prototype.resolveAtAnimationFrame=function(e){window.requestAnimationFrame(()=>this.resolve(e))};i.prototype.rejectAtAnimationFrame=function(e){window.requestAnimationFrame(()=>this.reject(e))};i.resolveAtAnimationFrame=function(t){return new i(e=>window.requestAnimationFrame(()=>e(t)))};i.rejectAtAnimationFrame=function(n){return new i((e,t)=>window.requestAnimationFrame(()=>t(n)))}}}).call(this)}).call(this,o("_process"),typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{},o("timers").setImmediate)},{_process:11,setimmediate:10,timers:12}],5:[function(e,t,n){"use strict";const h=e("./seventh.js");const l=()=>undefined;h.promisifyAll=(t,i)=>{if(i){return(...e)=>{return new h((n,r)=>{t.call(i,...e,(e,...t)=>{if(e){if(t.length&&e instanceof Error){e.args=t}r(e)}else{n(t)}})})}}return function(...e){return new h((n,r)=>{t.call(this,...e,(e,...t)=>{if(e){if(t.length&&e instanceof Error){e.args=t}r(e)}else{n(t)}})})}};h.promisify=(t,i)=>{if(i){return(...e)=>{return new h((n,r)=>{t.call(i,...e,(e,t)=>{if(e){if(t!==undefined&&e instanceof Error){e.arg=t}r(e)}else{n(t)}})})}}return function(...e){return new h((n,r)=>{t.call(this,...e,(e,t)=>{if(e){if(t!==undefined&&e instanceof Error){e.arg=t}r(e)}else{n(t)}})})}};h.interceptor=(r,i,s,o)=>{if(typeof s!=="function"){o=s;s=l}return function(...e){var t=o||this,n=r.call(t,...e);h.resolve(n).then(e=>i.call(t,e),e=>s.call(t,e));return n}};h.once=(r,i)=>{var n=null,s=new WeakMap;const o=e=>{var t=e?s.get(e):n;if(t){return t}t={triggered:false,result:undefined};if(e){s.set(e,t)}else{n=t}return t};return function(...e){var t=i||this,n=o(t);if(!n.triggered){n.triggered=true;n.result=r.call(t,...e)}return n.result}};h.serialize=(i,s)=>{var n=null,r=new WeakMap;const o=e=>{var t=e?r.get(e):n;if(t){return t}t={lastPromise:h.resolve()};if(e){r.set(e,t)}else{n=t}return t};return function(...e){var t=s||this,n=o(t),r=new h;n.lastPromise.finally(()=>{h.propagate(i.call(t,...e),r)});n.lastPromise=r;return r}};h.debounce=(i,s)=>{var n=null,r=new WeakMap;const o=e=>{var t=e?r.get(e):n;if(t){return t}t={inProgress:null};if(e){r.set(e,t)}else{n=t}return t};return function(...e){var t=s||this,n=o(t);if(n.inProgress){return n.inProgress}let r=n.inProgress=i.call(t,...e);h.finally(r,()=>n.inProgress=null);return r}};h.debounceDelay=(i,s,o)=>{var n=null,r=new WeakMap;const l=e=>{var t=e?r.get(e):n;if(t){return t}t={inProgress:null};if(e){r.set(e,t)}else{n=t}return t};return function(...e){var t=o||this,n=l(t);if(n.inProgress){return n.inProgress}let r=n.inProgress=s.call(t,...e);h.finally(r,()=>setTimeout(()=>n.inProgress=null,i));return r}};h.debounceNextTick=(n,r)=>{var i=null,e=null,t=null,s=null,o=new WeakMap;const l=e=>{var t=e?o.get(e):s;if(t){return t}t={inProgress:null,waitingNextTick:false,currentUpdateWith:null,currentUpdatePromise:null,nextUpdateWith:null,nextUpdatePromise:null};if(e){o.set(e,t)}else{s=t}return t};const u=function(){var n=l(this);n.inProgress=n.currentUpdatePromise=null;if(n.nextUpdateWith){let e=n.nextUpdateWith;n.nextUpdateWith=null;let t=n.nextUpdatePromise;n.nextUpdatePromise=null;n.inProgress=i.call(this,e);h.propagate(n.inProgress,t)}};i=function(e){var t=l(this);t.inProgress=new h;t.currentUpdateWith=e;t.waitingNextTick=true;h.nextTick(()=>{t.waitingNextTick=false;let e=n.call(this,...t.currentUpdateWith);if(h.isThenable(e)){t.currentUpdatePromise=e;h.finally(e,u.bind(this));h.propagate(e,t.inProgress)}else{t.currentUpdatePromise=null;t.inProgress.resolve(e);u.call(this)}});return t.inProgress};return function(...e){var t=r||this,n=l(t);if(n.waitingNextTick){n.currentUpdateWith=e;return n.inProgress}if(n.currentUpdatePromise){if(!n.nextUpdatePromise){n.nextUpdatePromise=new h}n.nextUpdateWith=e;return n.nextUpdatePromise}return i.call(t,e)}};h.debounceUpdate=(e,n,r)=>{var i=null,s=null,t=0,o=null,l=null,u=null,a=new WeakMap;if(typeof e==="function"){r=n;n=e}else{if(typeof e.delay==="number"){t=e.delay}if(typeof e.delayFn==="function"){o=e.delayFn}if(e.waitNextTick){l=h.resolveNextTick}else if(typeof e.waitFn==="function"){l=e.waitFn}}const f=e=>{var t=e?a.get(e):u;if(t){return t}t={inProgress:null,waitInProgress:null,currentUpdateWith:null,currentUpdatePromise:null,nextUpdateWith:null,nextUpdatePromise:null};if(e){a.set(e,t)}else{u=t}return t};const c=function(){var n=f(this);n.inProgress=n.currentUpdatePromise=null;if(n.nextUpdateWith){let e=n.nextUpdateWith;n.nextUpdateWith=null;let t=n.nextUpdatePromise;n.nextUpdatePromise=null;n.inProgress=i.call(this,e);h.propagate(n.inProgress,t)}};if(o){s=function(){o().then(c.bind(this))}}else if(t){s=function(){setTimeout(c.bind(this),t)}}else{s=c}if(l){i=function(e){var t=f(this);t.inProgress=new h;t.currentUpdateWith=e;t.waitInProgress=l();h.finally(t.waitInProgress,()=>{t.waitInProgress=null;t.currentUpdatePromise=n.call(this,...t.currentUpdateWith);h.finally(t.currentUpdatePromise,s.bind(this));h.propagate(t.currentUpdatePromise,t.inProgress)});return t.inProgress};return function(...e){var t=r||this,n=f(t);if(n.waitInProgress){n.currentUpdateWith=e;return n.inProgress}if(n.currentUpdatePromise){if(!n.nextUpdatePromise){n.nextUpdatePromise=new h}n.nextUpdateWith=e;return n.nextUpdatePromise}return i.call(t,e)}}i=function(e){var t=f(this);t.inProgress=n.call(this,...e);h.finally(t.inProgress,s.bind(this));return t.inProgress};return function(...e){var t=r||this,n=f(t);if(n.inProgress){if(!n.nextUpdatePromise){n.nextUpdatePromise=new h}n.nextUpdateWith=e;return n.nextUpdatePromise}return i.call(t,e)}};h.NO_DELAY={};h.BATCH_NO_DELAY={};h.debounceSync=(s,o)=>{var n=new Map;const l=e=>{var t=n.get(e);if(!t){t={inProgress:null,inProgressIsFull:null,last:null,lastTime:null,lastFullSync:null,lastFullSyncTime:null,nextFullSyncPromise:null,nextFullSyncWith:null,noDelayBatches:new Set};n.set(e,t)}return t};const u=(e,t)=>{var n,r,i,s=new Date;e.inProgress=null;if(t>=2){e.lastFullSyncTime=e.lastTime=s}else if(t>=1){e.lastTime=s}if(e.nextFullSyncWith){if(o.delay&&e.lastFullSyncTime&&(n=s-e.lastFullSyncTime-o.delay)<0){e.inProgress=h.resolveTimeout(-n+1);e.inProgress.finally(()=>u(e,0));return e.nextFullSyncPromise}r=e.nextFullSyncWith;e.nextFullSyncWith=null;i=e.nextFullSyncPromise;e.nextFullSyncPromise=null;e.lastFullSync=e.last=e.inProgress=o.fn.call(...r);h.propagate(e.inProgress,i);h.finally(e.inProgress,()=>u(e,2));return e.inProgress}};const e=function(e,...t){var n=false,r=s.thisBinding||this,i=l(e);if(t[0]===h.NO_DELAY){n=true;t.shift()}else if(t[0]===h.BATCH_NO_DELAY){t.shift();let e=t.shift();if(!i.noDelayBatches.has(e)){i.noDelayBatches.add(e);n=true}}if(i.inProgress){return i.inProgress}if(!n&&s.delay&&i.lastTime&&new Date-i.lastTime<s.delay){if(typeof s.onDebounce==="function"){s.onDebounce(e,...t)}return i.last}i.last=i.inProgress=s.fn.call(r,e,...t);i.inProgressIsFull=false;h.finally(i.inProgress,()=>u(i,1));return i.inProgress};const t=function(e,...t){var n,r=false,i=o.thisBinding||this,s=l(e);if(t[0]===h.NO_DELAY){r=true;t.shift()}else if(t[0]===h.BATCH_NO_DELAY){t.shift();let e=t.shift();if(!s.noDelayBatches.has(e)){s.noDelayBatches.add(e);r=true}}if(!s.inProgress&&!r&&o.delay&&s.lastFullSyncTime&&(n=new Date-s.lastFullSyncTime-o.delay)<0){s.inProgress=h.resolveTimeout(-n+1);h.finally(s.inProgress,()=>u(s,0))}if(s.inProgress){if(!s.nextFullSyncPromise){s.nextFullSyncPromise=new h}s.nextFullSyncWith=[i,e,...t];return s.nextFullSyncPromise}s.lastFullSync=s.last=s.inProgress=o.fn.call(i,e,...t);h.finally(s.inProgress,()=>u(s,2));return s.inProgress};return[e,t]};h.timeout=(n,r,i)=>{return function(...e){var t=new h;h.propagate(r.call(i||this,...e),t);setTimeout(()=>t.reject(new Error("Timeout")),n);return t}};h.variableTimeout=(r,i)=>{return function(e,...t){var n=new h;h.propagate(r.call(i||this,...t),n);setTimeout(()=>n.reject(new Error("Timeout")),e);return n}}},{"./seventh.js":8}],6:[function(e,t,n){(function(o){(function(){"use strict";const i=e("./seventh.js");var s=false;i.asyncExit=function(n,r){if(s){return}s=true;var e=o.listeners("asyncExit");if(!e.length){o.exit(n);return}if(r===undefined){r=1e3}const t=t=>{if(t.length<3){t(n,r);return i.dummy}return new i(e=>{t(n,r,()=>{e()})})};i.map(e,t).finally(()=>o.exit(n));setTimeout(()=>o.exit(n),r)};i.resolveSafeTimeout=function(t,n){return new i(e=>{setTimeout(()=>{setTimeout(()=>{setTimeout(()=>{setTimeout(()=>e(n),0)},t/2)},t/2)},0)})}}).call(this)}).call(this,e("_process"))},{"./seventh.js":8,_process:11}],7:[function(e,t,n){"use strict";const r=e("./seventh.js");r.parasite=()=>{var e=["tap","tapCatch","finally","fatal","done","callback","callbackAll"];e.forEach(e=>r.Native.prototype[e]=r.prototype[e]);r.Native.prototype._then=r.Native.prototype.then}},{"./seventh.js":8}],8:[function(e,t,n){"use strict";const r=e("./core.js");t.exports=r;e("./batch.js");e("./wrapper.js");e("./decorators.js");e("./Queue.js");e("./api.js");e("./parasite.js");e("./misc.js")},{"./Queue.js":1,"./api.js":2,"./batch.js":3,"./core.js":4,"./decorators.js":5,"./misc.js":6,"./parasite.js":7,"./wrapper.js":9}],9:[function(e,t,n){"use strict";const a=e("./seventh.js");a.timeLimit=(n,r)=>{return new a((e,t)=>{if(typeof r==="function"){r=r()}a.resolve(r).then(e,t);setTimeout(()=>t(new Error("Timeout")),n)})};a.retry=(e,t)=>{var n=e.retries||1,r=e.coolDown||0,i=e.raiseFactor||1,s=e.maxCoolDown||Infinity,o=e.timeout||0,l=e.catch||null;const u=()=>{return(o?a.timeLimit(o,t):t()).catch(e=>{if(!n--){throw e}var t=r;r=Math.min(r*i,s);if(l){return a.resolve(l(e)).then(()=>a.resolveTimeout(t).then(u))}return a.resolveTimeout(t).then(u)})};return u()};a.onceEvent=(t,n)=>{return new a(e=>t.once(n,e))};a.onceEventAll=(e,n)=>{return new a(t=>e.once(n,(...e)=>t(e)))};a.onceEventOrError=(l,u,e,n=false)=>{return new a((t,r)=>{var i;var s=(...e)=>{l.removeListener("error",o);if(i){for(let e in i){l.removeListener(e,i[e])}}t(n?e:e[0])};var o=e=>{l.removeListener(u,s);if(i){for(let e in i){l.removeListener(e,i[e])}}r(e)};l.once(u,s);l.once("error",o);if(e){if(!Array.isArray(e)){e=[e]}i={};e.forEach(n=>{var e=(...e)=>{l.removeListener("error",o);l.removeListener(u,s);var t=new Error("Received an excluded event: "+n);t.event=n;t.eventArgs=e;r(t)};l.once(n,e);i[n]=e})}})};a.onceEventAllOrError=(e,t,n)=>{return a.onceEventOrError(e,t,n,true)}},{"./seventh.js":8}],10:[function(e,t,n){(function(g,e){(function(){(function(n,r){"use strict";if(n.setImmediate){return}var i=1;var s={};var o=false;var l=n.document;var u;function e(e){if(typeof e!=="function"){e=new Function(""+e)}var t=new Array(arguments.length-1);for(var n=0;n<t.length;n++){t[n]=arguments[n+1]}var r={callback:e,args:t};s[i]=r;u(i);return i++}function a(e){delete s[e]}function f(e){var t=e.callback;var n=e.args;switch(n.length){case 0:t();break;case 1:t(n[0]);break;case 2:t(n[0],n[1]);break;case 3:t(n[0],n[1],n[2]);break;default:t.apply(r,n);break}}function c(e){if(o){setTimeout(c,0,e)}else{var t=s[e];if(t){o=true;try{f(t)}finally{a(e);o=false}}}}function t(){u=function(e){g.nextTick(function(){c(e)})}}function h(){if(n.postMessage&&!n.importScripts){var e=true;var t=n.onmessage;n.onmessage=function(){e=false};n.postMessage("","*");n.onmessage=t;return e}}function d(){var t="setImmediate$"+Math.random()+"$";var e=function(e){if(e.source===n&&typeof e.data==="string"&&e.data.indexOf(t)===0){c(+e.data.slice(t.length))}};if(n.addEventListener){n.addEventListener("message",e,false)}else{n.attachEvent("onmessage",e)}u=function(e){n.postMessage(t+e,"*")}}function p(){var t=new MessageChannel;t.port1.onmessage=function(e){var t=e.data;c(t)};u=function(e){t.port2.postMessage(e)}}function v(){var n=l.documentElement;u=function(e){var t=l.createElement("script");t.onreadystatechange=function(){c(e);t.onreadystatechange=null;n.removeChild(t);t=null};n.appendChild(t)}}function m(){u=function(e){setTimeout(c,0,e)}}var y=Object.getPrototypeOf&&Object.getPrototypeOf(n);y=y&&y.setTimeout?y:n;if({}.toString.call(n.process)==="[object process]"){t()}else if(h()){d()}else if(n.MessageChannel){p()}else if(l&&"onreadystatechange"in l.createElement("script")){v()}else{m()}y.setImmediate=e;y.clearImmediate=a})(typeof self==="undefined"?typeof e==="undefined"?this:e:self)}).call(this)}).call(this,e("_process"),typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{_process:11}],11:[function(e,t,n){var r=t.exports={};var i;var s;function o(){throw new Error("setTimeout has not been defined")}function l(){throw new Error("clearTimeout has not been defined")}(function(){try{if(typeof setTimeout==="function"){i=setTimeout}else{i=o}}catch(e){i=o}try{if(typeof clearTimeout==="function"){s=clearTimeout}else{s=l}}catch(e){s=l}})();function u(t){if(i===setTimeout){return setTimeout(t,0)}if((i===o||!i)&&setTimeout){i=setTimeout;return setTimeout(t,0)}try{return i(t,0)}catch(e){try{return i.call(null,t,0)}catch(e){return i.call(this,t,0)}}}function a(t){if(s===clearTimeout){return clearTimeout(t)}if((s===l||!s)&&clearTimeout){s=clearTimeout;return clearTimeout(t)}try{return s(t)}catch(e){try{return s.call(null,t)}catch(e){return s.call(this,t)}}}var f=[];var c=false;var h;var d=-1;function p(){if(!c||!h){return}c=false;if(h.length){f=h.concat(f)}else{d=-1}if(f.length){v()}}function v(){if(c){return}var e=u(p);c=true;var t=f.length;while(t){h=f;f=[];while(++d<t){if(h){h[d].run()}}d=-1;t=f.length}h=null;c=false;a(e)}r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var n=1;n<arguments.length;n++){t[n-1]=arguments[n]}}f.push(new m(e,t));if(f.length===1&&!c){u(v)}};function m(e,t){this.fun=e;this.array=t}m.prototype.run=function(){this.fun.apply(null,this.array)};r.title="browser";r.browser=true;r.env={};r.argv=[];r.version="";r.versions={};function y(){}r.on=y;r.addListener=y;r.once=y;r.off=y;r.removeListener=y;r.removeAllListeners=y;r.emit=y;r.prependListener=y;r.prependOnceListener=y;r.listeners=function(e){return[]};r.binding=function(e){throw new Error("process.binding is not supported")};r.cwd=function(){return"/"};r.chdir=function(e){throw new Error("process.chdir is not supported")};r.umask=function(){return 0}},{}],12:[function(u,e,a){(function(r,l){(function(){var e=u("process/browser.js").nextTick;var t=Function.prototype.apply;var i=Array.prototype.slice;var s={};var o=0;a.setTimeout=function(){return new n(t.call(setTimeout,window,arguments),clearTimeout)};a.setInterval=function(){return new n(t.call(setInterval,window,arguments),clearInterval)};a.clearTimeout=a.clearInterval=function(e){e.close()};function n(e,t){this._id=e;this._clearFn=t}n.prototype.unref=n.prototype.ref=function(){};n.prototype.close=function(){this._clearFn.call(window,this._id)};a.enroll=function(e,t){clearTimeout(e._idleTimeoutId);e._idleTimeout=t};a.unenroll=function(e){clearTimeout(e._idleTimeoutId);e._idleTimeout=-1};a._unrefActive=a.active=function(t){clearTimeout(t._idleTimeoutId);var e=t._idleTimeout;if(e>=0){t._idleTimeoutId=setTimeout(function e(){if(t._onTimeout)t._onTimeout()},e)}};a.setImmediate=typeof r==="function"?r:function(t){var n=o++;var r=arguments.length<2?false:i.call(arguments,1);s[n]=true;e(function e(){if(s[n]){if(r){t.apply(null,r)}else{t.call(null)}a.clearImmediate(n)}});return n};a.clearImmediate=typeof l==="function"?l:function(e){delete s[e]}}).call(this)}).call(this,u("timers").setImmediate,u("timers").clearImmediate)},{"process/browser.js":11,timers:12}]},{},[8])(8)});