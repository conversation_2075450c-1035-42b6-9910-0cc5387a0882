{"name": "n<PERSON><PERSON>", "version": "1.0.19", "description": "Multidimensional Arrays", "main": "ndarray.js", "directories": {"test": "test"}, "dependencies": {"iota-array": "^1.0.0", "is-buffer": "^1.0.2"}, "devDependencies": {"permutation-rank": "^1.0.0", "dup": "^1.0.0", "invert-permutation": "^1.0.0", "tape": "^2.12.3"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/ndarray.git"}, "keywords": ["n<PERSON><PERSON>", "array", "multi", "multidimensional", "dimension", "higher", "image", "volume", "webgl", "tensor", "matrix", "linear", "algebra", "science", "numerical", "computing", "stride", "shape"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "readmeFilename": "README.md", "gitHead": "a85785ca7a7e12c3fc29671a4f7c214bebc4ddc7", "testling": {"files": "test/*.js", "browsers": ["ie/10..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/6.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}