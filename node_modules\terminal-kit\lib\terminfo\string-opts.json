["backTab", "bell", "carriageReturn", "changeScrollRegion", "clearAllTabs", "clearScreen", "clrEol", "clrEos", "column<PERSON><PERSON><PERSON>", "commandCharacter", "cursor<PERSON><PERSON>ress", "cursorDown", "cursorHome", "cursorInvisible", "cursor<PERSON><PERSON>t", "cursor<PERSON><PERSON><PERSON><PERSON><PERSON>", "cursor<PERSON>ormal", "cursorRight", "cursorToLl", "cursorUp", "cursorVisible", "deleteCharacter", "deleteLine", "disStatusLine", "downHalfLine", "enterAltCharsetMode", "enterBlinkMode", "enterBoldMode", "enterCaMode", "enterDeleteMode", "enterDimMode", "enterInsertMode", "enterSecureMode", "enterProtectedMode", "enterReverseMode", "enterStandoutMode", "enterUnderlineMode", "eraseChars", "exitAltCharsetMode", "exitAttributeMode", "exitCaMode", "exitDeleteMode", "exitInsertMode", "exitStandoutMode", "exitUnderlineMode", "flashScreen", "formFeed", "fromStatusLine", "init1String", "init2String", "init3String", "initFile", "insertCharacter", "insertLine", "insertPadding", "keyBackspace", "keyCatab", "keyClear", "keyCtab", "keyDc", "keyDl", "keyDown", "keyEic", "keyEol", "keyEos", "keyF0", "keyF1", "keyF10", "keyF2", "keyF3", "keyF4", "keyF5", "keyF6", "keyF7", "keyF8", "keyF9", "keyHome", "keyIc", "keyIl", "keyLeft", "keyLl", "keyNpage", "keyPpage", "keyRight", "keySf", "keySr", "keyStab", "keyUp", "keypadLocal", "keypadXmit", "labF0", "labF1", "labF10", "labF2", "labF3", "labF4", "labF5", "labF6", "labF7", "labF8", "labF9", "metaOff", "metaOn", "newline", "padChar", "parmDch", "parmDeleteLine", "parmDownCursor", "parmIch", "parmIndex", "parmInsertLine", "parmLeftCursor", "parmRightCursor", "parmRindex", "parmUpCursor", "p<PERSON><PERSON><PERSON>", "pkeyLocal", "pkeyXmit", "printScreen", "prt<PERSON><PERSON><PERSON>", "prtrOn", "repeatChar", "reset1String", "reset2String", "reset3String", "resetFile", "restoreCursor", "<PERSON><PERSON><PERSON><PERSON>", "saveCursor", "scrollForward", "scrollReverse", "setAttributes", "setTab", "setWindow", "tab", "toStatusLine", "underlineChar", "upHalfLine", "initProg", "keyA1", "keyA3", "keyB2", "keyC1", "keyC3", "prtrNon", "charPadding", "acsChars", "plabNorm", "keyBtab", "enterXonMode", "exitXonMode", "enterAmMode", "exitAmMode", "xonCharacter", "xoffCharacter", "enaAcs", "labelOn", "labelOff", "keyBeg", "keyCancel", "keyClose", "keyCommand", "keyCopy", "keyCreate", "keyEnd", "keyEnter", "keyExit", "keyFind", "keyHelp", "keyMark", "keyMessage", "keyMove", "keyNext", "keyOpen", "keyOptions", "keyPrevious", "keyPrint", "keyRedo", "keyReference", "keyRefresh", "<PERSON><PERSON><PERSON><PERSON>", "keyRestart", "keyResume", "keySave", "keySuspend", "keyUndo", "keySbeg", "keyScancel", "keyScommand", "keyScopy", "keyScreate", "keySdc", "keySdl", "keySelect", "keySend", "keySeol", "keySexit", "keySfind", "keyShelp", "keyShome", "keySic", "keySleft", "keySmessage", "keySmove", "keySnext", "keySoptions", "keySprevious", "keySprint", "keySredo", "keySreplace", "keySright", "keySrsume", "keySsave", "keySsuspend", "keySundo", "reqForInput", "keyF11", "keyF12", "keyF13", "keyF14", "keyF15", "keyF16", "keyF17", "keyF18", "keyF19", "keyF20", "keyF21", "keyF22", "keyF23", "keyF24", "keyF25", "keyF26", "keyF27", "keyF28", "keyF29", "keyF30", "keyF31", "keyF32", "keyF33", "keyF34", "keyF35", "keyF36", "keyF37", "keyF38", "keyF39", "keyF40", "keyF41", "keyF42", "keyF43", "keyF44", "keyF45", "keyF46", "keyF47", "keyF48", "keyF49", "keyF50", "keyF51", "keyF52", "keyF53", "keyF54", "keyF55", "keyF56", "keyF57", "keyF58", "keyF59", "keyF60", "keyF61", "keyF62", "keyF63", "clrBol", "<PERSON><PERSON><PERSON><PERSON>", "setLeftMargin", "setRightMargin", "labelFormat", "setClock", "displayClock", "removeClock", "createWindow", "gotoWindow", "hangup", "dialPhone", "quickDial", "tone", "pulse", "flashHook", "fixedPause", "waitTone", "user0", "user1", "user2", "user3", "user4", "user5", "user6", "user7", "user8", "user9", "origPair", "origColors", "initializeColor", "initializePair", "setColorPair", "setForeground", "setBackground", "changeChar<PERSON><PERSON>", "changeLinePitch", "changeResHorz", "changeRes<PERSON><PERSON>", "defineChar", "enterDoublewideMode", "enterDraftQuality", "enterItalicsMode", "enterLeftwardMode", "enterMicroMode", "enterNearLetterQuality", "enterNormalQuality", "enterShadowMode", "enterSubscriptMode", "enterSuperscriptMode", "enterUpwardMode", "exitDoublewideMode", "exitItalicsMode", "exitLeftwardMode", "exitMicroMode", "exitShadowMode", "exitSubscriptMode", "exitSuperscriptMode", "exitUpwardMode", "microColumnAddress", "microDown", "microLeft", "microRight", "microRowAddress", "microUp", "orderOfPins", "parmDownMicro", "parmLeftMicro", "parmRightMicro", "parmUpMicro", "selectCharSet", "setBottomMargin", "setBottomMarginParm", "setLeftMarginParm", "setRightMarginParm", "setTopMargin", "setTopMarginParm", "startBitImage", "startCharSetDef", "stopBitImage", "stopCharSetDef", "subscriptCharacters", "superscriptCharacters", "theseCauseCr", "zeroMotion", "charSetNames", "keyMouse", "mouseInfo", "reqMousePos", "getMouse", "setAForeground", "setABackground", "pkeyPlab", "deviceType", "codeSetInit", "set0DesSeq", "set1DesSeq", "set2DesSeq", "set3DesSeq", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "setTbMargin", "bitImageRepeat", "bitImageNewline", "bitImageCarriageReturn", "colorNames", "defineBitImageRegion", "endBitImageRegion", "setColorBand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayPcChar", "enterPcCharsetMode", "exitPcCharsetMode", "enterScancodeMode", "exitScancodeMode", "pcTermOptions", "scancodeEscape", "altScancodeEsc", "enterHorizontalHlMode", "enterLeftHlMode", "enterLowHlMode", "enterRightHlMode", "enterTopHlMode", "enterVerticalHlMode", "setAAttributes", "setPglenInch"]