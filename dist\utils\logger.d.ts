import winston from 'winston';
export declare const logger: winston.Logger;
export declare const createLogger: (component: string) => winston.Logger;
export declare const loggers: {
    agent: winston.Logger;
    session: winston.Logger;
    context: winston.Logger;
    ai: winston.Logger;
    tools: winston.Logger;
    security: winston.Logger;
    cli: winston.Logger;
    storage: winston.Logger;
};
export declare class PerformanceLogger {
    private startTime;
    private operation;
    private logger;
    constructor(operation: string, customLogger?: winston.Logger);
    end(metadata?: Record<string, unknown>): void;
    error(error: Error, metadata?: Record<string, unknown>): void;
}
export declare const logError: (message: string, error: unknown, metadata?: Record<string, unknown>) => void;
export declare const logDebug: (message: string, dataFn: () => Record<string, unknown>) => void;
export declare const auditLog: (action: string, user: string, resource: string, result: "success" | "failure", metadata?: Record<string, unknown>) => void;
export declare const logAIInteraction: (provider: string, model: string, tokens: number, duration: number, success: boolean, metadata?: Record<string, unknown>) => void;
export declare const logToolExecution: (toolName: string, args: Record<string, unknown>, result: {
    success: boolean;
    duration: number;
    output?: string;
    error?: string;
}, metadata?: Record<string, unknown>) => void;
export declare const logSessionActivity: (sessionId: string, activity: string, metadata?: Record<string, unknown>) => void;
export declare const logConfigChange: (component: string, changes: Record<string, {
    from: unknown;
    to: unknown;
}>, metadata?: Record<string, unknown>) => void;
export declare const cleanupLogs: (olderThanDays?: number) => Promise<void>;
export default logger;
//# sourceMappingURL=logger.d.ts.map