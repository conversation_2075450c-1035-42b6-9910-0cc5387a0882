name: CI
on:
  push:
    branches: [master]
  pull_request: # run on all PRs, not just PRs to a particular branch
jobs:
  test:
    strategy:
      fail-fast: true
      matrix:
        nodeversion: [8.x, 10.x, 12.x, 14.x]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.nodeversion }}
      - run: npm install
      - run: npm test
