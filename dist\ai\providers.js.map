{"version": 3, "file": "providers.js", "sourceRoot": "", "sources": ["../../src/ai/providers.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAC5B,mCAAgC;AAChC,2CAAwC;AA4BxC,MAAa,iBAAiB;IACpB,MAAM,CAAS;IACf,MAAM,CAAmB;IAEjC,YAAY,MAAmB;QAC7B,IAAI,CAAC,MAAM,GAAG;YACZ,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI;YACnC,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,GAAG;SACvC,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;IACpC,CAAC;IAEO,YAAY;QAClB,MAAM,YAAY,GAAQ;YACxB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;SAC5D,CAAC;QAEF,oCAAoC;QACpC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC7B,KAAK,QAAQ;gBACX,+BAA+B;gBAC/B,MAAM;YAER,KAAK,UAAU;gBACb,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,6BAA6B,CAAC;gBAC5E,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;gBAC5E,MAAM;YAER,KAAK,QAAQ;gBACX,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,2BAA2B,CAAC;gBAC1E,YAAY,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,wCAAwC;gBACxE,MAAM;YAER,KAAK,OAAO;gBACV,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;gBAC3C,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;gBAChF,YAAY,CAAC,cAAc,GAAG;oBAC5B,SAAS,EAAE,YAAY,CAAC,MAAM;iBAC/B,CAAC;gBACF,MAAM;YAER;gBACE,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,OAAO,IAAI,gBAAM,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,SAAsB;QACvC,IAAI,CAAC,MAAM,GAAG;YACZ,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,MAAM,EAAE,SAAS,CAAC,MAAM;YACxB,OAAO,EAAE,SAAS,CAAC,OAAO;YAC1B,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;YACvD,WAAW,EAAE,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;SAC9D,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAClC,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACxC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC9B,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;SACzB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAA0B;QAC/C,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;QAElE,IAAI,CAAC;YACH,sCAAsC;YACtC,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAEvD,oCAAoC;YACpC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;YAE1E,kCAAkC;YAClC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACvD,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ,EAAE,cAAc;gBACxB,KAAK,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;gBACvD,WAAW,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBACxD,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gBACzC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI;gBAC5C,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,gBAAgB,GAAG,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAe,EAAE,CAAC;YACjC,MAAM,WAAW,GAAiB,EAAE,CAAC;YACrC,IAAI,eAAe,GAA6B,IAAI,CAAC;YAErD,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;gBAEtC,IAAI,KAAK,EAAE,OAAO,EAAE,CAAC;oBACnB,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC;oBAClC,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,KAAK,CAAC,OAAO;qBACvB,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,KAAK,EAAE,UAAU,EAAE,CAAC;oBACtB,KAAK,MAAM,aAAa,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;wBAC7C,IAAI,aAAa,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;4BACtC,IAAI,eAAe,IAAI,eAAe,CAAC,EAAE,EAAE,CAAC;gCAC1C,4BAA4B;gCAC5B,SAAS,CAAC,IAAI,CAAC,eAA2B,CAAC,CAAC;4BAC9C,CAAC;4BAED,sBAAsB;4BACtB,eAAe,GAAG;gCAChB,EAAE,EAAE,aAAa,CAAC,EAAE,IAAI,IAAA,eAAM,GAAE;gCAChC,IAAI,EAAE,aAAa,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE;gCACxC,SAAS,EAAE,EAAE;gCACb,SAAS,EAAE,MAAM;6BAClB,CAAC;wBACJ,CAAC;wBAED,IAAI,aAAa,CAAC,QAAQ,EAAE,IAAI,IAAI,eAAe,EAAE,CAAC;4BACpD,eAAe,CAAC,IAAI,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;wBACrD,CAAC;wBAED,IAAI,aAAa,CAAC,QAAQ,EAAE,SAAS,IAAI,eAAe,EAAE,CAAC;4BACzD,IAAI,CAAC;gCACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gCAC1D,eAAe,CAAC,SAAS,GAAG,EAAE,GAAG,eAAe,CAAC,SAAS,EAAE,GAAG,IAAI,EAAE,CAAC;4BACxE,CAAC;4BAAC,OAAO,KAAK,EAAE,CAAC;gCACf,sCAAsC;4BACxC,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,aAAa,KAAK,YAAY,IAAI,eAAe,EAAE,EAAE,EAAE,CAAC;oBAC5E,SAAS,CAAC,IAAI,CAAC,eAA2B,CAAC,CAAC;oBAC5C,eAAe,GAAG,IAAI,CAAC;gBACzB,CAAC;YACH,CAAC;YAED,4BAA4B;YAC5B,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC;gBACvC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;oBACjC,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,WAAW;wBACjB,OAAO,EAAE,aAAa,QAAQ,CAAC,IAAI,KAAK;wBACxC,QAAQ,EAAE,EAAE,QAAQ,EAAE;qBACvB,CAAC,CAAC;oBAEH,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,CAAC;oBAC1C,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAEzB,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,aAAa;wBACnB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,MAAM,CAAC,KAAK,EAAE;wBAClE,QAAQ,EAAE,EAAE,MAAM,EAAE;qBACrB,CAAC,CAAC;gBACL,CAAC;gBAED,uEAAuE;gBACvE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC3B,MAAM,WAAW,GAAwB;wBACvC,EAAE,EAAE,IAAA,eAAM,GAAE;wBACZ,IAAI,EAAE,WAAW;wBACjB,OAAO,EAAE,gBAAgB;wBACzB,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,SAAS;wBACT,WAAW;qBACZ,CAAC;oBAEF,2BAA2B;oBAC3B,MAAM,kBAAkB,GAA0B,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;wBAC3E,EAAE,EAAE,IAAA,eAAM,GAAE;wBACZ,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,MAAM,CAAC,KAAK,EAAE;wBAClE,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC,CAAC,CAAC;oBAEJ,qBAAqB;oBACrB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC;wBAChD,GAAG,cAAc;wBACjB,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;wBAChC,GAAG,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;qBAC3D,CAAC,CAAC;oBAEH,OAAO;wBACL,EAAE,EAAE,IAAA,eAAM,GAAE;wBACZ,IAAI,EAAE,WAAW;wBACjB,OAAO,EAAE,aAAa;wBACtB,SAAS,EAAE,IAAI,IAAI,EAAE;wBACrB,SAAS;wBACT,WAAW;qBACZ,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,gBAAgB;gBACzB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;gBACvD,WAAW,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;aAC9D,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,QAAe;QAC5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACzD,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,QAAQ;YACR,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;YACzC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI;SAC7C,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;IACrD,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAChD,MAAM,EAAE,gBAAgB,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;QAEtD,MAAM,YAAY,GAAG;;;uBAGF,gBAAgB,CAAC,IAAI;kBAC1B,gBAAgB,CAAC,IAAI;cACzB,gBAAgB,CAAC,QAAQ,IAAI,SAAS;eACrC,gBAAgB,CAAC,SAAS,IAAI,MAAM;qBAC9B,gBAAgB,CAAC,cAAc,IAAI,MAAM;cAChD,eAAe,CAAC,QAAQ;kBACpB,eAAe,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDA+BK,CAAC;QAE/C,OAAO;YACL,EAAE,EAAE,IAAA,eAAM,GAAE;YACZ,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,YAAY;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,QAA+B;QACrD,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;IACvD,CAAC;IAEO,cAAc,CAAC,OAA4B;QACjD,MAAM,SAAS,GAAQ;YACrB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC;QAEF,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,SAAS,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAClD,EAAE,EAAE,EAAE,CAAC,EAAE;gBACT,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,EAAE,CAAC,IAAI;oBACb,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC;iBACxC;aACF,CAAC,CAAC,CAAC;QACN,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,YAAY,CAAC,KAAY;QAC/B,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBAC9C,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;CACF;AA/UD,8CA+UC"}