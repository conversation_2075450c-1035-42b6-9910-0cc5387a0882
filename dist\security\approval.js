"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApprovalSystem = void 0;
const events_1 = require("events");
const inquirer_1 = __importDefault(require("inquirer"));
const chalk_1 = __importDefault(require("chalk"));
const logger_1 = require("../utils/logger");
class ApprovalSystem extends events_1.EventEmitter {
    mode;
    autoApprovedCommands = new Set();
    blockedCommands = new Set();
    constructor(mode = 'suggest') {
        super();
        this.mode = mode;
        this.initializeDefaultRules();
    }
    initializeDefaultRules() {
        // Commands that are generally safe to auto-approve
        const safeCommands = [
            'ls', 'dir', 'pwd', 'whoami', 'date', 'echo',
            'cat', 'head', 'tail', 'grep', 'find', 'locate',
            'ps', 'top', 'df', 'du', 'free', 'uptime',
            'git status', 'git log', 'git diff', 'git show',
            'npm list', 'npm outdated', 'npm audit',
            'node --version', 'npm --version', 'git --version',
        ];
        safeCommands.forEach(cmd => this.autoApprovedCommands.add(cmd));
        // Commands that should always be blocked or require explicit approval
        const dangerousCommands = [
            'rm -rf /', 'rm -rf *', 'format', 'fdisk',
            'dd if=', 'mkfs', 'shutdown', 'reboot', 'halt',
            'del /s', 'rmdir /s', 'format c:',
        ];
        dangerousCommands.forEach(cmd => this.blockedCommands.add(cmd));
    }
    setMode(mode) {
        this.mode = mode;
        logger_1.logger.info('Approval mode changed', { mode });
    }
    getMode() {
        return this.mode;
    }
    async requestApproval(toolCall, riskAssessment) {
        const request = {
            toolCall,
            riskAssessment,
            timestamp: new Date(),
        };
        this.emit('approval-required', request);
        // Check if command is blocked
        if (this.isCommandBlocked(toolCall)) {
            logger_1.logger.warn('Command blocked by security policy', {
                tool: toolCall.name,
                command: this.getCommandString(toolCall)
            });
            return false;
        }
        // Auto-approve based on mode and risk level
        if (this.shouldAutoApprove(toolCall, riskAssessment)) {
            logger_1.logger.debug('Command auto-approved', {
                tool: toolCall.name,
                riskLevel: riskAssessment.level
            });
            return true;
        }
        // Request user approval
        return await this.promptUserApproval(request);
    }
    shouldAutoApprove(toolCall, riskAssessment) {
        switch (this.mode) {
            case 'full-auto':
                return riskAssessment.level !== 'critical';
            case 'auto-edit':
                // Auto-approve file operations and safe commands
                if (toolCall.name.startsWith('file_') && riskAssessment.level === 'safe') {
                    return true;
                }
                return this.isCommandAutoApproved(toolCall) && riskAssessment.level === 'safe';
            case 'suggest':
            default:
                return false; // Always ask for approval
        }
    }
    async promptUserApproval(request) {
        const { toolCall, riskAssessment } = request;
        console.log('\n' + chalk_1.default.yellow('🔒 Approval Required'));
        console.log(chalk_1.default.cyan('Tool:'), toolCall.name);
        console.log(chalk_1.default.cyan('Risk Level:'), this.formatRiskLevel(riskAssessment.level));
        if (toolCall.name === 'shell') {
            console.log(chalk_1.default.cyan('Command:'), toolCall.arguments.command);
            if (toolCall.arguments.args && Array.isArray(toolCall.arguments.args)) {
                console.log(chalk_1.default.cyan('Arguments:'), toolCall.arguments.args.join(' '));
            }
        }
        else {
            console.log(chalk_1.default.cyan('Arguments:'), JSON.stringify(toolCall.arguments, null, 2));
        }
        if (riskAssessment.reasons.length > 0) {
            console.log(chalk_1.default.yellow('Risk Factors:'));
            riskAssessment.reasons.forEach(reason => {
                console.log(chalk_1.default.yellow('  • '), reason);
            });
        }
        if (riskAssessment.suggestions.length > 0) {
            console.log(chalk_1.default.blue('Suggestions:'));
            riskAssessment.suggestions.forEach(suggestion => {
                console.log(chalk_1.default.blue('  • '), suggestion);
            });
        }
        const choices = [
            { name: 'Approve', value: 'approve' },
            { name: 'Deny', value: 'deny' },
            { name: 'Approve and remember for this session', value: 'approve-remember' },
        ];
        if (riskAssessment.level === 'critical') {
            choices.unshift({ name: 'Cancel (recommended for critical risk)', value: 'deny' });
        }
        const answer = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'decision',
                message: 'Do you want to proceed?',
                choices,
                default: riskAssessment.level === 'critical' ? 'deny' : 'approve',
            },
        ]);
        const approved = answer.decision === 'approve' || answer.decision === 'approve-remember';
        if (answer.decision === 'approve-remember') {
            this.rememberApproval(toolCall);
        }
        logger_1.logger.info('User approval decision', {
            tool: toolCall.name,
            approved,
            riskLevel: riskAssessment.level
        });
        return approved;
    }
    rememberApproval(toolCall) {
        const commandKey = this.getCommandKey(toolCall);
        this.autoApprovedCommands.add(commandKey);
        logger_1.logger.debug('Command remembered for auto-approval', { command: commandKey });
    }
    isCommandBlocked(toolCall) {
        const commandString = this.getCommandString(toolCall);
        for (const blocked of this.blockedCommands) {
            if (commandString.toLowerCase().includes(blocked.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
    isCommandAutoApproved(toolCall) {
        const commandKey = this.getCommandKey(toolCall);
        const commandString = this.getCommandString(toolCall);
        // Check exact match
        if (this.autoApprovedCommands.has(commandKey)) {
            return true;
        }
        // Check partial matches for known safe commands
        for (const approved of this.autoApprovedCommands) {
            if (commandString.toLowerCase().startsWith(approved.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
    getCommandKey(toolCall) {
        if (toolCall.name === 'shell') {
            const command = toolCall.arguments.command;
            const args = toolCall.arguments.args || [];
            return `${command} ${args.join(' ')}`.trim();
        }
        return `${toolCall.name}:${JSON.stringify(toolCall.arguments)}`;
    }
    getCommandString(toolCall) {
        if (toolCall.name === 'shell') {
            const command = toolCall.arguments.command;
            const args = toolCall.arguments.args || [];
            return `${command} ${args.join(' ')}`.trim();
        }
        return toolCall.name;
    }
    formatRiskLevel(level) {
        const colors = {
            safe: chalk_1.default.green,
            low: chalk_1.default.blue,
            medium: chalk_1.default.yellow,
            high: chalk_1.default.red,
            critical: chalk_1.default.bgRed.white,
        };
        return colors[level](level.toUpperCase());
    }
    // Configuration methods
    addAutoApprovedCommand(command) {
        this.autoApprovedCommands.add(command);
        logger_1.logger.debug('Command added to auto-approved list', { command });
    }
    removeAutoApprovedCommand(command) {
        this.autoApprovedCommands.delete(command);
        logger_1.logger.debug('Command removed from auto-approved list', { command });
    }
    addBlockedCommand(command) {
        this.blockedCommands.add(command);
        logger_1.logger.debug('Command added to blocked list', { command });
    }
    removeBlockedCommand(command) {
        this.blockedCommands.delete(command);
        logger_1.logger.debug('Command removed from blocked list', { command });
    }
    getAutoApprovedCommands() {
        return Array.from(this.autoApprovedCommands);
    }
    getBlockedCommands() {
        return Array.from(this.blockedCommands);
    }
    clearSessionApprovals() {
        // Remove session-specific approvals (keep default ones)
        const defaultCommands = new Set([
            'ls', 'dir', 'pwd', 'whoami', 'date', 'echo',
            'cat', 'head', 'tail', 'grep', 'find', 'locate',
            'ps', 'top', 'df', 'du', 'free', 'uptime',
            'git status', 'git log', 'git diff', 'git show',
            'npm list', 'npm outdated', 'npm audit',
            'node --version', 'npm --version', 'git --version',
        ]);
        this.autoApprovedCommands = defaultCommands;
        logger_1.logger.info('Session approvals cleared');
    }
    // Statistics and monitoring
    getApprovalStats() {
        return {
            mode: this.mode,
            autoApprovedCount: this.autoApprovedCommands.size,
            blockedCount: this.blockedCommands.size,
        };
    }
}
exports.ApprovalSystem = ApprovalSystem;
//# sourceMappingURL=approval.js.map