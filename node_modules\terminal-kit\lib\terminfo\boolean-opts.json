["autoLeftMargin", "autoRightMargin", "noEscCtlc", "ceolStandoutGlitch", "eatNewlineGlitch", "eraseOverstrike", "genericType", "hardCopy", "hasMetaKey", "hasStatusLine", "insert<PERSON>ull<PERSON>litch", "memoryAbove", "memoryBelow", "moveInsertMode", "moveStandoutMode", "overStrike", "statusLineEscOk", "destTabsMagicSmso", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transparentUnderline", "xonXoff", "needsXonXoff", "prtr<PERSON><PERSON>nt", "hardCursor", "nonRevRmcup", "noPadChar", "nonDestScrollRegion", "canChange", "backColorErase", "hueLightnessSaturation", "colAddrGlitch", "crCancelsMicroMode", "hasPrintWheel", "rowAddrGlitch", "semiAutoRightMargin", "cpiChangesRes", "lpiChangesRes"]