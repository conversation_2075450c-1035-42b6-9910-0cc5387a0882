"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityError = exports.ToolError = exports.AgentError = exports.ConfigSchema = void 0;
const zod_1 = require("zod");
// Configuration Schemas
exports.ConfigSchema = zod_1.z.object({
    provider: zod_1.z.enum(['openai', 'deepseek', 'ollama', 'azure']),
    model: zod_1.z.string(),
    apiKey: zod_1.z.string().optional(),
    baseUrl: zod_1.z.string().url().optional(),
    maxTokens: zod_1.z.number().positive().optional(),
    temperature: zod_1.z.number().min(0).max(2).optional(),
    approvalMode: zod_1.z.enum(['suggest', 'auto-edit', 'full-auto']),
    workingDirectory: zod_1.z.string(),
    security: zod_1.z.object({
        allowedCommands: zod_1.z.array(zod_1.z.string()).optional(),
        blockedCommands: zod_1.z.array(zod_1.z.string()).optional(),
        maxExecutionTime: zod_1.z.number().positive().optional(),
        sandboxMode: zod_1.z.boolean().optional(),
    }).optional(),
});
// Error Types
class AgentError extends Error {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'AgentError';
    }
}
exports.AgentError = AgentError;
class ToolError extends AgentError {
    toolName;
    constructor(message, toolName, details) {
        super(message, 'TOOL_ERROR', { toolName, ...details });
        this.toolName = toolName;
        this.name = 'ToolError';
    }
}
exports.ToolError = ToolError;
class SecurityError extends AgentError {
    riskLevel;
    constructor(message, riskLevel, details) {
        super(message, 'SECURITY_ERROR', { riskLevel, ...details });
        this.riskLevel = riskLevel;
        this.name = 'SecurityError';
    }
}
exports.SecurityError = SecurityError;
//# sourceMappingURL=index.js.map