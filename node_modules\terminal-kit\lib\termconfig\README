

<terminal>.js refers to the real terminal application, e.g. gnome-terminal, konsole, ...

<terminal>.generic.js refers to a fail-safe version of a terminal used when we got the terminal identifier
using the $TERM environment variable and it happens that this identifier is shared with not-so-compliant terminal,
e.g. xterm-256color.

terminfo.js contains for each method the terminfo name of the capability.
