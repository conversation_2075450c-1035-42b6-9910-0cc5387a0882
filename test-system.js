#!/usr/bin/env node

/**
 * Comprehensive test script for Kritrima AI CLI
 * Tests all major components and functionalities
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class SystemTester {
  constructor() {
    this.testResults = [];
    this.cliPath = path.join(__dirname, 'dist', 'index.js');
  }

  async runTest(name, testFn) {
    console.log(`\n🧪 Running test: ${name}`);
    try {
      await testFn();
      console.log(`✅ ${name} - PASSED`);
      this.testResults.push({ name, status: 'PASSED' });
    } catch (error) {
      console.error(`❌ ${name} - FAILED: ${error.message}`);
      this.testResults.push({ name, status: 'FAILED', error: error.message });
    }
  }

  async execCLI(args, input = null) {
    return new Promise((resolve, reject) => {
      const child = spawn('node', [this.cliPath, ...args], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        resolve({ code, stdout, stderr });
      });

      child.on('error', (error) => {
        reject(error);
      });

      if (input) {
        child.stdin.write(input);
        child.stdin.end();
      }
    });
  }

  async testBuildExists() {
    if (!fs.existsSync(this.cliPath)) {
      throw new Error('CLI build not found. Run npm run build first.');
    }
  }

  async testCLIHelp() {
    const result = await this.execCLI(['--help']);
    if (result.code !== 0) {
      throw new Error(`CLI help failed with code ${result.code}`);
    }
    if (!result.stdout.includes('Kritrima')) {
      throw new Error('Help output does not contain expected content');
    }
  }

  async testConfigShow() {
    const result = await this.execCLI(['config', 'show']);
    // Should work even without config file
    if (result.code !== 0 && !result.stderr.includes('Configuration file not found')) {
      throw new Error(`Config show failed unexpectedly: ${result.stderr}`);
    }
  }

  async testConfigValidation() {
    const result = await this.execCLI(['config', 'validate']);
    // Should handle missing config gracefully
    if (result.code !== 0 && !result.stderr.includes('Configuration file not found')) {
      throw new Error(`Config validation failed unexpectedly: ${result.stderr}`);
    }
  }

  async testSessionList() {
    const result = await this.execCLI(['session', 'list']);
    // Should work even with no sessions
    if (result.code !== 0 && !result.stdout.includes('No sessions found')) {
      throw new Error(`Session list failed: ${result.stderr}`);
    }
  }

  async testVersion() {
    const result = await this.execCLI(['version']);
    if (result.code !== 0) {
      throw new Error(`Version command failed with code ${result.code}`);
    }
    if (!result.stdout.includes('Kritrima AI CLI')) {
      throw new Error('Version output does not contain expected content');
    }
  }

  async testFileOperations() {
    // Test file creation
    const testFile = path.join(__dirname, 'test-temp.txt');
    const testContent = 'Hello, Kritrima!';
    
    // Clean up any existing test file
    if (fs.existsSync(testFile)) {
      fs.unlinkSync(testFile);
    }

    // Create test file
    fs.writeFileSync(testFile, testContent);
    
    // Verify file exists
    if (!fs.existsSync(testFile)) {
      throw new Error('Test file creation failed');
    }

    // Verify content
    const content = fs.readFileSync(testFile, 'utf-8');
    if (content !== testContent) {
      throw new Error('Test file content mismatch');
    }

    // Clean up
    fs.unlinkSync(testFile);
  }

  async testPackageJson() {
    const packagePath = path.join(__dirname, 'package.json');
    if (!fs.existsSync(packagePath)) {
      throw new Error('package.json not found');
    }

    const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf-8'));
    if (pkg.name !== 'kritrima-ai-cli') {
      throw new Error('Package name mismatch');
    }

    if (!pkg.dependencies || !pkg.dependencies.openai) {
      throw new Error('OpenAI dependency not found');
    }
  }

  async testTypeScriptConfig() {
    const tsconfigPath = path.join(__dirname, 'tsconfig.json');
    if (!fs.existsSync(tsconfigPath)) {
      throw new Error('tsconfig.json not found');
    }

    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf-8'));
    if (!tsconfig.compilerOptions) {
      throw new Error('TypeScript compiler options not found');
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Kritrima AI CLI System Tests\n');

    await this.runTest('Build Exists', () => this.testBuildExists());
    await this.runTest('CLI Help', () => this.testCLIHelp());
    await this.runTest('Config Show', () => this.testConfigShow());
    await this.runTest('Config Validation', () => this.testConfigValidation());
    await this.runTest('Session List', () => this.testSessionList());
    await this.runTest('Version Command', () => this.testVersion());
    await this.runTest('File Operations', () => this.testFileOperations());
    await this.runTest('Package.json', () => this.testPackageJson());
    await this.runTest('TypeScript Config', () => this.testTypeScriptConfig());

    this.printSummary();
  }

  printSummary() {
    console.log('\n📊 Test Summary');
    console.log('================');
    
    const passed = this.testResults.filter(r => r.status === 'PASSED').length;
    const failed = this.testResults.filter(r => r.status === 'FAILED').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Total: ${this.testResults.length}`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => r.status === 'FAILED')
        .forEach(r => console.log(`  • ${r.name}: ${r.error}`));
    }

    console.log(`\n🎯 Success Rate: ${Math.round((passed / this.testResults.length) * 100)}%`);
    
    if (failed === 0) {
      console.log('\n🎉 All tests passed! The system is ready for use.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the errors above.');
      process.exit(1);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new SystemTester();
  tester.runAllTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = SystemTester;
