"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.configManager = exports.ConfigManager = void 0;
exports.loadConfig = loadConfig;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const os_1 = __importDefault(require("os"));
const dotenv_1 = require("dotenv");
const yaml_1 = __importDefault(require("yaml"));
const zod_1 = require("zod");
const logger_1 = require("./logger");
// Load environment variables
(0, dotenv_1.config)();
// Configuration schema
const ConfigSchema = zod_1.z.object({
    provider: zod_1.z.enum(['openai', 'deepseek', 'ollama', 'azure']).default('openai'),
    model: zod_1.z.string().default('gpt-4'),
    apiKey: zod_1.z.string().optional(),
    baseUrl: zod_1.z.string().url().optional(),
    maxTokens: zod_1.z.number().positive().default(4096),
    temperature: zod_1.z.number().min(0).max(2).default(0.7),
    approvalMode: zod_1.z.enum(['suggest', 'auto-edit', 'full-auto']).default('suggest'),
    workingDirectory: zod_1.z.string().default(process.cwd()),
    // Security settings
    security: zod_1.z.object({
        allowedCommands: zod_1.z.array(zod_1.z.string()).optional(),
        blockedCommands: zod_1.z.array(zod_1.z.string()).optional(),
        allowedPaths: zod_1.z.array(zod_1.z.string()).optional(),
        blockedPaths: zod_1.z.array(zod_1.z.string()).optional(),
        maxExecutionTime: zod_1.z.number().positive().default(30000),
        sandboxMode: zod_1.z.boolean().default(false),
    }).optional(),
    // Logging settings
    logging: zod_1.z.object({
        level: zod_1.z.enum(['error', 'warn', 'info', 'debug']).default('info'),
        console: zod_1.z.boolean().default(false),
        file: zod_1.z.boolean().default(true),
    }).optional(),
    // UI settings
    ui: zod_1.z.object({
        interactive: zod_1.z.boolean().default(true),
        verbose: zod_1.z.boolean().default(false),
        quiet: zod_1.z.boolean().default(false),
        theme: zod_1.z.enum(['default', 'dark', 'light']).default('default'),
    }).optional(),
    // Session settings
    session: zod_1.z.object({
        autoSave: zod_1.z.boolean().default(true),
        maxSessions: zod_1.z.number().positive().default(100),
        cleanupDays: zod_1.z.number().positive().default(30),
    }).optional(),
});
class ConfigManager {
    configPath;
    config = null;
    constructor(configPath) {
        this.configPath = configPath || this.getDefaultConfigPath();
    }
    getDefaultConfigPath() {
        const configDir = path_1.default.join(os_1.default.homedir(), '.kritrima');
        return path_1.default.join(configDir, 'config.yaml');
    }
    async load() {
        try {
            // Ensure config directory exists
            await promises_1.default.mkdir(path_1.default.dirname(this.configPath), { recursive: true });
            // Try to load existing config
            try {
                const configContent = await promises_1.default.readFile(this.configPath, 'utf-8');
                const rawConfig = yaml_1.default.parse(configContent);
                this.config = ConfigSchema.parse(rawConfig);
                logger_1.logger.info('Configuration loaded', { path: this.configPath });
            }
            catch (error) {
                // Config file doesn't exist or is invalid, create default
                logger_1.logger.info('Creating default configuration', { path: this.configPath });
                this.config = this.createDefaultConfig();
                await this.save();
            }
            // Override with environment variables
            this.applyEnvironmentOverrides();
            return this.config;
        }
        catch (error) {
            logger_1.logger.error('Failed to load configuration', error);
            throw error;
        }
    }
    createDefaultConfig() {
        return ConfigSchema.parse({
            provider: 'openai',
            model: 'gpt-4',
            approvalMode: 'suggest',
            workingDirectory: process.cwd(),
        });
    }
    applyEnvironmentOverrides() {
        if (!this.config)
            return;
        // AI Provider settings
        if (process.env['KRITRIMA_PROVIDER']) {
            this.config.provider = process.env['KRITRIMA_PROVIDER'];
        }
        if (process.env['KRITRIMA_MODEL']) {
            this.config.model = process.env['KRITRIMA_MODEL'];
        }
        if (process.env['KRITRIMA_API_KEY'] || process.env['OPENAI_API_KEY']) {
            this.config.apiKey = process.env['KRITRIMA_API_KEY'] || process.env['OPENAI_API_KEY'];
        }
        if (process.env['KRITRIMA_BASE_URL']) {
            this.config.baseUrl = process.env['KRITRIMA_BASE_URL'];
        }
        if (process.env['KRITRIMA_MAX_TOKENS']) {
            this.config.maxTokens = parseInt(process.env['KRITRIMA_MAX_TOKENS']);
        }
        if (process.env['KRITRIMA_TEMPERATURE']) {
            this.config.temperature = parseFloat(process.env['KRITRIMA_TEMPERATURE']);
        }
        if (process.env['KRITRIMA_APPROVAL_MODE']) {
            this.config.approvalMode = process.env['KRITRIMA_APPROVAL_MODE'];
        }
        if (process.env['KRITRIMA_WORKING_DIR']) {
            this.config.workingDirectory = process.env['KRITRIMA_WORKING_DIR'];
        }
        // Logging settings
        if (process.env['LOG_LEVEL']) {
            this.config.logging = this.config.logging || {
                level: 'info',
                console: false,
                file: true,
            };
            this.config.logging.level = process.env['LOG_LEVEL'];
        }
        if (process.env['LOG_CONSOLE']) {
            this.config.logging = this.config.logging || {
                level: 'info',
                console: false,
                file: true,
            };
            this.config.logging.console = process.env['LOG_CONSOLE'] === 'true';
        }
        // Provider-specific API keys
        if (process.env['DEEPSEEK_API_KEY'] && this.config.provider === 'deepseek') {
            this.config.apiKey = process.env['DEEPSEEK_API_KEY'];
        }
        if (process.env['AZURE_OPENAI_API_KEY'] && this.config.provider === 'azure') {
            this.config.apiKey = process.env['AZURE_OPENAI_API_KEY'];
        }
    }
    async save() {
        if (!this.config) {
            throw new Error('No configuration to save');
        }
        try {
            const configContent = yaml_1.default.stringify(this.config, {
                indent: 2,
                lineWidth: 100,
            });
            await promises_1.default.writeFile(this.configPath, configContent, 'utf-8');
            logger_1.logger.info('Configuration saved', { path: this.configPath });
        }
        catch (error) {
            logger_1.logger.error('Failed to save configuration', error);
            throw error;
        }
    }
    async update(updates) {
        if (!this.config) {
            throw new Error('Configuration not loaded');
        }
        // Validate updates
        const updatedConfig = { ...this.config, ...updates };
        this.config = ConfigSchema.parse(updatedConfig);
        await this.save();
        logger_1.logger.info('Configuration updated', { updates });
    }
    get() {
        if (!this.config) {
            throw new Error('Configuration not loaded');
        }
        return { ...this.config };
    }
    toAgentConfig() {
        if (!this.config) {
            throw new Error('Configuration not loaded');
        }
        return {
            provider: this.config.provider,
            model: this.config.model,
            apiKey: this.config.apiKey,
            baseUrl: this.config.baseUrl,
            maxTokens: this.config.maxTokens,
            temperature: this.config.temperature,
            approvalMode: this.config.approvalMode,
            workingDirectory: this.config.workingDirectory,
        };
    }
    // Utility methods for specific configurations
    getProviderConfig() {
        if (!this.config)
            return null;
        return {
            provider: this.config.provider,
            model: this.config.model,
            apiKey: this.config.apiKey,
            baseUrl: this.config.baseUrl,
        };
    }
    getSecurityConfig() {
        return this.config?.security || {};
    }
    getLoggingConfig() {
        return this.config?.logging || {};
    }
    getUIConfig() {
        return this.config?.ui || {};
    }
    getSessionConfig() {
        return this.config?.session || {};
    }
    // Validation methods
    async validateConfig() {
        const errors = [];
        if (!this.config) {
            errors.push('Configuration not loaded');
            return { valid: false, errors };
        }
        // Validate API key for providers that require it
        if (['openai', 'deepseek', 'azure'].includes(this.config.provider) && !this.config.apiKey) {
            errors.push(`API key required for provider: ${this.config.provider}`);
        }
        // Validate base URL for Azure
        if (this.config.provider === 'azure' && !this.config.baseUrl) {
            errors.push('Base URL required for Azure provider');
        }
        // Validate working directory
        try {
            await promises_1.default.access(this.config.workingDirectory);
        }
        catch (error) {
            errors.push(`Working directory not accessible: ${this.config.workingDirectory}`);
        }
        // Validate model name format
        if (!this.config.model || this.config.model.trim().length === 0) {
            errors.push('Model name cannot be empty');
        }
        return { valid: errors.length === 0, errors };
    }
    // Provider-specific configurations
    static getProviderDefaults(provider) {
        switch (provider) {
            case 'openai':
                return {
                    provider: 'openai',
                    model: 'gpt-4',
                    baseUrl: undefined,
                };
            case 'deepseek':
                return {
                    provider: 'deepseek',
                    model: 'deepseek-chat',
                    baseUrl: 'https://api.deepseek.com/v1',
                };
            case 'ollama':
                return {
                    provider: 'ollama',
                    model: 'llama2',
                    baseUrl: 'http://localhost:11434/v1',
                    apiKey: 'ollama',
                };
            case 'azure':
                return {
                    provider: 'azure',
                    model: 'gpt-4',
                    // baseUrl and apiKey must be provided by user
                };
            default:
                return {};
        }
    }
    // Configuration templates
    static createTemplate(provider) {
        const defaults = ConfigManager.getProviderDefaults(provider);
        return ConfigSchema.parse({
            ...defaults,
            approvalMode: 'suggest',
            workingDirectory: process.cwd(),
        });
    }
    // Export configuration for backup
    async exportConfig() {
        if (!this.config) {
            throw new Error('Configuration not loaded');
        }
        return yaml_1.default.stringify({
            ...this.config,
            // Remove sensitive information
            apiKey: this.config.apiKey ? '[REDACTED]' : undefined,
        }, { indent: 2 });
    }
    // Import configuration from backup
    async importConfig(configYaml) {
        try {
            const rawConfig = yaml_1.default.parse(configYaml);
            this.config = ConfigSchema.parse(rawConfig);
            await this.save();
            logger_1.logger.info('Configuration imported successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to import configuration', error);
            throw error;
        }
    }
}
exports.ConfigManager = ConfigManager;
// Global config manager instance
exports.configManager = new ConfigManager();
// Convenience function to load configuration
async function loadConfig(configPath) {
    const manager = configPath ? new ConfigManager(configPath) : exports.configManager;
    await manager.load();
    return manager.toAgentConfig();
}
//# sourceMappingURL=config.js.map