{"names": ["win32", "non-standard windows profile (node.js)", "libuv profile"], "columns": 80, "initTabs": 8, "lines": 24, "maxColors": 8, "maxPairs": 64, "backTab": "\u001b[Z", "carriageReturn": "\r", "clearScreen": "\u001b[H\u001b[2J", "clrEol": "\u001b[K", "clrEos": "\u001b[J", "cursorDown": "\u001b[B", "cursorHome": "\u001b[H", "cursorInvisible": "\u001b[?25l", "cursorLeft": "\u001b[D", "cursorRight": "\u001b[C", "cursorUp": "\u001b[A", "cursorVisible": "\u001b[?25h", "deleteCharacter": "\u001b[P", "deleteLine": "\u001b[M", "enterAltCharsetMode": "\u001b(0", "enterBoldMode": "\u001b[1m", "enterCaMode": "\u001b[?1049h", "enterInsertMode": "\u001b[4h", "enterReverseMode": "\u001b[7m", "enterStandoutMode": "\u001b[7m", "eraseChars": "\u001b[%p1%dX", "exitAltCharsetMode": "\u001b(B", "exitAttributeMode": "\u001b(B\u001b[m", "exitCaMode": "\u001b[?1049l", "exitInsertMode": "\u001b[4l", "exitStandoutMode": "\u001b[27m", "insertLine": "\u001b[L", "keyBackspace": "\b", "keyDc": "\u001b[3~", "keyDown": "\u001bOB", "keyF1": "\u001bOP", "keyF10": "\u001b[21~", "keyF2": "\u001bOQ", "keyF3": "\u001bOR", "keyF4": "\u001bOS", "keyF5": "\u001b[15~", "keyF6": "\u001b[17~", "keyF7": "\u001b[18~", "keyF8": "\u001b[19~", "keyF9": "\u001b[20~", "keyHome": "\u001bOH", "keyIc": "\u001b[2~", "keyLeft": "\u001bOD", "keyNpage": "\u001b[6~", "keyPpage": "\u001b[5~", "keyRight": "\u001bOC", "keySf": "\u001b[1;2B", "keySr": "\u001b[1;2A", "keyUp": "\u001bOA", "keypadLocal": "\u001b[?1l\u001b>", "keypadXmit": "\u001b[?1h\u001b=", "metaOff": "\u001b[?1034l", "metaOn": "\u001b[?1034h", "parmDch": "\u001b[%p1%dP", "parmDeleteLine": "\u001b[%p1%dM", "parmDownCursor": "\u001b[%p1%dB", "parmIch": "\u001b[%p1%d@", "parmIndex": "\u001b[%p1%dS", "parmInsertLine": "\u001b[%p1%dL", "parmLeftCursor": "\u001b[%p1%dD", "parmRightCursor": "\u001b[%p1%dC", "parmRindex": "\u001b[%p1%dT", "parmUpCursor": "\u001b[%p1%dA", "printScreen": "\u001b[i", "prtrOff": "\u001b[4i", "prtrOn": "\u001b[5i", "reset1String": "\u001bc", "reset2String": "\u001b[!p\u001b[?3;4l\u001b[4l\u001b>", "scrollForward": "\n", "scrollReverse": "\u001bM", "tab": "\t", "keyB2": "\u001bOE", "acsChars": "``aaffggiijjkkllmmnnooppqqrrssttuuvvwwxxyyzz{{||}}~~", "keyBtab": "\u001b[Z", "enterAmMode": "\u001b[?7h", "exitAmMode": "\u001b[?7l", "keyEnd": "\u001bOF", "keyEnter": "\u001bOM", "keySdc": "\u001b[3;2~", "keySend": "\u001b[1;2F", "keyShome": "\u001b[1;2H", "keySic": "\u001b[2;2~", "keySleft": "\u001b[1;2D", "keySnext": "\u001b[6;2~", "keySprevious": "\u001b[5;2~", "keySright": "\u001b[1;2C", "keyF11": "\u001b[23~", "keyF12": "\u001b[24~", "keyF13": "\u001b[1;2P", "keyF14": "\u001b[1;2Q", "keyF15": "\u001b[1;2R", "keyF16": "\u001b[1;2S", "keyF17": "\u001b[15;2~", "keyF18": "\u001b[17;2~", "keyF19": "\u001b[18;2~", "keyF20": "\u001b[19;2~", "keyF21": "\u001b[20;2~", "keyF22": "\u001b[21;2~", "keyF23": "\u001b[23;2~", "keyF24": "\u001b[24;2~", "keyF25": "\u001b[1;5P", "keyF26": "\u001b[1;5Q", "keyF27": "\u001b[1;5R", "keyF28": "\u001b[1;5S", "keyF29": "\u001b[15;5~", "keyF30": "\u001b[17;5~", "keyF31": "\u001b[18;5~", "keyF32": "\u001b[19;5~", "keyF33": "\u001b[20;5~", "keyF34": "\u001b[21;5~", "keyF35": "\u001b[23;5~", "keyF36": "\u001b[24;5~", "keyF37": "\u001b[1;6P", "keyF38": "\u001b[1;6Q", "keyF39": "\u001b[1;6R", "keyF40": "\u001b[1;6S", "keyF41": "\u001b[15;6~", "keyF42": "\u001b[17;6~", "keyF43": "\u001b[18;6~", "keyF44": "\u001b[19;6~", "keyF45": "\u001b[20;6~", "keyF46": "\u001b[21;6~", "keyF47": "\u001b[23;6~", "keyF48": "\u001b[24;6~", "keyF49": "\u001b[1;3P", "keyF50": "\u001b[1;3Q", "keyF51": "\u001b[1;3R", "keyF52": "\u001b[1;3S", "keyF53": "\u001b[15;3~", "keyF54": "\u001b[17;3~", "keyF55": "\u001b[18;3~", "keyF56": "\u001b[19;3~", "keyF57": "\u001b[20;3~", "keyF58": "\u001b[21;3~", "keyF59": "\u001b[23;3~", "keyF60": "\u001b[24;3~", "keyF61": "\u001b[1;4P", "keyF62": "\u001b[1;4Q", "keyF63": "\u001b[1;4R", "clrBol": "\u001b[1K", "user6": "\u001b[%i%d;%dR", "user7": "\u001b[6n", "user8": "\u001b[?1;2c", "user9": "\u001b[c", "origPair": "\u001b[39;49m", "setForeground": "\u001b[3%?%p1%{1}%=%t4%e%p1%{3}%=%t6%e%p1%{4}%=%t1%e%p1%{6}%=%t3%e%p1%d%;m", "setBackground": "\u001b[4%?%p1%{1}%=%t4%e%p1%{3}%=%t6%e%p1%{4}%=%t1%e%p1%{6}%=%t3%e%p1%d%;m", "setAForeground": "\u001b[3%p1%dm", "setABackground": "\u001b[4%p1%dm"}