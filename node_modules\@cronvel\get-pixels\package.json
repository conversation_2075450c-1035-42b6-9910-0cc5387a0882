{"name": "@cronvel/get-pixels", "version": "3.4.1", "description": "Pruned version of the 'get-pixels' package by <PERSON><PERSON><PERSON> (only node.js and local fs files)", "main": "node-pixels.js", "dependencies": {"jpeg-js": "^0.4.4", "ndarray": "^1.0.19", "ndarray-pack": "^1.1.1", "node-bitmap": "0.0.1", "omggif": "^1.0.10", "pngjs": "^6.0.0"}, "repository": {"type": "git", "url": "git://github.com/cronvel/get-pixels.git"}, "keywords": ["n<PERSON><PERSON>", "pixel", "get", "read", "pixel", "image", "png", "jpeg", "jpg", "jpe", "gif", "decode", "buffer", "data", "parse", "node"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "readmeFilename": "README.md"}