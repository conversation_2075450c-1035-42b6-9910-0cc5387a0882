# Kritrima AI CLI - Example Usage

This document provides comprehensive examples of using the Kritrima AI CLI system.

## Basic Setup

```bash
# Build the project
npm run build

# Test the system
npm test

# Show help
node dist/index.js --help
```

## Configuration Examples

### OpenAI Setup
```bash
# Initialize OpenAI configuration
node dist/index.js config init openai

# Set API key
node dist/index.js config set apiKey "sk-your-openai-key"

# Configure model
node dist/index.js config set model "gpt-4"

# Set approval mode
node dist/index.js config set approvalMode "suggest"
```

### Deepseek Setup
```bash
# Initialize Deepseek configuration
node dist/index.js config init deepseek

# Set API key and base URL
node dist/index.js config set apiKey "sk-your-deepseek-key"
node dist/index.js config set baseUrl "https://api.deepseek.com/v1"
node dist/index.js config set model "deepseek-chat"
```

### Ollama (Local) Setup
```bash
# Initialize Ollama configuration
node dist/index.js config init ollama

# Configure for local Ollama instance
node dist/index.js config set baseUrl "http://localhost:11434/v1"
node dist/index.js config set model "llama2"
node dist/index.js config set apiKey "ollama"
```

## Interactive Mode Examples

### Starting Interactive Mode
```bash
node dist/index.js chat
```

### Example Conversations

#### 1. Project Analysis
```
kritrima> Show me the current project structure

🔧 Analyzing project structure...
✅ Detected Node.js TypeScript project

📁 Current Context:
Working Directory: /path/to/project
Project Type: node
Language: TypeScript
Framework: None
Package Manager: npm
Platform: win32
Node Version: v20.1.0
Files Indexed: 45
Dependencies: 12
Available Scripts: build, test, dev, lint
```

#### 2. File Operations
```
kritrima> Create a new TypeScript interface for a User

🔧 Creating TypeScript interface...
✅ Created src/types/User.ts

interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

🔧 Adding to index exports...
✅ Updated src/types/index.ts
```

#### 3. Development Tasks
```
kritrima> Install express and create a basic server

🔧 Installing dependencies...
✅ npm install express @types/express

🔧 Creating server file...
✅ Created src/server.ts

🔧 Adding start script...
✅ Updated package.json scripts

🎉 Express server setup complete!
Run: npm start
```

#### 4. System Information
```
kritrima> Show me system resources and running processes

🔧 Gathering system information...
✅ System Info:
- OS: Windows 11
- CPU: Intel i7-12700K (16 cores)
- Memory: 32 GB (18 GB free)
- Disk: 1 TB SSD (450 GB free)

🔧 Checking Node.js processes...
✅ Found 3 Node.js processes:
- node.exe (PID: 1234) - 150 MB
- node.exe (PID: 5678) - 89 MB  
- node.exe (PID: 9012) - 45 MB
```

## Single Command Examples

### File Operations
```bash
# Read a file
node dist/index.js exec "Show me the contents of package.json"

# Search for files
node dist/index.js exec "Find all TypeScript files in the src directory"

# Create a file
node dist/index.js exec "Create a README.md file with project description"
```

### Development Tasks
```bash
# Install dependencies
node dist/index.js exec "Install lodash and its types"

# Run tests
node dist/index.js exec "Run the test suite and show results"

# Build project
node dist/index.js exec "Build the project and check for errors"
```

### System Commands
```bash
# Check Git status
node dist/index.js exec "Show git status and recent commits"

# Monitor resources
node dist/index.js exec "Show current CPU and memory usage"

# Network information
node dist/index.js exec "Display network interfaces and connectivity"
```

## Advanced Features

### Tool Chaining Example
```
kritrima> Set up a complete React TypeScript project with testing

The AI will automatically:
1. 🔧 Initialize npm project
2. 🔧 Install React, TypeScript, and testing dependencies
3. 🔧 Create tsconfig.json
4. 🔧 Set up project structure (src/, public/, tests/)
5. 🔧 Create basic App component
6. 🔧 Configure Jest for testing
7. 🔧 Add npm scripts
8. ✅ Project setup complete!
```

### Context-Aware Operations
```
kritrima> Add error handling to the current project

🔧 Analyzing existing code...
✅ Found 15 TypeScript files
🔧 Identifying error-prone areas...
✅ Found 8 functions without error handling
🔧 Adding try-catch blocks...
✅ Updated 8 files with proper error handling
🔧 Creating error utility module...
✅ Created src/utils/errors.ts
```

### Parallel Execution
```
kritrima> Analyze project health and security

🔧 Running parallel analysis...
├── 🔧 Checking dependencies for vulnerabilities...
├── 🔧 Analyzing code quality...
├── 🔧 Checking TypeScript errors...
└── 🔧 Running security audit...

✅ Dependency audit: 2 vulnerabilities found
✅ Code quality: 95% score
✅ TypeScript: No errors
✅ Security: 3 issues detected

📊 Overall project health: Good (minor issues to address)
```

## Configuration Management

### View Current Configuration
```bash
node dist/index.js config show
```

### Update Settings
```bash
# Change AI model
node dist/index.js config set model "gpt-3.5-turbo"

# Adjust temperature for creativity
node dist/index.js config set temperature 0.8

# Change approval mode
node dist/index.js config set approvalMode "auto-edit"

# Set working directory
node dist/index.js config set workingDirectory "/path/to/project"
```

### Validate Configuration
```bash
node dist/index.js config validate
```

## Session Management

### List Sessions
```bash
node dist/index.js session list
```

### Clean Up Old Sessions
```bash
# Delete sessions older than 30 days
node dist/index.js session cleanup --days 30
```

### Delete Specific Session
```bash
node dist/index.js session delete abc12345
```

## Utility Commands

### Show Version
```bash
node dist/index.js version
```

### View Logs
```bash
node dist/index.js logs --tail 50
```

### Clean Up System
```bash
node dist/index.js cleanup --days 7
```

## Safety Features

### Approval Modes

#### Suggest Mode (Default)
```
kritrima> Delete all node_modules folders

⚠️  This command will delete directories. Do you want to proceed?
Command: find . -name "node_modules" -type d -exec rm -rf {} +
Risk Level: HIGH

[y/N]: y
🔧 Executing command...
✅ Deleted 3 node_modules directories
```

#### Auto-Edit Mode
```
# File operations are auto-approved
kritrima> Create a new component file

🔧 Creating component...
✅ Auto-approved: File creation (LOW risk)

# Shell commands still require approval
kritrima> Install new dependencies

⚠️  Shell command requires approval in auto-edit mode
[y/N]: 
```

#### Full-Auto Mode
```
# All operations are auto-approved (use with caution)
kritrima> Set up complete development environment

🔧 Auto-executing 15 operations...
✅ All operations completed automatically
```

## Error Handling Examples

### Network Issues
```
kritrima> Analyze this codebase

❌ Network error: Unable to connect to AI provider
🔄 Retrying in 2 seconds... (attempt 1/3)
🔄 Retrying in 4 seconds... (attempt 2/3)
✅ Connected successfully
🔧 Analyzing codebase...
```

### Command Failures
```
kritrima> Install non-existent package

🔧 Installing package...
❌ npm install failed: Package not found
💡 Suggestion: Did you mean 'express' instead of 'expres'?
🔧 Trying alternative: npm install express
✅ Successfully installed express
```

### Permission Issues
```
kritrima> Write to system directory

❌ Permission denied: Cannot write to /usr/local/bin
💡 Suggestion: Try using sudo or choose a different directory
🔧 Alternative: Writing to user directory instead
✅ File created in ~/.local/bin/
```

This comprehensive example demonstrates the full capabilities of the Kritrima AI CLI system, from basic configuration to advanced agentic workflows.
