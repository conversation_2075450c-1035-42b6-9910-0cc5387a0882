(function(e){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=e()}else if(typeof define==="function"&&define.amd){define([],e)}else{var t;if(typeof window!=="undefined"){t=window}else if(typeof global!=="undefined"){t=global}else if(typeof self!=="undefined"){t=self}else{t=this}t.stringKit=e()}})(function(){var e,t,a;return function(){function u(i,o,s){function l(a,e){if(!o[a]){if(!i[a]){var t="function"==typeof require&&require;if(!e&&t)return t(a,!0);if(c)return c(a,!0);var r=new Error("Cannot find module '"+a+"'");throw r.code="MODULE_NOT_FOUND",r}var n=o[a]={exports:{}};i[a][0].call(n.exports,function(e){var t=i[a][1][e];return l(t||e)},n,n.exports,u,i,o,s)}return o[a].exports}for(var c="function"==typeof require&&require,e=0;e<s.length;e++)l(s[e]);return l}return u}()({1:[function(e,t,a){"use strict";const r=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"];function n(e,t={}){this.sign=1;this.digits=[];this.exposant=0;this.special=null;this.decimalSeparator=t.decimalSeparator??".";this.forceDecimalSeparator=!!t.forceDecimalSeparator;this.groupSeparator=t.groupSeparator??"";this.numerals=t.numerals??r;this.numeralZero=t.numeralZero??null;this.placeNumerals=t.placeNumerals??null;this.set(e)}t.exports=n;n.prototype.set=function(e){var t,a,r,n,i,o,s;e=+e;this.sign=1;this.digits.length=0;this.exposant=0;this.special=null;if(!Number.isFinite(e)){this.special=e;return null}e=""+e;t=e.match(/(-)?([0-9]+)(?:.([0-9]+))?(?:e([+-][0-9]+))?/);if(!t){throw new Error("Unexpected error")}this.sign=t[1]?-1:1;this.exposant=t[2].length+(parseInt(t[4],10)||0);i=0;o=false;s=0;for(r=0,n=t[2].length;r<n;r++){a=+t[2][r];if(a!==0){o=true;this.digits[i]=a;i++;s=i}else if(o){this.digits[i]=a;i++}else{this.exposant--}}if(t[3]){for(r=0,n=t[3].length;r<n;r++){a=+t[3][r];if(a!==0){o=true;this.digits[i]=a;i++;s=i}else if(o){this.digits[i]=a;i++}else{this.exposant--}}}if(s!==i){this.digits.length=s}};n.prototype.toNumber=function(){if(this.special!==null){return this.special}return parseFloat((this.sign<0?"-":"")+"0."+this.digits.join("")+"e"+this.exposant)};n.prototype.toString=function(...e){if(this.special!==null){return""+this.special}if(this.exposant>20||this.exposant<-20){return this.toScientificString(...e)}return this.toNoExpString(...e)};n.prototype.toExponential=n.prototype.toExponentialString=function(){if(this.special!==null){return""+this.special}var e=this.sign<0?"-":"";if(!this.digits.length){return e+"0"}e+=this.digits[0];if(this.digits.length>1){e+=this.decimalSeparator+this.digits.join("").slice(1)}e+="e"+(this.exposant>0?"+":"")+(this.exposant-1);return e};const i=["⁰","¹","²","³","⁴","⁵","⁶","⁷","⁸","⁹"];const o="⁺";const s="⁻";const l="0".charCodeAt(0);n.prototype.toScientific=n.prototype.toScientificString=function(){if(this.special!==null){return""+this.special}var e=this.sign<0?"-":"";if(!this.digits.length){return e+"0"}e+=this.digits[0];if(this.digits.length>1){e+=this.decimalSeparator+this.digits.join("").slice(1)}var t=(this.exposant<=0?s:"")+(""+Math.abs(this.exposant-1)).split("").map(e=>i[e.charCodeAt(0)-l]).join("");e+=" × 10"+t;return e};n.prototype.toNoExp=n.prototype.toNoExpString=function(e=1,t=0,a=false,r=false,n=this.exposant){if(this.special!==null){return""+this.special}var i=[],o=[],s=this.sign<0?"-":r?"+":"";if(!this.digits.length){if(e>1){this.fillZeroes(i,e-1,e)}i.push(this.numeralZero??this.placeNumerals?.[0]?.[0]??this.numerals[0]);if(t&&!a){this.fillZeroes(o,t)}}else if(n<=0){this.fillZeroes(i,e);this.fillZeroes(o,-n,t-this.digits.length);this.appendNumerals(o,this.digits,undefined,undefined,-n-1);if(t&&this.digits.length-n<t){this.fillZeroes(o,t-this.digits.length+n)}}else if(n>=this.digits.length){if(n<e){this.fillZeroes(i,e-n,n-1)}this.appendNumerals(i,this.digits,undefined,undefined,n-1);this.fillZeroes(i,n-this.digits.length);if(t&&!a){this.fillZeroes(o,t)}}else{if(n<e){this.fillZeroes(i,e-n)}this.appendNumerals(i,this.digits,0,n,n-1);this.appendNumerals(o,this.digits,n,undefined,this.digits.length-n);if(t&&this.digits.length-n<t&&(!a||this.digits.length-n>0)){this.fillZeroes(o,t-this.digits.length+n)}}s+=this.groupSeparator?this.groupDigits(i,this.groupSeparator):i.join("");if(o.length){s+=this.decimalSeparator+(this.decimalGroupSeparator?this.groupDigits(o,this.decimalGroupSeparator):o.join(""))}else if(this.forceDecimalSeparator){s+=this.decimalSeparator}return s};const c=["","k","M","G","T","P","E","Z","Y"];const u=["","m","µ","n","p","f","a","z","y"];n.prototype.toMetric=n.prototype.toMetricString=function(e=1,t=0,a=false,r=false){if(this.special!==null){return""+this.special}if(!this.digits.length){return this.sign>0?"0":"-0"}var n="",i;if(this.exposant>0){i=1+(this.exposant-1)%3;n=c[Math.floor((this.exposant-1)/3)];if(n===undefined){return this.toScientificString()}}else{i=3- -this.exposant%3;n=u[1+Math.floor(-this.exposant/3)];if(n===undefined){return this.toScientificString()}}return this.toNoExpString(e,t,a,r,i)+n};n.prototype.precision=function(a,e=0){var t;if(this.special!==null||a>=this.digits.length){return this}if(a<0){this.digits.length=0;return this}e*=this.sign;if(e<0){t=this.digits.length>a+4&&this.digits[a]===9&&this.digits[a+1]===9&&this.digits[a+2]===9&&this.digits[a+3]===9&&this.digits[a+4]===9}else if(e>0){t=this.digits[a]>0||this.digits[a+1]>0||this.digits[a+2]>0||this.digits[a+3]>0||this.digits[a+4]>0}else{t=this.digits[a]>=5}if(t){let e=a-1,t=false;for(;e>=0;e--){if(this.digits[e]<9){this.digits[e]++;t=true;break}else{this.digits[e]=0}}if(!t){this.exposant++;this.digits[0]=1;this.digits.length=1}else{this.digits.length=e+1}}else{this.digits.length=a;this.removeTrailingZero()}return this};n.prototype.round=function(e=0,t=0){var a=this.exposant+e;return this.precision(a,t)};n.prototype.floor=function(e=0){var t=this.exposant+e;return this.precision(t,-1)};n.prototype.ceil=function(e=0){var t=this.exposant+e;return this.precision(t,1)};n.prototype.removeTrailingZero=function(){var e=this.digits.length-1;while(e>=0&&this.digits[e]===0){e--}this.digits.length=e+1};const g=3;n.prototype.groupDigits=function(e,t,a=false){var r="",n=a?0:g-e.length%g,i=0,o=e.length;for(;i<o;i++){r+=i&&(i+n)%g===0?t+e[i]:e[i]}return r};n.prototype.appendNumerals=function(r,n,e=0,i=n.length,o=i){for(let t=e,a=o;t<i;t++,a--){let e=this.placeNumerals?.[a]??this.numerals;r.push(e[n[t]]??n[t])}return r};n.prototype.fillZeroes=function(a,r,n=r-1){for(let e=0,t=n;e<r;e++,t--){let e=this.placeNumerals?.[t]??this.numerals;a.push(e[0]??0)}return a};const f={numeralZero:"N",placeNumerals:[["","I","II","III","IV","V","VI","VII","VIII","IX"],["","X","XX","XXX","XL","L","LX","LXX","LXXX","XC"],["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM"],["","M","MM","MMM","MMMM","ↁ","ↁↀ","ↁↀↀ","ↁↀↀↀ","ↁↀↀↀↀ"]]};const p={numeralZero:"N",placeNumerals:[["","I","II","III","IIII","V","VI","VII","VIII","VIIII"],["","X","XX","XXX","XXXX","L","LX","LXX","LXXX","LXXXX"],["","C","CC","CCC","CCCC","D","DC","DCC","DCCC","DCCCC"],["","M","MM","MMM","MMMM","ↁ","ↁↀ","ↁↀↀ","ↁↀↀↀ","ↁↀↀↀↀ"]]};const h={numeralZero:"N",placeNumerals:[["","I","II","III","IV","V","VI","VII","VIII","IX"],["","X","XX","XXX","XL","L","LX","LXX","LXXX","XC"],["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM"],["","M","MM","MMM","MMMM","IↃↃ","IↃↃCIↃ","IↃↃCIↃCIↃ","IↃↃCIↃCIↃCIↃ","IↃↃCIↃCIↃCIↃCIↃ"],["","CCIↃↃ","CCIↃↃCCIↃↃ","CCIↃↃCCIↃↃCCIↃↃ","CCIↃↃCCIↃↃCCIↃↃCCIↃↃ","IↃↃↃ","IↃↃↃCCIↃↃ","IↃↃↃCCIↃↃCCIↃↃ","IↃↃↃCCIↃↃCCIↃↃCCIↃↃ","IↃↃↃCCIↃↃCCIↃↃCCIↃↃCCIↃↃ"],["","CCCIↃↃↃ","CCCIↃↃↃCCCIↃↃↃ","CCCIↃↃↃCCCIↃↃↃCCCIↃↃↃ","CCCIↃↃↃCCCIↃↃↃCCCIↃↃↃCCCIↃↃↃ","IↃↃↃↃ","IↃↃↃↃCCCIↃↃↃ","IↃↃↃↃCCCIↃↃↃCCCIↃↃↃ","IↃↃↃↃCCCIↃↃↃCCCIↃↃↃCCCIↃↃↃ","IↃↃↃↃCCCIↃↃↃCCCIↃↃↃCCCIↃↃↃCCCIↃↃↃ"]]};n.roman=(e,t)=>{t=t?Object.assign({},t,f):f;return new n(e,t)};n.additiveRoman=(e,t)=>{t=t?Object.assign({},t,p):p;return new n(e,t)}},{}],2:[function(e,t,a){"use strict";const r={reset:"[0m",bold:"[1m",dim:"[2m",italic:"[3m",underline:"[4m",inverse:"[7m",defaultColor:"[39m",black:"[30m",red:"[31m",green:"[32m",yellow:"[33m",blue:"[34m",magenta:"[35m",cyan:"[36m",white:"[37m",grey:"[90m",gray:"[90m",brightBlack:"[90m",brightRed:"[91m",brightGreen:"[92m",brightYellow:"[93m",brightBlue:"[94m",brightMagenta:"[95m",brightCyan:"[96m",brightWhite:"[97m",defaultBgColor:"[49m",bgBlack:"[40m",bgRed:"[41m",bgGreen:"[42m",bgYellow:"[43m",bgBlue:"[44m",bgMagenta:"[45m",bgCyan:"[46m",bgWhite:"[47m",bgGrey:"[100m",bgGray:"[100m",bgBrightBlack:"[100m",bgBrightRed:"[101m",bgBrightGreen:"[102m",bgBrightYellow:"[103m",bgBrightBlue:"[104m",bgBrightMagenta:"[105m",bgBrightCyan:"[106m",bgBrightWhite:"[107m"};t.exports=r;r.fgColor={defaultColor:r.defaultColor,black:r.black,red:r.red,green:r.green,yellow:r.yellow,blue:r.blue,magenta:r.magenta,cyan:r.cyan,white:r.white,grey:r.grey,gray:r.gray,brightBlack:r.brightBlack,brightRed:r.brightRed,brightGreen:r.brightGreen,brightYellow:r.brightYellow,brightBlue:r.brightBlue,brightMagenta:r.brightMagenta,brightCyan:r.brightCyan,brightWhite:r.brightWhite};r.bgColor={defaultColor:r.defaultBgColor,black:r.bgBlack,red:r.bgRed,green:r.bgGreen,yellow:r.bgYellow,blue:r.bgBlue,magenta:r.bgMagenta,cyan:r.bgCyan,white:r.bgWhite,grey:r.bgGrey,gray:r.bgGray,brightBlack:r.bgBrightBlack,brightRed:r.bgBrightRed,brightGreen:r.bgBrightGreen,brightYellow:r.bgBrightYellow,brightBlue:r.bgBrightBlue,brightMagenta:r.bgBrightMagenta,brightCyan:r.bgBrightCyan,brightWhite:r.bgBrightWhite};r.trueColor=(t,a,r)=>{if(a===undefined&&typeof t==="string"){let e=t;if(e[0]==="#"){e=e.slice(1)}if(e.length===3){e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]}t=parseInt(e.slice(0,2),16)||0;a=parseInt(e.slice(2,4),16)||0;r=parseInt(e.slice(4,6),16)||0}return"[38;2;"+t+";"+a+";"+r+"m"};r.bgTrueColor=(t,a,r)=>{if(a===undefined&&typeof t==="string"){let e=t;if(e[0]==="#"){e=e.slice(1)}if(e.length===3){e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]}t=parseInt(e.slice(0,2),16)||0;a=parseInt(e.slice(2,4),16)||0;r=parseInt(e.slice(4,6),16)||0}return"[48;2;"+t+";"+a+";"+r+"m"};const o={0:null,1:{bold:true},2:{dim:true},22:{bold:false,dim:false},3:{italic:true},23:{italic:false},4:{underline:true},24:{underline:false},5:{blink:true},25:{blink:false},7:{inverse:true},27:{inverse:false},8:{hidden:true},28:{hidden:false},9:{strike:true},29:{strike:false},30:{color:0},31:{color:1},32:{color:2},33:{color:3},34:{color:4},35:{color:5},36:{color:6},37:{color:7},39:{color:"default"},90:{color:8},91:{color:9},92:{color:10},93:{color:11},94:{color:12},95:{color:13},96:{color:14},97:{color:15},40:{bgColor:0},41:{bgColor:1},42:{bgColor:2},43:{bgColor:3},44:{bgColor:4},45:{bgColor:5},46:{bgColor:6},47:{bgColor:7},49:{bgColor:"default"},100:{bgColor:8},101:{bgColor:9},102:{bgColor:10},103:{bgColor:11},104:{bgColor:12},105:{bgColor:13},106:{bgColor:14},107:{bgColor:15}};r.parse=e=>{var t,a,r,n,i=[];for([,t,a]of e.matchAll(/\x1b\[([0-9;]+)m|(.[^\x1b]*)/g)){if(a){if(i.length){i[i.length-1].text+=a}else{i.push({text:a})}}else{t.split(";").forEach(e=>{n=o[e];if(n===undefined){return}if(!i.length||i[i.length-1].text){if(!n){r={text:""}}else{r=Object.assign({},r,n);r.text=""}i.push(r)}else{if(!n){i[i.length-1]={text:""}}else{Object.assign(r,n)}}})}}return i}},{}],3:[function(e,t,a){"use strict";var r={};t.exports=r;r.toCamelCase=function(e,i=false,o=false){if(!e||typeof e!=="string"){return""}return e.replace(/(?:^[\s_-]*|([\s_-]+))(([^\s_-]?)([^\s_-]*))/g,(e,t,a,r,n)=>{if(i){if(!t&&!o){return a}if(!r){return""}return r.toUpperCase()+n}if(!t&&!o){return a.toLowerCase()}if(!r){return""}return r.toUpperCase()+n.toLowerCase()})};r.camelCaseToSeparated=function(e,i=" ",t=true){if(!e||typeof e!=="string"){return""}if(!t){return e.replace(/^([A-Z])|([A-Z])/g,(e,t,a)=>{if(t){return t.toLowerCase()}return i+a.toLowerCase()})}return e.replace(/(?:(^)|)([A-Z]+)(?:($)|(?=[a-z]))/g,(e,t,a,r)=>{t=t==="";r=r==="";var n=t?"":i;return a.length===1?n+a.toLowerCase():r?n+a:a.length===2?n+a[0].toLowerCase()+i+a[1].toLowerCase():n+a.slice(0,-1)+i+a.slice(-1).toLowerCase()})};r.camelCaseToDash=r.camelCaseToDashed=e=>r.camelCaseToSeparated(e,"-",false)},{}],4:[function(e,t,a){"use strict";const r=e("./latinize.js");const n=e("./english.js");const s=e("./json-data/emoji-keyword-to-charlist.json");const l=e("./json-data/emoji-char-to-canonical-name.json");const c={};t.exports=c;c.getCanonicalName=e=>l[e];const i={};c.getKeywords=e=>{if(!l[e]){return}if(!i[e]){i[e]=c.splitIntoKeywords(l[e]);Object.freeze(i[e])}return i[e]};c.search=(e,t=false)=>{var a=c.splitIntoKeywords(e),r={},n,i=0;for(let t of a){if(!s[t]){continue}for(let e of s[t]){if(!r[e]){n=1;r[e]={emoji:e,score:n,canonical:l[e],keywords:c.getKeywords(e)}}else{n=r[e].score+1;r[e].score=n}if(n>i){i=n}}}var o=[...Object.values(r)];if(t){o=o.filter(e=>e.score===i)}o.sort((e,t)=>t.score-e.score||e.keywords.length-t.keywords.length);return o};c.searchBest=e=>c.search(e,true);c.get=e=>c.search(e,true)[0]?.emoji;c.simplifyName=e=>{e=e.toLowerCase().replace(/[“”().!]/g,"").replace(/[ ’',]/g,"-").replace(/-+/g,"-");e=r(e);return e};c.simplifyKeyword=e=>{var t=e;t=n.undoPresentParticiple(t);return t};const o=new Set(["with","of"]);c.splitIntoKeywords=(e,t=false)=>{if(!t){e=c.simplifyName(e)}var a=e.split(/-/g).filter(e=>e.length>=2&&!o.has(e));a=a.map(c.simplifyKeyword);a=[...new Set(a)];return a}},{"./english.js":5,"./json-data/emoji-char-to-canonical-name.json":10,"./json-data/emoji-keyword-to-charlist.json":11,"./latinize.js":14}],5:[function(e,t,a){"use strict";const r={};t.exports=r;const o=new Set(["a","e","i","o","u","y"]);const s=new Set(["a","i","o","u"]);const l=new Set(["s"]);const c=new Set(["v"]);const u=new Set(["w","x"]);const g=new Set(["cl","dl","gl","kl","nc","pl","tl"]);const f=new Set(["d","g","m","n","p"]);const p=new Set(["or"]);r.undoPresentParticiple=i=>{if(i.endsWith("ing")&&i.length>=6&&!i.endsWith("ghtning")){let e="ing";let t=e.length;let a=i[i.length-t-1],r=i[i.length-t-2],n=i[i.length-t-3];if(o.has(a)){i=i.slice(0,-t)}else if(o.has(r)){if(o.has(n)){if(l.has(a)&&!p.has(r+a)){i=i.slice(0,-t)+"e"}else{i=i.slice(0,-t)}}else if(s.has(r)&&!u.has(a)){i=i.slice(0,-t)+"e"}else{i=i.slice(0,-t)}}else if(a===r&&f.has(a)){i=i.slice(0,-t-1)}else if(c.has(a)||g.has(r+a)){i=i.slice(0,-t)+"e"}else{i=i.slice(0,-t)}}return i}},{}],6:[function(e,t,a){"use strict";a.regExp=a.regExpPattern=e=>e.replace(/([.*+?^${}()|[\]/\\])/g,"\\$1");a.regExpReplacement=e=>e.replace(/\$/g,"$$$$");a.format=e=>e.replace(/%/g,"%%");a.jsSingleQuote=e=>a.control(e).replace(/'/g,"\\'");a.jsDoubleQuote=e=>a.control(e).replace(/"/g,'\\"');a.shellArg=e=>"'"+e.replace(/'/g,"'\\''")+"'";var r={"\r":"\\r","\n":"\\n","\t":"\\t","":"\\x7f"};a.control=(e,a=false)=>e.replace(/[\x00-\x1f\x7f]/g,e=>{if(a&&(e==="\n"||e==="\t")){return e}if(r[e]!==undefined){return r[e]}var t=e.charCodeAt(0).toString(16);if(t.length%2){t="0"+t}return"\\x"+t});var n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#039;"};a.html=e=>e.replace(/[&<>]/g,e=>n[e]);a.htmlAttr=e=>e.replace(/[&<>"]/g,e=>n[e]);a.htmlSpecialChars=e=>e.replace(/[&<>"']/g,e=>n[e]);a.unicodePercentEncode=e=>e.replace(/[\x00-\x1f\u0100-\uffff\x7f%]/g,e=>{try{return encodeURI(e)}catch(e){return""}});a.httpHeaderValue=e=>a.unicodePercentEncode(e)},{}],7:[function(B,e,E){(function(L){(function(){"use strict";const c=B("./inspect.js").inspect;const u=B("./inspect.js").inspectError;const t=B("./escape.js");const n=B("./ansi.js");const i=B("./unicode.js");const o=B("./naturalSort.js");const h=B("./StringNumber.js");E.formatMethod=function(...d){var m,e=d[0],b=1,y=d.length;if(typeof e!=="string"){if(!e){e=""}else if(typeof e.toString==="function"){e=e.toString()}else{e=""}}var w={hasMarkup:false,shift:null,markupStack:[]};if(this.markupReset&&this.startingMarkupReset){e=(typeof this.markupReset==="function"?this.markupReset(w.markupStack):this.markupReset)+e}e=e.replace(/\^\[([^\]]*)]?|\^(.)|(%%)|%([+-]?)([0-9]*)(?:\[([^\]]*)\])?([a-zA-Z])/g,(e,t,a,r,n,i,o,s)=>{var l,c,u,g,f,p,h=[];if(r){return"%"}if(t){a=t}if(a){if(this.noMarkup){return"^"+a}return k.call(this,w,e,a)}if(i){i=parseInt(i,10);if(n){if(n==="+"){i=b+i}else if(n==="-"){i=b-i}}}else{i=b}b++;if(i>=y||i<1){m=undefined}else{m=d[i]}if(v[s]){l=v[s](m,o,this);if(this.argumentSanitizer&&!v[s].noSanitize){l=this.argumentSanitizer(l)}if(this.escapeMarkup&&!v[s].noEscapeMarkup){l=E.escapeMarkup(l)}if(o&&!v[s].noCommonModeArg){l=C(l,o)}return l}if(s==="F"){b--;if(o===undefined){return""}u=o.split(":");g=u[0];f=u[1];if(!g){return""}if(f&&(p=f.match(/%([+-]?)([0-9]*)[a-zA-Z]/g))){for(c=0;c<p.length;c++){n=p[c][1];i=p[c][2];if(i){i=parseInt(i,10);if(n){if(n==="+"){i=b+i}else if(n==="-"){i=b-i}}}else{i=b}b++;if(i>=y||i<1){h[c]=undefined}else{h[c]=d[i]}}}if(!this||!this.fn||typeof this.fn[g]!=="function"){return""}return this.fn[g].apply(this,h)}return""});if(w.hasMarkup&&this.markupReset&&this.endingMarkupReset){e+=typeof this.markupReset==="function"?this.markupReset(w.markupStack):this.markupReset}if(this.extraArguments){for(;b<y;b++){m=d[b];if(m===null||m===undefined){continue}else if(typeof m==="string"){e+=m}else if(typeof m==="number"){e+=m}else if(typeof m.toString==="function"){e+=m.toString()}}}return e};E.markupMethod=function(l){if(typeof l!=="string"){if(!l){l=""}else if(typeof l.toString==="function"){l=l.toString()}else{l=""}}var c={hasMarkup:false,shift:null,markupStack:[]};if(this.parse){let e,t,a,r,n,i,o,s=[];for([a,r,n,i]of l.matchAll(/\^\[([^\]]*)]?|\^(.)|([^^]+)/g)){if(i){if(s.length){s[s.length-1].text+=i}else{s.push({text:i})}continue}if(r){n=r}e=k.call(this,c,a,n);if(!Array.isArray(e)){e=[e]}for(t of e){o=s.length?s[s.length-1]:null;if(typeof t==="string"){if(o){o.text+=t}else{s.push({text:t})}}else if(!t){if(o&&o.text.length&&Object.keys(o).length>1){s.push({text:""})}}else{if(o&&o.text.length){s.push(Object.assign({text:""},...c.markupStack))}else{if(o){Object.assign(o,t)}else{s.push(Object.assign({text:""},t))}}}}}return s}if(this.markupReset&&this.startingMarkupReset){l=(typeof this.markupReset==="function"?this.markupReset(c.markupStack):this.markupReset)+l}l=l.replace(/\^\[([^\]]*)]?|\^(.)/g,(e,t,a)=>k.call(this,c,e,t||a));if(c.hasMarkup&&this.markupReset&&this.endingMarkupReset){l+=typeof this.markupReset==="function"?this.markupReset(c.markupStack):this.markupReset}return l};function k(e,t,a){var r,n,i,o,s;if(a==="^"){return"^"}if(this.shiftMarkup&&this.shiftMarkup[a]){e.shift=this.shiftMarkup[a];return""}if(a.length>1&&this.dataMarkup&&(s=a.indexOf(":"))!==-1){n=a.slice(0,s);r=this.dataMarkup[n];if(r===undefined){if(this.markupCatchAll===undefined){return""}r=this.markupCatchAll}e.hasMarkup=true;i=a.slice(s+1);if(typeof r==="function"){o=r(e.markupStack,n,i)}else{o={[r]:i};l(e,o)}return o}if(e.shift){r=this.shiftedMarkup?.[e.shift]?.[a];e.shift=null}else{r=this.markup?.[a]}if(r===undefined){if(this.markupCatchAll===undefined){return""}r=this.markupCatchAll}e.hasMarkup=true;if(typeof r==="function"){o=r(e.markupStack,a)}else{o=r;l(e,o)}return o}function l(t,a){if(Array.isArray(a)){for(let e of a){if(e===null){t.markupStack.length=0}else{t.markupStack.push(e)}}}else{if(a===null){t.markupStack.length=0}else{t.markupStack.push(a)}}}E.stripMarkup=e=>e.replace(/\^\[[^\]]*]?|\^./g,e=>e==="^^"?"^":e==="^ "?" ":"");E.escapeMarkup=e=>e.replace(/\^/g,"^^");const a={argumentSanitizer:e=>t.control(e,true),extraArguments:true,color:false,noMarkup:false,escapeMarkup:false,endingMarkupReset:true,startingMarkupReset:false,markupReset:n.reset,shiftMarkup:{"#":"background"},markup:{":":n.reset," ":n.reset+" ","-":n.dim,"+":n.bold,_:n.underline,"/":n.italic,"!":n.inverse,b:n.blue,B:n.brightBlue,c:n.cyan,C:n.brightCyan,g:n.green,G:n.brightGreen,k:n.black,K:n.brightBlack,m:n.magenta,M:n.brightMagenta,r:n.red,R:n.brightRed,w:n.white,W:n.brightWhite,y:n.yellow,Y:n.brightYellow},shiftedMarkup:{background:{":":n.reset," ":n.reset+" ",b:n.bgBlue,B:n.bgBrightBlue,c:n.bgCyan,C:n.bgBrightCyan,g:n.bgGreen,G:n.bgBrightGreen,k:n.bgBlack,K:n.bgBrightBlack,m:n.bgMagenta,M:n.bgBrightMagenta,r:n.bgRed,R:n.bgBrightRed,w:n.bgWhite,W:n.bgBrightWhite,y:n.bgYellow,Y:n.bgBrightYellow}},dataMarkup:{fg:(e,t,a)=>{var r=n.fgColor[a]||n.trueColor(a);e.push(r);return r},bg:(e,t,a)=>{var r=n.bgColor[a]||n.bgTrueColor(a);e.push(r);return r}},markupCatchAll:(e,t,a)=>{var r="";if(a===undefined){if(t[0]==="#"){r=n.trueColor(t)}else if(typeof n[t]==="string"){r=n[t]}}e.push(r);return r}};a.dataMarkup.color=a.dataMarkup.c=a.dataMarkup.fgColor=a.dataMarkup.fg;a.dataMarkup.bgColor=a.dataMarkup.bg;E.createFormatter=e=>E.formatMethod.bind(Object.assign({},a,e));E.format=E.formatMethod.bind(a);E.format.default=a;E.formatNoMarkup=E.formatMethod.bind(Object.assign({},a,{noMarkup:true}));E.formatThirdPartyMarkup=E.formatMethod.bind(Object.assign({},a,{noMarkup:true,escapeMarkup:true}));E.createMarkup=e=>E.markupMethod.bind(Object.assign({},a,e));E.markup=E.markupMethod.bind(a);E.format.count=function(e,t=false){var a,r,n,i=1,o=0;if(typeof e!=="string"){return 0}var s=t?/%([+-]?)([0-9]*)(?:\[[^\]]*\])?[a-zA-EG-Z]/g:/%([+-]?)([0-9]*)(?:\[[^\]]*\])?[a-zA-EG-Z]|(\^\[[^\]]*]?|\^.)/g;for([,n,r,a]of e.matchAll(s)){if(a){continue}if(r){r=parseInt(r,10);if(n){if(n==="+"){r=i+r}else if(n==="-"){r=i-r}}}else{r=i}i++;if(o<r){o=r}}return o};E.format.hasFormatting=function(e){if(e.search(/\^(.?)|(%%)|%([+-]?)([0-9]*)(?:\[([^\]]*)\])?([a-zA-Z])/)!==-1){return true}return false};const v={};E.format.modes=v;v.s=(e,t)=>{var a=w(t);if(typeof e==="string"){return e}if(e===null||e===undefined||e===false){return a.empty?"":"("+e+")"}if(e===true){return"("+e+")"}if(typeof e==="number"){return""+e}if(typeof e.toString==="function"){return e.toString()}return"("+e+")"};v.r=e=>v.s(e);v.r.noSanitize=true;v.S=(e,t,a)=>{var r=w(t);var n=a.escapeMarkup?e=>a.argumentSanitizer?a.argumentSanitizer(e):e:e=>E.markupMethod.call(a,a.argumentSanitizer?a.argumentSanitizer(e):e);if(typeof e==="string"){return n(e)}if(e===null||e===undefined||e===false){return r.empty?"":"("+e+")"}if(e===true){return"("+e+")"}if(typeof e==="number"){return""+e}if(typeof e.toString==="function"){return n(e.toString())}return n("("+e+")")};v.S.noSanitize=true;v.S.noEscapeMarkup=true;v.S.noCommonModeArg=true;v.N=(e,t)=>M(e,t,false);v.n=(e,t)=>M(e,t,true);v.f=(e,t)=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){e=0}var a=b(t),r=new h(e,{decimalSeparator:".",groupSeparator:a.groupSeparator});if(a.rounding!==null){r.round(a.rounding)}if(a.precision){r.precision(a.precision)}return r.toString(a.leftPadding,a.rightPadding,a.rightPaddingOnlyIfDecimal)};v.f.noSanitize=true;v.v=(e,t)=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){e=0}var a=b(t),r=h.additiveRoman(e,{decimalSeparator:".",groupSeparator:a.groupSeparator});if(a.rounding!==null){r.round(a.rounding)}if(a.precision){r.precision(a.precision)}return r.toString(a.leftPadding,a.rightPadding,a.rightPaddingOnlyIfDecimal)};v.v.noSanitize=true;v.V=(e,t)=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){e=0}var a=b(t),r=h.roman(e,{decimalSeparator:".",groupSeparator:a.groupSeparator});if(a.rounding!==null){r.round(a.rounding)}if(a.precision){r.precision(a.precision)}return r.toString(a.leftPadding,a.rightPadding,a.rightPaddingOnlyIfDecimal)};v.V.noSanitize=true;v.P=(e,t)=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){e=0}e*=100;var a=b(t),r=new h(e,{decimalSeparator:".",groupSeparator:a.groupSeparator});if(a.rounding!==null||!a.precision){r.round(a.rounding||0)}if(a.precision){r.precision(a.precision)}return r.toNoExpString(a.leftPadding,a.rightPadding,a.rightPaddingOnlyIfDecimal)+"%"};v.P.noSanitize=true;v.p=(e,t)=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){e=0}e=(e-1)*100;var a=b(t),r=new h(e,{decimalSeparator:".",groupSeparator:a.groupSeparator});if(a.rounding!==null||!a.precision){r.round(a.rounding||0)}if(a.precision){r.precision(a.precision)}return r.toNoExpString(a.leftPadding,a.rightPadding,a.rightPaddingOnlyIfDecimal,true)+"%"};v.p.noSanitize=true;v.k=(e,t)=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){return"0"}var a=b(t),r=new h(e,{decimalSeparator:".",groupSeparator:a.groupSeparator});if(a.rounding!==null){r.round(a.rounding)}if(a.precision||a.rounding===null){r.precision(a.precision||3)}return r.toMetricString(a.leftPadding,a.rightPadding,a.rightPaddingOnlyIfDecimal)};v.k.noSanitize=true;v.e=(e,t)=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){e=0}var a=b(t),r=new h(e,{decimalSeparator:".",groupSeparator:a.groupSeparator});if(a.rounding!==null){r.round(a.rounding)}if(a.precision){r.precision(a.precision)}return r.toExponential()};v.e.noSanitize=true;v.K=(e,t)=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){e=0}var a=b(t),r=new h(e,{decimalSeparator:".",groupSeparator:a.groupSeparator});if(a.rounding!==null){r.round(a.rounding)}if(a.precision){r.precision(a.precision)}return r.toScientific()};v.K.noSanitize=true;v.d=v.i=e=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e==="number"){return""+Math.floor(e)}return"0"};v.i.noSanitize=true;v.u=e=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e==="number"){return""+Math.max(Math.floor(e),0)}return"0"};v.u.noSanitize=true;v.U=e=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e==="number"){return""+Math.max(Math.floor(e),1)}return"1"};v.U.noSanitize=true;v.m=e=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){return"(NaN)"}var t="";if(e<0){t="-";e=-e}var a=D(e),r=e-a;if(!r){return t+a+"°"}var n=D(r*60),i=D(r*3600-n*60);if(i){return t+a+"°"+(""+n).padStart(2,"0")+"′"+(""+i).padStart(2,"0")+"″"}return t+a+"°"+(""+n).padStart(2,"0")+"′"};v.m.noSanitize=true;v.T=(e,t)=>{try{e=new Date(e)}catch(e){return"(invalid)"}if(Number.isNaN(e.getTime())){return"(invalid)"}var a="",r="",n="",i=x(t),o=i.roundingType,s=i.useAbbreviation;if(i.years){if(a){a+="-"}a+=e.getFullYear()}if(i.months){if(a){a+="-"}a+=(""+(e.getMonth()+1)).padStart(2,"0")}if(i.days){if(a){a+="-"}a+=(""+e.getDate()).padStart(2,"0")}if(i.hours){if(r&&!i.useAbbreviation){r+=":"}r+=(""+e.getHours()).padStart(2,"0");if(i.useAbbreviation){r+="h"}}if(i.minutes){if(r&&!i.useAbbreviation){r+=":"}r+=(""+e.getMinutes()).padStart(2,"0");if(i.useAbbreviation){r+="min"}}if(i.seconds){if(r&&!i.useAbbreviation){r+=":"}r+=(""+e.getSeconds()).padStart(2,"0");if(i.useAbbreviation){r+="s"}}if(a){if(n){n+=" "}n+=a}if(r){if(n){n+=" "}n+=r}return n};v.T.noSanitize=true;v.t=(e,t)=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){return"(NaN)"}var a,r,n,i,o,s="",l=I(t),c=l.roundingType,u=l.useAbbreviation?"h":":",g=l.useAbbreviation?"min":":",f=l.useAbbreviation?"s":".",p=l.useAbbreviation;n=e/1e3;if(n<0){n=-n;c*=-1;s="-"}if(n<60&&!l.forceMinutes){i=new h(n,{decimalSeparator:f,forceDecimalSeparator:p});i.round(l.rounding,c);if(i.toNumber()<60){o=i.toString(1,l.rightPadding,l.rightPaddingOnlyIfDecimal);return s+o}n=60}r=Math.floor(n/60);n=n%60;i=new h(n,{decimalSeparator:f,forceDecimalSeparator:p});i.round(l.rounding,c);if(i.toNumber()<60){o=i.toString(2,l.rightPadding,l.rightPaddingOnlyIfDecimal)}else{r++;n=0;i.set(n);o=i.toString(2,l.rightPadding,l.rightPaddingOnlyIfDecimal)}if(r<60&&!l.forceHours){return s+r+g+o}a=Math.floor(r/60);r=r%60;return s+a+u+(""+r).padStart(2,"0")+g+o};v.t.noSanitize=true;v.h=e=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e==="number"){return""+Math.max(Math.floor(e),0).toString(16)}return"0"};v.h.noSanitize=true;v.x=e=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e!=="number"){return"00"}var t=""+Math.max(Math.floor(e),0).toString(16);if(t.length%2){t="0"+t}return t};v.x.noSanitize=true;v.o=e=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e==="number"){return""+Math.max(Math.floor(e),0).toString(8)}return"0"};v.o.noSanitize=true;v.b=e=>{if(typeof e==="string"){e=parseFloat(e)}if(typeof e==="number"){return""+Math.max(Math.floor(e),0).toString(2)}return"0"};v.b.noSanitize=true;v.X=e=>{if(typeof e==="string"){e=L.from(e)}else if(!L.isBuffer(e)){return""}return e.toString("hex")};v.X.noSanitize=true;v.z=e=>{if(typeof e==="string"){e=L.from(e)}else if(!L.isBuffer(e)){return""}return e.toString("base64")};v.Z=e=>{if(typeof e==="string"){e=L.from(e)}else if(!L.isBuffer(e)){return""}return e.toString("base64").replace(/\+/g,"-").replace(/\//g,"_").replace(/[=]{1,2}$/g,"")};const r={};v.I=(e,t,a)=>A(e,t,a,r);v.I.noSanitize=true;const s={noFunc:true,enumOnly:true,noDescriptor:true,useInspect:true,useInspectPropertyBlackList:true};v.Y=(e,t,a)=>A(e,t,a,s);v.Y.noSanitize=true;const g={minimal:true,bulletIndex:true,noMarkup:true};v.O=(e,t,a)=>A(e,t,a,g);v.O.noSanitize=true;const f={};v.E=(e,t,a)=>A(e,t,a,f,true);v.E.noSanitize=true;v.J=e=>e===undefined?"null":JSON.stringify(e);v.D=()=>"";v.D.noSanitize=true;const p=/([a-zA-Z])(.[^a-zA-Z]*)/g;const d=/([a-zA-Z]|^)([^a-zA-Z]*)/g;function C(a,r){for(let[,e,t]of r.matchAll(p)){if(e==="L"){let e=i.width(a);t=+t||1;if(e>t){a=i.truncateWidth(a,t-1).trim()+"…";e=i.width(a)}if(e<t){a=" ".repeat(t-e)+a}}else if(e==="R"){let e=i.width(a);t=+t||1;if(e>t){a=i.truncateWidth(a,t-1).trim()+"…";e=i.width(a)}if(e<t){a=a+" ".repeat(t-e)}}}return a}const m={leftPadding:1,rightPadding:0,rightPaddingOnlyIfDecimal:false,rounding:null,precision:null,groupSeparator:""};function b(a){m.leftPadding=1;m.rightPadding=0;m.rightPaddingOnlyIfDecimal=false;m.rounding=null;m.precision=null;m.groupSeparator="";if(a){for(let[,e,t]of a.matchAll(d)){if(e==="z"){m.leftPadding=+t}else if(e==="g"){m.groupSeparator=t||" "}else if(!e){if(t[0]==="."){let e=t[t.length-1];if(e==="!"){m.rounding=m.rightPadding=parseInt(t.slice(1,-1),10)||0}else if(e==="?"){m.rounding=m.rightPadding=parseInt(t.slice(1,-1),10)||0;m.rightPaddingOnlyIfDecimal=true}else{m.rounding=parseInt(t.slice(1),10)||0}}else if(t[t.length-1]==="."){m.rounding=-parseInt(t.slice(0,-1),10)||0}else{m.precision=parseInt(t,10)||null}}}}return m}const y={empty:false};function w(a){y.empty=false;if(a){for(let[,e,t]of a.matchAll(d)){if(e==="e"){y.empty=true}}}return y}const S={useAbbreviation:false,rightPadding:0,rightPaddingOnlyIfDecimal:false,years:true,months:true,days:true,hours:true,minutes:true,seconds:true};function x(a){S.rightPadding=0;S.rightPaddingOnlyIfDecimal=false;S.rounding=0;S.roundingType=-1;S.years=S.months=S.days=false;S.hours=S.minutes=S.seconds=false;S.useAbbreviation=false;var r=false;if(a){for(let[,e,t]of a.matchAll(d)){if(e==="T"){S.years=S.months=S.days=false;S.hours=S.minutes=S.seconds=true;r=true}else if(e==="D"){S.years=S.months=S.days=true;S.hours=S.minutes=S.seconds=false;r=true}else if(e==="Y"){S.years=true;r=true}else if(e==="M"){S.months=true;r=true}else if(e==="d"){S.days=true;r=true}else if(e==="h"){S.hours=true;r=true}else if(e==="m"){S.minutes=true;r=true}else if(e==="s"){S.seconds=true;r=true}else if(e==="r"){S.roundingType=0}else if(e==="f"){S.roundingType=-1}else if(e==="c"){S.roundingType=1}else if(e==="a"){S.useAbbreviation=true}else if(!e){if(t[0]==="."){let e=t[t.length-1];if(e==="!"){S.rounding=S.rightPadding=parseInt(t.slice(1,-1),10)||0}else if(e==="?"){S.rounding=S.rightPadding=parseInt(t.slice(1,-1),10)||0;S.rightPaddingOnlyIfDecimal=true}else{S.rounding=parseInt(t.slice(1),10)||0}}}}}if(!r){S.years=S.months=S.days=true;S.hours=S.minutes=S.seconds=true}return S}const j={useAbbreviation:false,rightPadding:0,rightPaddingOnlyIfDecimal:false,rounding:0,roundingType:-1,forceHours:false,forceMinutes:false};function I(a){j.rightPadding=0;j.rightPaddingOnlyIfDecimal=false;j.rounding=0;j.roundingType=-1;j.useAbbreviation=j.forceHours=j.forceMinutes=false;if(a){for(let[,e,t]of a.matchAll(d)){if(e==="h"){j.forceHours=j.forceMinutes=true}else if(e==="m"){j.forceMinutes=true}else if(e==="r"){j.roundingType=0}else if(e==="f"){j.roundingType=-1}else if(e==="c"){j.roundingType=1}else if(e==="a"){j.useAbbreviation=true}else if(!e){if(t[0]==="."){let e=t[t.length-1];if(e==="!"){j.rounding=j.rightPadding=parseInt(t.slice(1,-1),10)||0}else if(e==="?"){j.rounding=j.rightPadding=parseInt(t.slice(1,-1),10)||0;j.rightPaddingOnlyIfDecimal=true}else{j.rounding=parseInt(t.slice(1),10)||0}}}}}return j}function M(e,a,t){var r=2;if(a){for(let[,e,t]of a.matchAll(d)){if(!e){r=parseInt(t,10)||1}}}return z(e,t,r,0)}function z(e,t,a,r){if(typeof e==="string"){return e}if(e===null||e===undefined||e===true||e===false){return""+e}if(typeof e==="number"){return v.f(e,".3g ")}if(e instanceof Set){e=[...e]}if(Array.isArray(e)){if(r>=a){return"[...]"}e=e.map(e=>z(e,true,a,r+1));if(t){return"["+e.join(",")+"]"}return e.join(", ")}if(L.isBuffer(e)){e=[...e].map(e=>{e=e.toString(16);if(e.length===1){e="0"+e}return e});return"<"+e.join(" ")+">"}var n=Object.getPrototypeOf(e);if(n===null||n===Object.prototype){if(r>=a){return"{...}"}e=Object.entries(e).sort(o).map(e=>e[0]+": "+z(e[1],true,a,r+1));if(t){return"{"+e.join(", ")+"}"}return e.join(", ")}if(typeof e.inspect==="function"){return e.inspect()}if(typeof e.toString==="function"){return e.toString()}return"("+e+")"}function A(e,a,t,r,n=false){var i,o,s=3,l=t&&t.color?"color":"none";if(a){for(let[,e,t]of a.matchAll(d)){if(e==="c"){if(t==="+"){l="color"}else if(t==="-"){l="none"}}else if(e==="i"){l="inline"}else if(e==="l"){i=parseInt(t,10)||undefined}else if(e==="s"){o=parseInt(t,10)||undefined}else if(!e){s=parseInt(t,10)||1}}}if(n){return u(Object.assign({depth:s,style:l,outputMaxLength:i,maxLength:o},r),e)}return c(Object.assign({depth:s,style:l,outputMaxLength:i,maxLength:o},r),e)}const P=1e-10;const O=Math.round(1/P);function T(e){return Math.round(e*O)/O}function D(e){return Math.floor(e+P)}function e(e,t){return T(t*Math.round(e*(1/t)))}}).call(this)}).call(this,B("buffer").Buffer)},{"./StringNumber.js":1,"./ansi.js":2,"./escape.js":6,"./inspect.js":9,"./naturalSort.js":16,"./unicode.js":20,buffer:22}],8:[function(e,t,a){"use strict";const C={};t.exports=C;C.score=(e,t)=>{if(e===t){return 1}if(e.length===0||t.length===0){return 0}return Math.max(0,1-C.levenshtein(e,t)/t.length)};const S=0;const x=.88;const v=.9;C.bestMatch=(e,t,a={})=>{var r=a.scoreLimit||S,n,i,o,s,l=-1,c=null;for(n=0,i=t.length;n<i;n++){s=t[n];o=C.score(e,s);if(o===1){return a.indexOf?n:s}if(o>r){r=o;c=s;l=n}}return a.indexOf?l:c};C.topMatch=(a,e,t={})=>{var r=t.scoreLimit||S,n=t.deltaRate||v,i,o,s;s=e.map((e,t)=>({pattern:e,index:t,score:C.score(a,e)}));s.sort((e,t)=>t.score-e.score);if(s[0].score<=r){return[]}r=Math.max(r,s[0].score*n);for(i=1,o=s.length;i<o;i++){if(s[i].score<r){s.length=i;break}}return t.indexOf?s.map(e=>e.index):s.map(e=>e.pattern)};const r=new Set(["a","an","the","this","that","those","some","of","in","on","at","my","your","her","his","its","our","their"]);function j(e,t=r){return e.split(/[ '"/|,:_-]+/g).filter(e=>e&&!t.has(e))}C.bestTokenMatch=(e,t,a={})=>{var r=a.scoreLimit||S,n=a.tokenDisparityPenalty||x,i,o,s,l,c,u,g,f,p,h,d=r,m,b,y=j(e),w,k=-1,v=null;if(!y.length||!t.length){return a.indexOf?k:v}for(i=0,o=t.length;i<o;i++){g=t[i];f=j(g);h=0;for(s=0,l=y.length;s<l;s++){m=y[s];w=0;for(c=0,u=f.length;c<u;c++){p=f[c];b=C.score(m,p);if(b>w){w=b;if(b===1){break}}}h+=w}h/=y.length;if(y.length!==f.length){h*=n**Math.abs(f.length-y.length)}if(h>d){d=h;v=g;k=i}}return a.indexOf?k:v};C.topTokenMatch=(e,t,a={})=>{var r=a.scoreLimit||S,n=a.tokenDisparityPenalty||x,i=a.deltaRate||v,o,s,l,c,u,g,f,p,h,d,m,b,y=j(e),w,k=[];if(!y.length||!t.length){return[]}for(o=0,s=t.length;o<s;o++){f=t[o];p=j(f);d=0;for(l=0,c=y.length;l<c;l++){m=y[l];w=0;for(u=0,g=p.length;u<g;u++){h=p[u];b=C.score(m,h);if(b>w){w=b;if(b===1){break}}}d+=w}d/=y.length;if(y.length!==p.length){d*=n**Math.abs(p.length-y.length)}k.push({pattern:f,index:o,score:d})}k.sort((e,t)=>t.score-e.score);if(k[0].score<=r){return[]}r=Math.max(r,k[0].score*i);for(o=1,s=k.length;o<s;o++){if(k[o].score<r){k.length=o;break}}return a.indexOf?k.map(e=>e.index):k.map(e=>e.pattern)};const g=[];const f=[];C.levenshtein=(t,a)=>{if(t===a){return 0}if(t.length>a.length){let e=t;t=a;a=e}let e=t.length;let r=a.length;while(e>0&&t.charCodeAt(e-1)===a.charCodeAt(r-1)){e--;r--}let n=0;while(n<e&&t.charCodeAt(n)===a.charCodeAt(n)){n++}e-=n;r-=n;if(e===0){return r}let i;let o;let s;let l;let c=0;let u=0;while(c<e){f[c]=t.charCodeAt(n+c);g[c]=++c}while(u<r){i=a.charCodeAt(n+u);s=u++;o=u;for(c=0;c<e;c++){l=i===f[c]?s:s+1;s=g[c];o=g[c]=s>o?l>o?o+1:l:l>s?s+1:l}}return o}},{}],9:[function(t,e,a){(function(z,g){(function(){"use strict";const C=t("./escape.js");const n=t("./ansi.js");const S={};const x=new Set([Object,Array]);function o(e,t){if(arguments.length<2){t=e;e={}}else if(!e||typeof e!=="object"){e={}}var a={depth:0,ancestors:[]};if(!e.style){e.style=u.none}else if(typeof e.style==="string"){e.style=u[e.style]}if(e.depth===undefined){e.depth=3}if(e.maxLength===undefined){e.maxLength=250}if(e.outputMaxLength===undefined){e.outputMaxLength=5e3}if(e.nofunc){e.noFunc=true}if(e.minimal){e.noFunc=true;e.noDescriptor=true;e.noType=true;e.noArrayProperty=true;e.enumOnly=true;e.proto=false;e.funcDetails=false}if(e.minimalPlusConstructor){e.noFunc=true;e.noDescriptor=true;e.noTypeButConstructor=true;e.noArrayProperty=true;e.enumOnly=true;e.proto=false;e.funcDetails=false}var r=j(a,e,t);if(r.length>e.outputMaxLength){r=e.style.truncate(r,e.outputMaxLength)}return r}a.inspect=o;function j(t,a,e){var r,n,i,o,s,l,c,u,g,f,p,h,d,m="",b="",y="",w="",k,v;g=typeof e;if(t.depth){w=(a.tab??a.style.tab).repeat(a.noMarkup?t.depth-1:t.depth)}if(g==="function"&&a.noFunc){return""}if(t.key!==undefined){if(t.descriptor){y=[];if(t.descriptor.error){y="["+t.descriptor.error+"]"}else{if(!t.descriptor.configurable){y.push("-conf")}if(!t.descriptor.enumerable){y.push("-enum")}if(!t.descriptor.writable){y.push("-w")}if(y.length){y=y.join(" ")}else{y=""}}}if(t.keyIsProperty){if(!a.noMarkup&&I(t.key)){b='"'+a.style.key(t.key)+'": '}else{b=a.style.key(t.key)+": "}}else if(a.bulletIndex){b=(typeof a.bulletIndex==="string"?a.bulletIndex:"*")+" "}else if(!a.noIndex){b=a.style.index(t.key)}if(y){y=" "+a.style.type(y)}}f=t.noPre?"":w+b;if(e===undefined){m+=f+a.style.constant("undefined")+y+a.style.newline}else if(e===S){m+=f+a.style.constant("[empty]")+y+a.style.newline}else if(e===null){m+=f+a.style.constant("null")+y+a.style.newline}else if(e===false){m+=f+a.style.constant("false")+y+a.style.newline}else if(e===true){m+=f+a.style.constant("true")+y+a.style.newline}else if(g==="number"){m+=f+a.style.number(e.toString())+(a.noType||a.noTypeButConstructor?"":" "+a.style.type("number"))+y+a.style.newline}else if(g==="string"){if(e.length>a.maxLength){m+=f+(a.noMarkup?"":'"')+a.style.string(C.control(e.slice(0,a.maxLength-1)))+"…"+(a.noMarkup?"":'"')+(a.noType||a.noTypeButConstructor?"":" "+a.style.type("string")+a.style.length("("+e.length+" - TRUNCATED)"))+y+a.style.newline}else{m+=f+(a.noMarkup?"":'"')+a.style.string(C.control(e))+(a.noMarkup?"":'"')+(a.noType||a.noTypeButConstructor?"":" "+a.style.type("string")+a.style.length("("+e.length+")"))+y+a.style.newline}}else if(z.isBuffer(e)){m+=f+a.style.inspect(e.inspect())+(a.noType?"":" "+a.style.type("Buffer")+a.style.length("("+e.length+")"))+y+a.style.newline}else if(g==="object"||g==="function"){n=i="";h=false;if(g==="function"){h=true;n=" "+a.style.funcName(e.name?e.name:"(anonymous)");i=a.style.length("("+e.length+")")}p=false;if(Array.isArray(e)){p=true;i=a.style.length("("+e.length+")")}if(!e.constructor){c="(no constructor)"}else if(!e.constructor.name){c="(anonymous)"}else{c=e.constructor.name}l=!e.constructor||x.has(e.constructor);c=a.style.constructorName(c);o=Object.getPrototypeOf(e);m+=f;if(!a.noType&&(!a.noTypeButConstructor||!l)){if(t.forceType&&!a.noType&&!a.noTypeButConstructor){m+=a.style.type(t.forceType)}else if(a.noTypeButConstructor){m+=c}else{m+=c+n+i+" "+a.style.type(g)+y}if(!h||a.funcDetails){m+=" "}}if(p&&a.noArrayProperty){s=[...Array(e.length).keys()]}else{s=Object.getOwnPropertyNames(e)}if(a.sort){s.sort()}d=M(e,t,a);if(a.protoBlackList&&a.protoBlackList.has(o)){m+=a.style.limit("[skip]")+a.style.newline}else if(d!==undefined){if(typeof d==="string"){m+="=> "+d+a.style.newline}else{m+="=> "+j({depth:t.depth,ancestors:t.ancestors,noPre:true},a,d)}}else if(h&&!a.funcDetails){m+=a.style.newline}else if(!s.length&&!a.proto){m+=(a.noMarkup?"":p?"[]":"{}")+a.style.newline}else if(t.depth>=a.depth){m+=a.style.limit("[depth limit]")+a.style.newline}else if(t.ancestors.indexOf(e)!==-1){m+=a.style.limit("[circular]")+a.style.newline}else{m+=(a.noMarkup?"":p?"[":"{")+a.style.newline;v=t.ancestors.slice();v.push(e);for(r=0;r<s.length&&m.length<a.outputMaxLength;r++){if(!p&&(a.propertyBlackList&&a.propertyBlackList.has(s[r])||a.useInspectPropertyBlackList&&e.inspectPropertyBlackList instanceof Set&&e.inspectPropertyBlackList.has(s[r]))){continue}if(p&&a.noArrayProperty&&!(s[r]in e)){m+=j({depth:t.depth+1,ancestors:v,key:s[r],keyIsProperty:false},a,S)}else{try{k=Object.getOwnPropertyDescriptor(e,s[r]);if(k&&!k.enumerable&&a.enumOnly){continue}u=!p||!k.enumerable||isNaN(s[r]);if(!a.noDescriptor&&k&&(k.get||k.set)){m+=j({depth:t.depth+1,ancestors:v,key:s[r],keyIsProperty:u,descriptor:k,forceType:"getter/setter"},a,{get:k.get,set:k.set})}else{m+=j({depth:t.depth+1,ancestors:v,key:s[r],keyIsProperty:u,descriptor:a.noDescriptor?undefined:k||{error:"Bad Proxy Descriptor"}},a,e[s[r]])}}catch(e){m+=j({depth:t.depth+1,ancestors:v,key:s[r],keyIsProperty:u,descriptor:a.noDescriptor?undefined:k},a,e)}}if(r<s.length-1){m+=a.style.comma}}if(a.proto){m+=j({depth:t.depth+1,ancestors:v,key:"__proto__",keyIsProperty:true},a,o)}m+=a.noMarkup?"":w+(p?"]":"}")+a.style.newline}}if(t.depth===0){if(a.style.trim){m=m.trim()}if(a.style==="html"){m=C.html(m)}}return m}function I(e){if(!e.length){return true}return false}var s=["pending","fulfilled","rejected"];function M(r,n,i){if(typeof r.constructor!=="function"){return}if(r instanceof String){return r.toString()}if(r instanceof RegExp){return r.toString()}if(r instanceof Date){return r.toString()+" ["+r.getTime()+"]"}if(typeof Set==="function"&&r instanceof Set){return Array.from(r)}if(typeof Map==="function"&&r instanceof Map){return Array.from(r)}if(r instanceof Promise){if(g&&g.binding&&g.binding("util")&&g.binding("util").getPromiseDetails){let e=g.binding("util").getPromiseDetails(r);let t=s[e[0]];let a="Promise <"+t+">";if(t==="fulfilled"){a+=" "+j({depth:n.depth,ancestors:n.ancestors,noPre:true},i,e[1])}else if(t==="rejected"){if(e[1]instanceof Error){a+=" "+l({style:i.style,noErrorStack:true},e[1])}else{a+=" "+j({depth:n.depth,ancestors:n.ancestors,noPre:true},i,e[1])}}return a}}if(r._bsontype){return r.toString()}if(i.useInspect&&typeof r.inspect==="function"){return r.inspect()}return}function l(t,a){var r="",e,n,i;if(arguments.length<2){a=t;t={}}else if(!t||typeof t!=="object"){t={}}if(!t.style){t.style=u.none}else if(typeof t.style==="string"){t.style=u[t.style]}if(!(a instanceof Error)){r+="[not an Error] ";if(typeof a==="string"){let e=5e3;if(a.length>e){r+=t.style.errorMessage(C.control(a.slice(0,e-1),true))+"…"+t.style.length("("+a.length+" - TRUNCATED)")+t.style.newline}else{r+=t.style.errorMessage(C.control(a,true))+t.style.newline}return r}else if(!a||typeof a!=="object"||!a.name||typeof a.name!=="string"||!a.message||typeof a.message!=="string"){r+=o(t,a);return r}}if(a.stack&&!t.noErrorStack){e=c(t,a.stack)}n=a.type||a.constructor.name;i=a.code||a.name||a.errno||a.number;r+=t.style.errorType(n)+(i?" ["+t.style.errorType(i)+"]":"")+": ";r+=t.style.errorMessage(a.message)+"\n";if(e){r+=t.style.errorStack(e)+"\n"}if(a.from){r+=t.style.newline+t.style.errorFromMessage("From error:")+t.style.newline+l(t,a.from)}return r}a.inspectError=l;function c(l,e){if(arguments.length<2){e=l;l={}}else if(!l||typeof l!=="object"){l={}}if(!l.style){l.style=u.none}else if(typeof l.style==="string"){l.style=u[l.style]}if(!e){return}if((l.browser||g.browser)&&e.indexOf("@")!==-1){e=e.replace(/[</]*(?=@)/g,"").replace(/^\s*([^@]*)\s*@\s*([^\n]*)(?::([0-9]+):([0-9]+))?$/gm,(e,t,a,r,n)=>{return l.style.errorStack("    at ")+(t?l.style.errorStackMethod(t)+" ":"")+l.style.errorStack("(")+(a?l.style.errorStackFile(a):l.style.errorStack("unknown"))+(r?l.style.errorStack(":")+l.style.errorStackLine(r):"")+(n?l.style.errorStack(":")+l.style.errorStackColumn(n):"")+l.style.errorStack(")")})}else{e=e.replace(/^[^\n]*\n/,"");e=e.replace(/^\s*(at)\s+(?:(?:(async|new)\s+)?([^\s:()[\]\n]+(?:\([^)]+\))?)\s)?(?:\[as ([^\s:()[\]\n]+)\]\s)?(?:\(?([^:()[\]\n]+):([0-9]+):([0-9]+)\)?)?$/gm,(e,t,a,r,n,i,o,s)=>{return l.style.errorStack("    at ")+(a?l.style.errorStackKeyword(a)+" ":"")+(r?l.style.errorStackMethod(r)+" ":"")+(n?l.style.errorStack("[as ")+l.style.errorStackMethodAs(n)+l.style.errorStack("] "):"")+l.style.errorStack("(")+(i?l.style.errorStackFile(i):l.style.errorStack("unknown"))+(o?l.style.errorStack(":")+l.style.errorStackLine(o):"")+(s?l.style.errorStack(":")+l.style.errorStackColumn(s):"")+l.style.errorStack(")")})}return e}a.inspectStack=c;var u={};var e=e=>e;u.none={trim:false,tab:"    ",newline:"\n",comma:"",limit:e,type:e=>"<"+e+">",constant:e,funcName:e,constructorName:e=>"<"+e+">",length:e,key:e,index:e=>"["+e+"] ",number:e,inspect:e,string:e,errorType:e,errorMessage:e,errorStack:e,errorStackKeyword:e,errorStackMethod:e,errorStackMethodAs:e,errorStackFile:e,errorStackLine:e,errorStackColumn:e,errorFromMessage:e,truncate:(e,t)=>e.slice(0,t-1)+"…"};u.inline=Object.assign({},u.none,{trim:true,tab:"",newline:" ",comma:", ",length:()=>"",index:()=>""});u.color=Object.assign({},u.none,{limit:e=>n.bold+n.brightRed+e+n.reset,type:e=>n.italic+n.brightBlack+e+n.reset,constant:e=>n.cyan+e+n.reset,funcName:e=>n.italic+n.magenta+e+n.reset,constructorName:e=>n.magenta+e+n.reset,length:e=>n.italic+n.brightBlack+e+n.reset,key:e=>n.green+e+n.reset,index:e=>n.blue+"["+e+"]"+n.reset+" ",number:e=>n.cyan+e+n.reset,inspect:e=>n.cyan+e+n.reset,string:e=>n.blue+e+n.reset,errorType:e=>n.red+n.bold+e+n.reset,errorMessage:e=>n.red+n.italic+e+n.reset,errorStack:e=>n.brightBlack+e+n.reset,errorStackKeyword:e=>n.italic+n.bold+e+n.reset,errorStackMethod:e=>n.brightYellow+e+n.reset,errorStackMethodAs:e=>n.yellow+e+n.reset,errorStackFile:e=>n.brightCyan+e+n.reset,errorStackLine:e=>n.blue+e+n.reset,errorStackColumn:e=>n.magenta+e+n.reset,errorFromMessage:e=>n.yellow+n.underline+e+n.reset,truncate:(e,t)=>{var a=n.gray+"…"+n.reset;e=e.slice(0,t-a.length);var r=e.lastIndexOf("");if(r>=e.length-6){e=e.slice(0,r)}return e+a}});u.html=Object.assign({},u.none,{tab:"&nbsp;&nbsp;&nbsp;&nbsp;",newline:"<br />",limit:e=>'<span style="color:red">'+e+"</span>",type:e=>'<i style="color:gray">'+e+"</i>",constant:e=>'<span style="color:cyan">'+e+"</span>",funcName:e=>'<i style="color:magenta">'+e+"</i>",constructorName:e=>'<span style="color:magenta">'+e+"</span>",length:e=>'<i style="color:gray">'+e+"</i>",key:e=>'<span style="color:green">'+e+"</span>",index:e=>'<span style="color:blue">['+e+"]</span> ",number:e=>'<span style="color:cyan">'+e+"</span>",inspect:e=>'<span style="color:cyan">'+e+"</span>",string:e=>'<span style="color:blue">'+e+"</span>",errorType:e=>'<span style="color:red">'+e+"</span>",errorMessage:e=>'<span style="color:red">'+e+"</span>",errorStack:e=>'<span style="color:gray">'+e+"</span>",errorStackKeyword:e=>"<i>"+e+"</i>",errorStackMethod:e=>'<span style="color:yellow">'+e+"</span>",errorStackMethodAs:e=>'<span style="color:yellow">'+e+"</span>",errorStackFile:e=>'<span style="color:cyan">'+e+"</span>",errorStackLine:e=>'<span style="color:blue">'+e+"</span>",errorStackColumn:e=>'<span style="color:gray">'+e+"</span>",errorFromMessage:e=>'<span style="color:yellow">'+e+"</span>"})}).call(this)}).call(this,{isBuffer:t("../../../../../../opt/node-v20.11.0/lib/node_modules/browserify/node_modules/is-buffer/index.js")},t("_process"))},{"../../../../../../opt/node-v20.11.0/lib/node_modules/browserify/node_modules/is-buffer/index.js":23,"./ansi.js":2,"./escape.js":6,_process:24}],10:[function(e,t,a){t.exports={"😀":"grinning-face","😃":"grinning-face-with-big-eyes","😄":"grinning-face-with-smiling-eyes","😁":"beaming-face-with-smiling-eyes","😆":"grinning-squinting-face","😅":"grinning-face-with-sweat","🤣":"rolling-on-the-floor-laughing","😂":"face-with-tears-of-joy","🙂":"slightly-smiling-face","🙃":"upside-down-face","🫠":"melting-face","😉":"winking-face","😊":"smiling-face-with-smiling-eyes","😇":"smiling-face-with-halo","🥰":"smiling-face-with-hearts","😍":"smiling-face-with-heart-eyes","🤩":"star-struck","😘":"face-blowing-a-kiss","😗":"kissing-face","☺️":"smiling-face","😚":"kissing-face-with-closed-eyes","😙":"kissing-face-with-smiling-eyes","🥲":"smiling-face-with-tear","😋":"face-savoring-food","😛":"face-with-tongue","😜":"winking-face-with-tongue","🤪":"zany-face","😝":"squinting-face-with-tongue","🤑":"money-mouth-face","🤗":"smiling-face-with-open-hands","🤭":"face-with-hand-over-mouth","🫢":"face-with-open-eyes-and-hand-over-mouth","🫣":"face-with-peeking-eye","🤫":"shushing-face","🤔":"thinking-face","🫡":"saluting-face","🤐":"zipper-mouth-face","🤨":"face-with-raised-eyebrow","😐":"neutral-face","😑":"expressionless-face","😶":"face-without-mouth","🫥":"dotted-line-face","😶‍🌫️":"face-in-clouds","😏":"smirking-face","😒":"unamused-face","🙄":"face-with-rolling-eyes","😬":"grimacing-face","😮‍💨":"face-exhaling","🤥":"lying-face","🫨":"shaking-face","😌":"relieved-face","😔":"pensive-face","😪":"sleepy-face","🤤":"drooling-face","😴":"sleeping-face","😷":"face-with-medical-mask","🤒":"face-with-thermometer","🤕":"face-with-head-bandage","🤢":"nauseated-face","🤮":"face-vomiting","🤧":"sneezing-face","🥵":"hot-face","🥶":"cold-face","🥴":"woozy-face","😵":"face-with-crossed-out-eyes","😵‍💫":"face-with-spiral-eyes","🤯":"exploding-head","🤠":"cowboy-hat-face","🥳":"partying-face","🥸":"disguised-face","😎":"smiling-face-with-sunglasses","🤓":"nerd-face","🧐":"face-with-monocle","😕":"confused-face","🫤":"face-with-diagonal-mouth","😟":"worried-face","🙁":"slightly-frowning-face","☹️":"frowning-face","😮":"face-with-open-mouth","😯":"hushed-face","😲":"astonished-face","😳":"flushed-face","🥺":"pleading-face","🥹":"face-holding-back-tears","😦":"frowning-face-with-open-mouth","😧":"anguished-face","😨":"fearful-face","😰":"anxious-face-with-sweat","😥":"sad-but-relieved-face","😢":"crying-face","😭":"loudly-crying-face","😱":"face-screaming-in-fear","😖":"confounded-face","😣":"persevering-face","😞":"disappointed-face","😓":"downcast-face-with-sweat","😩":"weary-face","😫":"tired-face","🥱":"yawning-face","😤":"face-with-steam-from-nose","😡":"enraged-face","😠":"angry-face","🤬":"face-with-symbols-on-mouth","😈":"smiling-face-with-horns","👿":"angry-face-with-horns","💀":"skull","☠️":"skull-and-crossbones","💩":"pile-of-poo","🤡":"clown-face","👹":"ogre","👺":"goblin","👻":"ghost","👽":"alien","👾":"alien-monster","🤖":"robot","😺":"grinning-cat","😸":"grinning-cat-with-smiling-eyes","😹":"cat-with-tears-of-joy","😻":"smiling-cat-with-heart-eyes","😼":"cat-with-wry-smile","😽":"kissing-cat","🙀":"weary-cat","😿":"crying-cat","😾":"pouting-cat","🙈":"see-no-evil-monkey","🙉":"hear-no-evil-monkey","🙊":"speak-no-evil-monkey","💌":"love-letter","💘":"heart-with-arrow","💝":"heart-with-ribbon","💖":"sparkling-heart","💗":"growing-heart","💓":"beating-heart","💞":"revolving-hearts","💕":"two-hearts","💟":"heart-decoration","❣️":"heart-exclamation","💔":"broken-heart","❤️‍🔥":"heart-on-fire","❤️‍🩹":"mending-heart","❤️":"red-heart","🩷":"pink-heart","🧡":"orange-heart","💛":"yellow-heart","💚":"green-heart","💙":"blue-heart","🩵":"light-blue-heart","💜":"purple-heart","🤎":"brown-heart","🖤":"black-heart","🩶":"grey-heart","🤍":"white-heart","💋":"kiss-mark","💯":"hundred-points","💢":"anger-symbol","💥":"collision","💫":"dizzy","💦":"sweat-droplets","💨":"dashing-away","🕳️":"hole","💬":"speech-balloon","👁️‍🗨️":"eye-in-speech-bubble","🗨️":"left-speech-bubble","🗯️":"right-anger-bubble","💭":"thought-balloon","💤":"zzz","👋":"waving-hand","🤚":"raised-back-of-hand","🖐️":"hand-with-fingers-splayed","✋":"raised-hand","🖖":"vulcan-salute","🫱":"rightwards-hand","🫲":"leftwards-hand","🫳":"palm-down-hand","🫴":"palm-up-hand","🫷":"leftwards-pushing-hand","🫸":"rightwards-pushing-hand","👌":"ok-hand","🤌":"pinched-fingers","🤏":"pinching-hand","✌️":"victory-hand","🤞":"crossed-fingers","🫰":"hand-with-index-finger-and-thumb-crossed","🤟":"love-you-gesture","🤘":"sign-of-the-horns","🤙":"call-me-hand","👈":"backhand-index-pointing-left","👉":"backhand-index-pointing-right","👆":"backhand-index-pointing-up","🖕":"middle-finger","👇":"backhand-index-pointing-down","☝️":"index-pointing-up","🫵":"index-pointing-at-the-viewer","👍":"thumbs-up","👎":"thumbs-down","✊":"raised-fist","👊":"oncoming-fist","🤛":"left-facing-fist","🤜":"right-facing-fist","👏":"clapping-hands","🙌":"raising-hands","🫶":"heart-hands","👐":"open-hands","🤲":"palms-up-together","🤝":"handshake","🙏":"folded-hands","✍️":"writing-hand","💅":"nail-polish","🤳":"selfie","💪":"flexed-biceps","🦾":"mechanical-arm","🦿":"mechanical-leg","🦵":"leg","🦶":"foot","👂":"ear","🦻":"ear-with-hearing-aid","👃":"nose","🧠":"brain","🫀":"anatomical-heart","🫁":"lungs","🦷":"tooth","🦴":"bone","👀":"eyes","👁️":"eye","👅":"tongue","👄":"mouth","🫦":"biting-lip","👶":"baby","🧒":"child","👦":"boy","👧":"girl","🧑":"person","👱":"person-blond-hair","👨":"man","🧔":"person-beard","🧔‍♂️":"man-beard","🧔‍♀️":"woman-beard","👨‍🦰":"man-red-hair","👨‍🦱":"man-curly-hair","👨‍🦳":"man-white-hair","👨‍🦲":"man-bald","👩":"woman","👩‍🦰":"woman-red-hair","🧑‍🦰":"person-red-hair","👩‍🦱":"woman-curly-hair","🧑‍🦱":"person-curly-hair","👩‍🦳":"woman-white-hair","🧑‍🦳":"person-white-hair","👩‍🦲":"woman-bald","🧑‍🦲":"person-bald","👱‍♀️":"woman-blond-hair","👱‍♂️":"man-blond-hair","🧓":"older-person","👴":"old-man","👵":"old-woman","🙍":"person-frowning","🙍‍♂️":"man-frowning","🙍‍♀️":"woman-frowning","🙎":"person-pouting","🙎‍♂️":"man-pouting","🙎‍♀️":"woman-pouting","🙅":"person-gesturing-no","🙅‍♂️":"man-gesturing-no","🙅‍♀️":"woman-gesturing-no","🙆":"person-gesturing-ok","🙆‍♂️":"man-gesturing-ok","🙆‍♀️":"woman-gesturing-ok","💁":"person-tipping-hand","💁‍♂️":"man-tipping-hand","💁‍♀️":"woman-tipping-hand","🙋":"person-raising-hand","🙋‍♂️":"man-raising-hand","🙋‍♀️":"woman-raising-hand","🧏":"deaf-person","🧏‍♂️":"deaf-man","🧏‍♀️":"deaf-woman","🙇":"person-bowing","🙇‍♂️":"man-bowing","🙇‍♀️":"woman-bowing","🤦":"person-facepalming","🤦‍♂️":"man-facepalming","🤦‍♀️":"woman-facepalming","🤷":"person-shrugging","🤷‍♂️":"man-shrugging","🤷‍♀️":"woman-shrugging","🧑‍⚕️":"health-worker","👨‍⚕️":"man-health-worker","👩‍⚕️":"woman-health-worker","🧑‍🎓":"student","👨‍🎓":"man-student","👩‍🎓":"woman-student","🧑‍🏫":"teacher","👨‍🏫":"man-teacher","👩‍🏫":"woman-teacher","🧑‍⚖️":"judge","👨‍⚖️":"man-judge","👩‍⚖️":"woman-judge","🧑‍🌾":"farmer","👨‍🌾":"man-farmer","👩‍🌾":"woman-farmer","🧑‍🍳":"cook","👨‍🍳":"man-cook","👩‍🍳":"woman-cook","🧑‍🔧":"mechanic","👨‍🔧":"man-mechanic","👩‍🔧":"woman-mechanic","🧑‍🏭":"factory-worker","👨‍🏭":"man-factory-worker","👩‍🏭":"woman-factory-worker","🧑‍💼":"office-worker","👨‍💼":"man-office-worker","👩‍💼":"woman-office-worker","🧑‍🔬":"scientist","👨‍🔬":"man-scientist","👩‍🔬":"woman-scientist","🧑‍💻":"technologist","👨‍💻":"man-technologist","👩‍💻":"woman-technologist","🧑‍🎤":"singer","👨‍🎤":"man-singer","👩‍🎤":"woman-singer","🧑‍🎨":"artist","👨‍🎨":"man-artist","👩‍🎨":"woman-artist","🧑‍✈️":"pilot","👨‍✈️":"man-pilot","👩‍✈️":"woman-pilot","🧑‍🚀":"astronaut","👨‍🚀":"man-astronaut","👩‍🚀":"woman-astronaut","🧑‍🚒":"firefighter","👨‍🚒":"man-firefighter","👩‍🚒":"woman-firefighter","👮":"police-officer","👮‍♂️":"man-police-officer","👮‍♀️":"woman-police-officer","🕵️":"detective","🕵️‍♂️":"man-detective","🕵️‍♀️":"woman-detective","💂":"guard","💂‍♂️":"man-guard","💂‍♀️":"woman-guard","🥷":"ninja","👷":"construction-worker","👷‍♂️":"man-construction-worker","👷‍♀️":"woman-construction-worker","🫅":"person-with-crown","🤴":"prince","👸":"princess","👳":"person-wearing-turban","👳‍♂️":"man-wearing-turban","👳‍♀️":"woman-wearing-turban","👲":"person-with-skullcap","🧕":"woman-with-headscarf","🤵":"person-in-tuxedo","🤵‍♂️":"man-in-tuxedo","🤵‍♀️":"woman-in-tuxedo","👰":"person-with-veil","👰‍♂️":"man-with-veil","👰‍♀️":"woman-with-veil","🤰":"pregnant-woman","🫃":"pregnant-man","🫄":"pregnant-person","🤱":"breast-feeding","👩‍🍼":"woman-feeding-baby","👨‍🍼":"man-feeding-baby","🧑‍🍼":"person-feeding-baby","👼":"baby-angel","🎅":"santa-claus","🤶":"mrs-claus","🧑‍🎄":"mx-claus","🦸":"superhero","🦸‍♂️":"man-superhero","🦸‍♀️":"woman-superhero","🦹":"supervillain","🦹‍♂️":"man-supervillain","🦹‍♀️":"woman-supervillain","🧙":"mage","🧙‍♂️":"man-mage","🧙‍♀️":"woman-mage","🧚":"fairy","🧚‍♂️":"man-fairy","🧚‍♀️":"woman-fairy","🧛":"vampire","🧛‍♂️":"man-vampire","🧛‍♀️":"woman-vampire","🧜":"merperson","🧜‍♂️":"merman","🧜‍♀️":"mermaid","🧝":"elf","🧝‍♂️":"man-elf","🧝‍♀️":"woman-elf","🧞":"genie","🧞‍♂️":"man-genie","🧞‍♀️":"woman-genie","🧟":"zombie","🧟‍♂️":"man-zombie","🧟‍♀️":"woman-zombie","🧌":"troll","💆":"person-getting-massage","💆‍♂️":"man-getting-massage","💆‍♀️":"woman-getting-massage","💇":"person-getting-haircut","💇‍♂️":"man-getting-haircut","💇‍♀️":"woman-getting-haircut","🚶":"person-walking","🚶‍♂️":"man-walking","🚶‍♀️":"woman-walking","🧍":"person-standing","🧍‍♂️":"man-standing","🧍‍♀️":"woman-standing","🧎":"person-kneeling","🧎‍♂️":"man-kneeling","🧎‍♀️":"woman-kneeling","🧑‍🦯":"person-with-white-cane","👨‍🦯":"man-with-white-cane","👩‍🦯":"woman-with-white-cane","🧑‍🦼":"person-in-motorized-wheelchair","👨‍🦼":"man-in-motorized-wheelchair","👩‍🦼":"woman-in-motorized-wheelchair","🧑‍🦽":"person-in-manual-wheelchair","👨‍🦽":"man-in-manual-wheelchair","👩‍🦽":"woman-in-manual-wheelchair","🏃":"person-running","🏃‍♂️":"man-running","🏃‍♀️":"woman-running","💃":"woman-dancing","🕺":"man-dancing","🕴️":"person-in-suit-levitating","👯":"people-with-bunny-ears","👯‍♂️":"men-with-bunny-ears","👯‍♀️":"women-with-bunny-ears","🧖":"person-in-steamy-room","🧖‍♂️":"man-in-steamy-room","🧖‍♀️":"woman-in-steamy-room","🧗":"person-climbing","🧗‍♂️":"man-climbing","🧗‍♀️":"woman-climbing","🤺":"person-fencing","🏇":"horse-racing","⛷️":"skier","🏂":"snowboarder","🏌️":"person-golfing","🏌️‍♂️":"man-golfing","🏌️‍♀️":"woman-golfing","🏄":"person-surfing","🏄‍♂️":"man-surfing","🏄‍♀️":"woman-surfing","🚣":"person-rowing-boat","🚣‍♂️":"man-rowing-boat","🚣‍♀️":"woman-rowing-boat","🏊":"person-swimming","🏊‍♂️":"man-swimming","🏊‍♀️":"woman-swimming","⛹️":"person-bouncing-ball","⛹️‍♂️":"man-bouncing-ball","⛹️‍♀️":"woman-bouncing-ball","🏋️":"person-lifting-weights","🏋️‍♂️":"man-lifting-weights","🏋️‍♀️":"woman-lifting-weights","🚴":"person-biking","🚴‍♂️":"man-biking","🚴‍♀️":"woman-biking","🚵":"person-mountain-biking","🚵‍♂️":"man-mountain-biking","🚵‍♀️":"woman-mountain-biking","🤸":"person-cartwheeling","🤸‍♂️":"man-cartwheeling","🤸‍♀️":"woman-cartwheeling","🤼":"people-wrestling","🤼‍♂️":"men-wrestling","🤼‍♀️":"women-wrestling","🤽":"person-playing-water-polo","🤽‍♂️":"man-playing-water-polo","🤽‍♀️":"woman-playing-water-polo","🤾":"person-playing-handball","🤾‍♂️":"man-playing-handball","🤾‍♀️":"woman-playing-handball","🤹":"person-juggling","🤹‍♂️":"man-juggling","🤹‍♀️":"woman-juggling","🧘":"person-in-lotus-position","🧘‍♂️":"man-in-lotus-position","🧘‍♀️":"woman-in-lotus-position","🛀":"person-taking-bath","🛌":"person-in-bed","🧑‍🤝‍🧑":"people-holding-hands","👭":"women-holding-hands","👫":"woman-and-man-holding-hands","👬":"men-holding-hands","💏":"kiss","👩‍❤️‍💋‍👨":"kiss-woman-man","👨‍❤️‍💋‍👨":"kiss-man-man","👩‍❤️‍💋‍👩":"kiss-woman-woman","💑":"couple-with-heart","👩‍❤️‍👨":"couple-with-heart-woman-man","👨‍❤️‍👨":"couple-with-heart-man-man","👩‍❤️‍👩":"couple-with-heart-woman-woman","👪":"family","👨‍👩‍👦":"family-man-woman-boy","👨‍👩‍👧":"family-man-woman-girl","👨‍👩‍👧‍👦":"family-man-woman-girl-boy","👨‍👩‍👦‍👦":"family-man-woman-boy-boy","👨‍👩‍👧‍👧":"family-man-woman-girl-girl","👨‍👨‍👦":"family-man-man-boy","👨‍👨‍👧":"family-man-man-girl","👨‍👨‍👧‍👦":"family-man-man-girl-boy","👨‍👨‍👦‍👦":"family-man-man-boy-boy","👨‍👨‍👧‍👧":"family-man-man-girl-girl","👩‍👩‍👦":"family-woman-woman-boy","👩‍👩‍👧":"family-woman-woman-girl","👩‍👩‍👧‍👦":"family-woman-woman-girl-boy","👩‍👩‍👦‍👦":"family-woman-woman-boy-boy","👩‍👩‍👧‍👧":"family-woman-woman-girl-girl","👨‍👦":"family-man-boy","👨‍👦‍👦":"family-man-boy-boy","👨‍👧":"family-man-girl","👨‍👧‍👦":"family-man-girl-boy","👨‍👧‍👧":"family-man-girl-girl","👩‍👦":"family-woman-boy","👩‍👦‍👦":"family-woman-boy-boy","👩‍👧":"family-woman-girl","👩‍👧‍👦":"family-woman-girl-boy","👩‍👧‍👧":"family-woman-girl-girl","🗣️":"speaking-head","👤":"bust-in-silhouette","👥":"busts-in-silhouette","🫂":"people-hugging","👣":"footprints","🐵":"monkey-face","🐒":"monkey","🦍":"gorilla","🦧":"orangutan","🐶":"dog-face","🐕":"dog","🦮":"guide-dog","🐕‍🦺":"service-dog","🐩":"poodle","🐺":"wolf","🦊":"fox","🦝":"raccoon","🐱":"cat-face","🐈":"cat","🐈‍⬛":"black-cat","🦁":"lion","🐯":"tiger-face","🐅":"tiger","🐆":"leopard","🐴":"horse-face","🫎":"moose","🫏":"donkey","🐎":"horse","🦄":"unicorn","🦓":"zebra","🦌":"deer","🦬":"bison","🐮":"cow-face","🐂":"ox","🐃":"water-buffalo","🐄":"cow","🐷":"pig-face","🐖":"pig","🐗":"boar","🐽":"pig-nose","🐏":"ram","🐑":"ewe","🐐":"goat","🐪":"camel","🐫":"two-hump-camel","🦙":"llama","🦒":"giraffe","🐘":"elephant","🦣":"mammoth","🦏":"rhinoceros","🦛":"hippopotamus","🐭":"mouse-face","🐁":"mouse","🐀":"rat","🐹":"hamster","🐰":"rabbit-face","🐇":"rabbit","🐿️":"chipmunk","🦫":"beaver","🦔":"hedgehog","🦇":"bat","🐻":"bear","🐻‍❄️":"polar-bear","🐨":"koala","🐼":"panda","🦥":"sloth","🦦":"otter","🦨":"skunk","🦘":"kangaroo","🦡":"badger","🐾":"paw-prints","🦃":"turkey","🐔":"chicken","🐓":"rooster","🐣":"hatching-chick","🐤":"baby-chick","🐥":"front-facing-baby-chick","🐦":"bird","🐧":"penguin","🕊️":"dove","🦅":"eagle","🦆":"duck","🦢":"swan","🦉":"owl","🦤":"dodo","🪶":"feather","🦩":"flamingo","🦚":"peacock","🦜":"parrot","🪽":"wing","🐦‍⬛":"black-bird","🪿":"goose","🐸":"frog","🐊":"crocodile","🐢":"turtle","🦎":"lizard","🐍":"snake","🐲":"dragon-face","🐉":"dragon","🦕":"sauropod","🦖":"t-rex","🐳":"spouting-whale","🐋":"whale","🐬":"dolphin","🦭":"seal","🐟":"fish","🐠":"tropical-fish","🐡":"blowfish","🦈":"shark","🐙":"octopus","🐚":"spiral-shell","🪸":"coral","🪼":"jellyfish","🐌":"snail","🦋":"butterfly","🐛":"bug","🐜":"ant","🐝":"honeybee","🪲":"beetle","🐞":"lady-beetle","🦗":"cricket","🪳":"cockroach","🕷️":"spider","🕸️":"spider-web","🦂":"scorpion","🦟":"mosquito","🪰":"fly","🪱":"worm","🦠":"microbe","💐":"bouquet","🌸":"cherry-blossom","💮":"white-flower","🪷":"lotus","🏵️":"rosette","🌹":"rose","🥀":"wilted-flower","🌺":"hibiscus","🌻":"sunflower","🌼":"blossom","🌷":"tulip","🪻":"hyacinth","🌱":"seedling","🪴":"potted-plant","🌲":"evergreen-tree","🌳":"deciduous-tree","🌴":"palm-tree","🌵":"cactus","🌾":"sheaf-of-rice","🌿":"herb","☘️":"shamrock","🍀":"four-leaf-clover","🍁":"maple-leaf","🍂":"fallen-leaf","🍃":"leaf-fluttering-in-wind","🪹":"empty-nest","🪺":"nest-with-eggs","🍄":"mushroom","🍇":"grapes","🍈":"melon","🍉":"watermelon","🍊":"tangerine","🍋":"lemon","🍌":"banana","🍍":"pineapple","🥭":"mango","🍎":"red-apple","🍏":"green-apple","🍐":"pear","🍑":"peach","🍒":"cherries","🍓":"strawberry","🫐":"blueberries","🥝":"kiwi-fruit","🍅":"tomato","🫒":"olive","🥥":"coconut","🥑":"avocado","🍆":"eggplant","🥔":"potato","🥕":"carrot","🌽":"ear-of-corn","🌶️":"hot-pepper","🫑":"bell-pepper","🥒":"cucumber","🥬":"leafy-green","🥦":"broccoli","🧄":"garlic","🧅":"onion","🥜":"peanuts","🫘":"beans","🌰":"chestnut","🫚":"ginger-root","🫛":"pea-pod","🍞":"bread","🥐":"croissant","🥖":"baguette-bread","🫓":"flatbread","🥨":"pretzel","🥯":"bagel","🥞":"pancakes","🧇":"waffle","🧀":"cheese-wedge","🍖":"meat-on-bone","🍗":"poultry-leg","🥩":"cut-of-meat","🥓":"bacon","🍔":"hamburger","🍟":"french-fries","🍕":"pizza","🌭":"hot-dog","🥪":"sandwich","🌮":"taco","🌯":"burrito","🫔":"tamale","🥙":"stuffed-flatbread","🧆":"falafel","🥚":"egg","🍳":"cooking","🥘":"shallow-pan-of-food","🍲":"pot-of-food","🫕":"fondue","🥣":"bowl-with-spoon","🥗":"green-salad","🍿":"popcorn","🧈":"butter","🧂":"salt","🥫":"canned-food","🍱":"bento-box","🍘":"rice-cracker","🍙":"rice-ball","🍚":"cooked-rice","🍛":"curry-rice","🍜":"steaming-bowl","🍝":"spaghetti","🍠":"roasted-sweet-potato","🍢":"oden","🍣":"sushi","🍤":"fried-shrimp","🍥":"fish-cake-with-swirl","🥮":"moon-cake","🍡":"dango","🥟":"dumpling","🥠":"fortune-cookie","🥡":"takeout-box","🦀":"crab","🦞":"lobster","🦐":"shrimp","🦑":"squid","🦪":"oyster","🍦":"soft-ice-cream","🍧":"shaved-ice","🍨":"ice-cream","🍩":"doughnut","🍪":"cookie","🎂":"birthday-cake","🍰":"shortcake","🧁":"cupcake","🥧":"pie","🍫":"chocolate-bar","🍬":"candy","🍭":"lollipop","🍮":"custard","🍯":"honey-pot","🍼":"baby-bottle","🥛":"glass-of-milk","☕":"hot-beverage","🫖":"teapot","🍵":"teacup-without-handle","🍶":"sake","🍾":"bottle-with-popping-cork","🍷":"wine-glass","🍸":"cocktail-glass","🍹":"tropical-drink","🍺":"beer-mug","🍻":"clinking-beer-mugs","🥂":"clinking-glasses","🥃":"tumbler-glass","🫗":"pouring-liquid","🥤":"cup-with-straw","🧋":"bubble-tea","🧃":"beverage-box","🧉":"mate","🧊":"ice","🥢":"chopsticks","🍽️":"fork-and-knife-with-plate","🍴":"fork-and-knife","🥄":"spoon","🔪":"kitchen-knife","🫙":"jar","🏺":"amphora","🌍":"globe-showing-europe-africa","🌎":"globe-showing-americas","🌏":"globe-showing-asia-australia","🌐":"globe-with-meridians","🗺️":"world-map","🗾":"map-of-japan","🧭":"compass","🏔️":"snow-capped-mountain","⛰️":"mountain","🌋":"volcano","🗻":"mount-fuji","🏕️":"camping","🏖️":"beach-with-umbrella","🏜️":"desert","🏝️":"desert-island","🏞️":"national-park","🏟️":"stadium","🏛️":"classical-building","🏗️":"building-construction","🧱":"brick","🪨":"rock","🪵":"wood","🛖":"hut","🏘️":"houses","🏚️":"derelict-house","🏠":"house","🏡":"house-with-garden","🏢":"office-building","🏣":"japanese-post-office","🏤":"post-office","🏥":"hospital","🏦":"bank","🏨":"hotel","🏩":"love-hotel","🏪":"convenience-store","🏫":"school","🏬":"department-store","🏭":"factory","🏯":"japanese-castle","🏰":"castle","💒":"wedding","🗼":"tokyo-tower","🗽":"statue-of-liberty","⛪":"church","🕌":"mosque","🛕":"hindu-temple","🕍":"synagogue","⛩️":"shinto-shrine","🕋":"kaaba","⛲":"fountain","⛺":"tent","🌁":"foggy","🌃":"night-with-stars","🏙️":"cityscape","🌄":"sunrise-over-mountains","🌅":"sunrise","🌆":"cityscape-at-dusk","🌇":"sunset","🌉":"bridge-at-night","♨️":"hot-springs","🎠":"carousel-horse","🛝":"playground-slide","🎡":"ferris-wheel","🎢":"roller-coaster","💈":"barber-pole","🎪":"circus-tent","🚂":"locomotive","🚃":"railway-car","🚄":"high-speed-train","🚅":"bullet-train","🚆":"train","🚇":"metro","🚈":"light-rail","🚉":"station","🚊":"tram","🚝":"monorail","🚞":"mountain-railway","🚋":"tram-car","🚌":"bus","🚍":"oncoming-bus","🚎":"trolleybus","🚐":"minibus","🚑":"ambulance","🚒":"fire-engine","🚓":"police-car","🚔":"oncoming-police-car","🚕":"taxi","🚖":"oncoming-taxi","🚗":"automobile","🚘":"oncoming-automobile","🚙":"sport-utility-vehicle","🛻":"pickup-truck","🚚":"delivery-truck","🚛":"articulated-lorry","🚜":"tractor","🏎️":"racing-car","🏍️":"motorcycle","🛵":"motor-scooter","🦽":"manual-wheelchair","🦼":"motorized-wheelchair","🛺":"auto-rickshaw","🚲":"bicycle","🛴":"kick-scooter","🛹":"skateboard","🛼":"roller-skate","🚏":"bus-stop","🛣️":"motorway","🛤️":"railway-track","🛢️":"oil-drum","⛽":"fuel-pump","🛞":"wheel","🚨":"police-car-light","🚥":"horizontal-traffic-light","🚦":"vertical-traffic-light","🛑":"stop-sign","🚧":"construction","⚓":"anchor","🛟":"ring-buoy","⛵":"sailboat","🛶":"canoe","🚤":"speedboat","🛳️":"passenger-ship","⛴️":"ferry","🛥️":"motor-boat","🚢":"ship","✈️":"airplane","🛩️":"small-airplane","🛫":"airplane-departure","🛬":"airplane-arrival","🪂":"parachute","💺":"seat","🚁":"helicopter","🚟":"suspension-railway","🚠":"mountain-cableway","🚡":"aerial-tramway","🛰️":"satellite","🚀":"rocket","🛸":"flying-saucer","🛎️":"bellhop-bell","🧳":"luggage","⌛":"hourglass-done","⏳":"hourglass-not-done","⌚":"watch","⏰":"alarm-clock","⏱️":"stopwatch","⏲️":"timer-clock","🕰️":"mantelpiece-clock","🕛":"twelve-o-clock","🕧":"twelve-thirty","🕐":"one-o-clock","🕜":"one-thirty","🕑":"two-o-clock","🕝":"two-thirty","🕒":"three-o-clock","🕞":"three-thirty","🕓":"four-o-clock","🕟":"four-thirty","🕔":"five-o-clock","🕠":"five-thirty","🕕":"six-o-clock","🕡":"six-thirty","🕖":"seven-o-clock","🕢":"seven-thirty","🕗":"eight-o-clock","🕣":"eight-thirty","🕘":"nine-o-clock","🕤":"nine-thirty","🕙":"ten-o-clock","🕥":"ten-thirty","🕚":"eleven-o-clock","🕦":"eleven-thirty","🌑":"new-moon","🌒":"waxing-crescent-moon","🌓":"first-quarter-moon","🌔":"waxing-gibbous-moon","🌕":"full-moon","🌖":"waning-gibbous-moon","🌗":"last-quarter-moon","🌘":"waning-crescent-moon","🌙":"crescent-moon","🌚":"new-moon-face","🌛":"first-quarter-moon-face","🌜":"last-quarter-moon-face","🌡️":"thermometer","☀️":"sun","🌝":"full-moon-face","🌞":"sun-with-face","🪐":"ringed-planet","⭐":"star","🌟":"glowing-star","🌠":"shooting-star","🌌":"milky-way","☁️":"cloud","⛅":"sun-behind-cloud","⛈️":"cloud-with-lightning-and-rain","🌤️":"sun-behind-small-cloud","🌥️":"sun-behind-large-cloud","🌦️":"sun-behind-rain-cloud","🌧️":"cloud-with-rain","🌨️":"cloud-with-snow","🌩️":"cloud-with-lightning","🌪️":"tornado","🌫️":"fog","🌬️":"wind-face","🌀":"cyclone","🌈":"rainbow","🌂":"closed-umbrella","☂️":"umbrella","☔":"umbrella-with-rain-drops","⛱️":"umbrella-on-ground","⚡":"high-voltage","❄️":"snowflake","☃️":"snowman","⛄":"snowman-without-snow","☄️":"comet","🔥":"fire","💧":"droplet","🌊":"water-wave","🎃":"jack-o-lantern","🎄":"christmas-tree","🎆":"fireworks","🎇":"sparkler","🧨":"firecracker","✨":"sparkles","🎈":"balloon","🎉":"party-popper","🎊":"confetti-ball","🎋":"tanabata-tree","🎍":"pine-decoration","🎎":"japanese-dolls","🎏":"carp-streamer","🎐":"wind-chime","🎑":"moon-viewing-ceremony","🧧":"red-envelope","🎀":"ribbon","🎁":"wrapped-gift","🎗️":"reminder-ribbon","🎟️":"admission-tickets","🎫":"ticket","🎖️":"military-medal","🏆":"trophy","🏅":"sports-medal","🥇":"1st-place-medal","🥈":"2nd-place-medal","🥉":"3rd-place-medal","⚽":"soccer-ball","⚾":"baseball","🥎":"softball","🏀":"basketball","🏐":"volleyball","🏈":"american-football","🏉":"rugby-football","🎾":"tennis","🥏":"flying-disc","🎳":"bowling","🏏":"cricket-game","🏑":"field-hockey","🏒":"ice-hockey","🥍":"lacrosse","🏓":"ping-pong","🏸":"badminton","🥊":"boxing-glove","🥋":"martial-arts-uniform","🥅":"goal-net","⛳":"flag-in-hole","⛸️":"ice-skate","🎣":"fishing-pole","🤿":"diving-mask","🎽":"running-shirt","🎿":"skis","🛷":"sled","🥌":"curling-stone","🎯":"bullseye","🪀":"yo-yo","🪁":"kite","🔫":"water-pistol","🎱":"pool-8-ball","🔮":"crystal-ball","🪄":"magic-wand","🎮":"video-game","🕹️":"joystick","🎰":"slot-machine","🎲":"game-die","🧩":"puzzle-piece","🧸":"teddy-bear","🪅":"pinata","🪩":"mirror-ball","🪆":"nesting-dolls","♠️":"spade-suit","♥️":"heart-suit","♦️":"diamond-suit","♣️":"club-suit","♟️":"chess-pawn","🃏":"joker","🀄":"mahjong-red-dragon","🎴":"flower-playing-cards","🎭":"performing-arts","🖼️":"framed-picture","🎨":"artist-palette","🧵":"thread","🪡":"sewing-needle","🧶":"yarn","🪢":"knot","👓":"glasses","🕶️":"sunglasses","🥽":"goggles","🥼":"lab-coat","🦺":"safety-vest","👔":"necktie","👕":"t-shirt","👖":"jeans","🧣":"scarf","🧤":"gloves","🧥":"coat","🧦":"socks","👗":"dress","👘":"kimono","🥻":"sari","🩱":"one-piece-swimsuit","🩲":"briefs","🩳":"shorts","👙":"bikini","👚":"woman-s-clothes","🪭":"folding-hand-fan","👛":"purse","👜":"handbag","👝":"clutch-bag","🛍️":"shopping-bags","🎒":"backpack","🩴":"thong-sandal","👞":"man-s-shoe","👟":"running-shoe","🥾":"hiking-boot","🥿":"flat-shoe","👠":"high-heeled-shoe","👡":"woman-s-sandal","🩰":"ballet-shoes","👢":"woman-s-boot","🪮":"hair-pick","👑":"crown","👒":"woman-s-hat","🎩":"top-hat","🎓":"graduation-cap","🧢":"billed-cap","🪖":"military-helmet","⛑️":"rescue-worker-s-helmet","📿":"prayer-beads","💄":"lipstick","💍":"ring","💎":"gem-stone","🔇":"muted-speaker","🔈":"speaker-low-volume","🔉":"speaker-medium-volume","🔊":"speaker-high-volume","📢":"loudspeaker","📣":"megaphone","📯":"postal-horn","🔔":"bell","🔕":"bell-with-slash","🎼":"musical-score","🎵":"musical-note","🎶":"musical-notes","🎙️":"studio-microphone","🎚️":"level-slider","🎛️":"control-knobs","🎤":"microphone","🎧":"headphone","📻":"radio","🎷":"saxophone","🪗":"accordion","🎸":"guitar","🎹":"musical-keyboard","🎺":"trumpet","🎻":"violin","🪕":"banjo","🥁":"drum","🪘":"long-drum","🪇":"maracas","🪈":"flute","📱":"mobile-phone","📲":"mobile-phone-with-arrow","☎️":"telephone","📞":"telephone-receiver","📟":"pager","📠":"fax-machine","🔋":"battery","🪫":"low-battery","🔌":"electric-plug","💻":"laptop","🖥️":"desktop-computer","🖨️":"printer","⌨️":"keyboard","🖱️":"computer-mouse","🖲️":"trackball","💽":"computer-disk","💾":"floppy-disk","💿":"optical-disk","📀":"dvd","🧮":"abacus","🎥":"movie-camera","🎞️":"film-frames","📽️":"film-projector","🎬":"clapper-board","📺":"television","📷":"camera","📸":"camera-with-flash","📹":"video-camera","📼":"videocassette","🔍":"magnifying-glass-tilted-left","🔎":"magnifying-glass-tilted-right","🕯️":"candle","💡":"light-bulb","🔦":"flashlight","🏮":"red-paper-lantern","🪔":"diya-lamp","📔":"notebook-with-decorative-cover","📕":"closed-book","📖":"open-book","📗":"green-book","📘":"blue-book","📙":"orange-book","📚":"books","📓":"notebook","📒":"ledger","📃":"page-with-curl","📜":"scroll","📄":"page-facing-up","📰":"newspaper","🗞️":"rolled-up-newspaper","📑":"bookmark-tabs","🔖":"bookmark","🏷️":"label","💰":"money-bag","🪙":"coin","💴":"yen-banknote","💵":"dollar-banknote","💶":"euro-banknote","💷":"pound-banknote","💸":"money-with-wings","💳":"credit-card","🧾":"receipt","💹":"chart-increasing-with-yen","✉️":"envelope","📧":"e-mail","📨":"incoming-envelope","📩":"envelope-with-arrow","📤":"outbox-tray","📥":"inbox-tray","📦":"package","📫":"closed-mailbox-with-raised-flag","📪":"closed-mailbox-with-lowered-flag","📬":"open-mailbox-with-raised-flag","📭":"open-mailbox-with-lowered-flag","📮":"postbox","🗳️":"ballot-box-with-ballot","✏️":"pencil","✒️":"black-nib","🖋️":"fountain-pen","🖊️":"pen","🖌️":"paintbrush","🖍️":"crayon","📝":"memo","💼":"briefcase","📁":"file-folder","📂":"open-file-folder","🗂️":"card-index-dividers","📅":"calendar","📆":"tear-off-calendar","🗒️":"spiral-notepad","🗓️":"spiral-calendar","📇":"card-index","📈":"chart-increasing","📉":"chart-decreasing","📊":"bar-chart","📋":"clipboard","📌":"pushpin","📍":"round-pushpin","📎":"paperclip","🖇️":"linked-paperclips","📏":"straight-ruler","📐":"triangular-ruler","✂️":"scissors","🗃️":"card-file-box","🗄️":"file-cabinet","🗑️":"wastebasket","🔒":"locked","🔓":"unlocked","🔏":"locked-with-pen","🔐":"locked-with-key","🔑":"key","🗝️":"old-key","🔨":"hammer","🪓":"axe","⛏️":"pick","⚒️":"hammer-and-pick","🛠️":"hammer-and-wrench","🗡️":"dagger","⚔️":"crossed-swords","💣":"bomb","🪃":"boomerang","🏹":"bow-and-arrow","🛡️":"shield","🪚":"carpentry-saw","🔧":"wrench","🪛":"screwdriver","🔩":"nut-and-bolt","⚙️":"gear","🗜️":"clamp","⚖️":"balance-scale","🦯":"white-cane","🔗":"link","⛓️":"chains","🪝":"hook","🧰":"toolbox","🧲":"magnet","🪜":"ladder","⚗️":"alembic","🧪":"test-tube","🧫":"petri-dish","🧬":"dna","🔬":"microscope","🔭":"telescope","📡":"satellite-antenna","💉":"syringe","🩸":"drop-of-blood","💊":"pill","🩹":"adhesive-bandage","🩼":"crutch","🩺":"stethoscope","🩻":"x-ray","🚪":"door","🛗":"elevator","🪞":"mirror","🪟":"window","🛏️":"bed","🛋️":"couch-and-lamp","🪑":"chair","🚽":"toilet","🪠":"plunger","🚿":"shower","🛁":"bathtub","🪤":"mouse-trap","🪒":"razor","🧴":"lotion-bottle","🧷":"safety-pin","🧹":"broom","🧺":"basket","🧻":"roll-of-paper","🪣":"bucket","🧼":"soap","🫧":"bubbles","🪥":"toothbrush","🧽":"sponge","🧯":"fire-extinguisher","🛒":"shopping-cart","🚬":"cigarette","⚰️":"coffin","🪦":"headstone","⚱️":"funeral-urn","🧿":"nazar-amulet","🪬":"hamsa","🗿":"moai","🪧":"placard","🪪":"identification-card","🏧":"atm-sign","🚮":"litter-in-bin-sign","🚰":"potable-water","♿":"wheelchair-symbol","🚹":"men-s-room","🚺":"women-s-room","🚻":"restroom","🚼":"baby-symbol","🚾":"water-closet","🛂":"passport-control","🛃":"customs","🛄":"baggage-claim","🛅":"left-luggage","⚠️":"warning","🚸":"children-crossing","⛔":"no-entry","🚫":"prohibited","🚳":"no-bicycles","🚭":"no-smoking","🚯":"no-littering","🚱":"non-potable-water","🚷":"no-pedestrians","📵":"no-mobile-phones","🔞":"no-one-under-eighteen","☢️":"radioactive","☣️":"biohazard","⬆️":"up-arrow","↗️":"up-right-arrow","➡️":"right-arrow","↘️":"down-right-arrow","⬇️":"down-arrow","↙️":"down-left-arrow","⬅️":"left-arrow","↖️":"up-left-arrow","↕️":"up-down-arrow","↔️":"left-right-arrow","↩️":"right-arrow-curving-left","↪️":"left-arrow-curving-right","⤴️":"right-arrow-curving-up","⤵️":"right-arrow-curving-down","🔃":"clockwise-vertical-arrows","🔄":"counterclockwise-arrows-button","🔙":"back-arrow","🔚":"end-arrow","🔛":"on-arrow","🔜":"soon-arrow","🔝":"top-arrow","🛐":"place-of-worship","⚛️":"atom-symbol","🕉️":"om","✡️":"star-of-david","☸️":"wheel-of-dharma","☯️":"yin-yang","✝️":"latin-cross","☦️":"orthodox-cross","☪️":"star-and-crescent","☮️":"peace-symbol","🕎":"menorah","🔯":"dotted-six-pointed-star","🪯":"khanda","♈":"aries","♉":"taurus","♊":"gemini","♋":"cancer","♌":"leo","♍":"virgo","♎":"libra","♏":"scorpio","♐":"sagittarius","♑":"capricorn","♒":"aquarius","♓":"pisces","⛎":"ophiuchus","🔀":"shuffle-tracks-button","🔁":"repeat-button","🔂":"repeat-single-button","▶️":"play-button","⏩":"fast-forward-button","⏭️":"next-track-button","⏯️":"play-or-pause-button","◀️":"reverse-button","⏪":"fast-reverse-button","⏮️":"last-track-button","🔼":"upwards-button","⏫":"fast-up-button","🔽":"downwards-button","⏬":"fast-down-button","⏸️":"pause-button","⏹️":"stop-button","⏺️":"record-button","⏏️":"eject-button","🎦":"cinema","🔅":"dim-button","🔆":"bright-button","📶":"antenna-bars","🛜":"wireless","📳":"vibration-mode","📴":"mobile-phone-off","♀️":"female-sign","♂️":"male-sign","⚧️":"transgender-symbol","✖️":"multiply","➕":"plus","➖":"minus","➗":"divide","🟰":"heavy-equals-sign","♾️":"infinity","‼️":"double-exclamation-mark","⁉️":"exclamation-question-mark","❓":"red-question-mark","❔":"white-question-mark","❕":"white-exclamation-mark","❗":"red-exclamation-mark","〰️":"wavy-dash","💱":"currency-exchange","💲":"heavy-dollar-sign","⚕️":"medical-symbol","♻️":"recycling-symbol","⚜️":"fleur-de-lis","🔱":"trident-emblem","📛":"name-badge","🔰":"japanese-symbol-for-beginner","⭕":"hollow-red-circle","✅":"check-mark-button","☑️":"check-box-with-check","✔️":"check-mark","❌":"cross-mark","❎":"cross-mark-button","➰":"curly-loop","➿":"double-curly-loop","〽️":"part-alternation-mark","✳️":"eight-spoked-asterisk","✴️":"eight-pointed-star","❇️":"sparkle","©️":"copyright","®️":"registered","™️":"trade-mark","#️⃣":"keycap-#","*️⃣":"keycap-*","0️⃣":"keycap-0","1️⃣":"keycap-1","2️⃣":"keycap-2","3️⃣":"keycap-3","4️⃣":"keycap-4","5️⃣":"keycap-5","6️⃣":"keycap-6","7️⃣":"keycap-7","8️⃣":"keycap-8","9️⃣":"keycap-9","🔟":"keycap-10","🔠":"input-latin-uppercase","🔡":"input-latin-lowercase","🔢":"input-numbers","🔣":"input-symbols","🔤":"input-latin-letters","🅰️":"a-button-blood-type","🆎":"ab-button-blood-type","🅱️":"b-button-blood-type","🆑":"cl-button","🆒":"cool-button","🆓":"free-button","ℹ️":"information","🆔":"id-button","Ⓜ️":"circled-m","🆕":"new-button","🆖":"ng-button","🅾️":"o-button-blood-type","🆗":"ok-button","🅿️":"p-button","🆘":"sos-button","🆙":"up-button","🆚":"vs-button","🈁":"japanese-here-button","🈂️":"japanese-service-charge-button","🈷️":"japanese-monthly-amount-button","🈶":"japanese-not-free-of-charge-button","🈯":"japanese-reserved-button","🉐":"japanese-bargain-button","🈹":"japanese-discount-button","🈚":"japanese-free-of-charge-button","🈲":"japanese-prohibited-button","🉑":"japanese-acceptable-button","🈸":"japanese-application-button","🈴":"japanese-passing-grade-button","🈳":"japanese-vacancy-button","㊗️":"japanese-congratulations-button","㊙️":"japanese-secret-button","🈺":"japanese-open-for-business-button","🈵":"japanese-no-vacancy-button","🔴":"red-circle","🟠":"orange-circle","🟡":"yellow-circle","🟢":"green-circle","🔵":"blue-circle","🟣":"purple-circle","🟤":"brown-circle","⚫":"black-circle","⚪":"white-circle","🟥":"red-square","🟧":"orange-square","🟨":"yellow-square","🟩":"green-square","🟦":"blue-square","🟪":"purple-square","🟫":"brown-square","⬛":"black-large-square","⬜":"white-large-square","◼️":"black-medium-square","◻️":"white-medium-square","◾":"black-medium-small-square","◽":"white-medium-small-square","▪️":"black-small-square","▫️":"white-small-square","🔶":"large-orange-diamond","🔷":"large-blue-diamond","🔸":"small-orange-diamond","🔹":"small-blue-diamond","🔺":"red-triangle-pointed-up","🔻":"red-triangle-pointed-down","💠":"diamond-with-a-dot","🔘":"radio-button","🔳":"white-square-button","🔲":"black-square-button","🏁":"chequered-flag","🚩":"triangular-flag","🎌":"crossed-flags","🏴":"black-flag","🏳️":"white-flag","🏳️‍🌈":"rainbow-flag","🏳️‍⚧️":"transgender-flag","🏴‍☠️":"pirate-flag","🇦🇨":"flag-ascension-island","🇦🇩":"flag-andorra","🇦🇪":"flag-united-arab-emirates","🇦🇫":"flag-afghanistan","🇦🇬":"flag-antigua-&-barbuda","🇦🇮":"flag-anguilla","🇦🇱":"flag-albania","🇦🇲":"flag-armenia","🇦🇴":"flag-angola","🇦🇶":"flag-antarctica","🇦🇷":"flag-argentina","🇦🇸":"flag-american-samoa","🇦🇹":"flag-austria","🇦🇺":"flag-australia","🇦🇼":"flag-aruba","🇦🇽":"flag-aland-islands","🇦🇿":"flag-azerbaijan","🇧🇦":"flag-bosnia-&-herzegovina","🇧🇧":"flag-barbados","🇧🇩":"flag-bangladesh","🇧🇪":"flag-belgium","🇧🇫":"flag-burkina-faso","🇧🇬":"flag-bulgaria","🇧🇭":"flag-bahrain","🇧🇮":"flag-burundi","🇧🇯":"flag-benin","🇧🇱":"flag-st-barthelemy","🇧🇲":"flag-bermuda","🇧🇳":"flag-brunei","🇧🇴":"flag-bolivia","🇧🇶":"flag-caribbean-netherlands","🇧🇷":"flag-brazil","🇧🇸":"flag-bahamas","🇧🇹":"flag-bhutan","🇧🇻":"flag-bouvet-island","🇧🇼":"flag-botswana","🇧🇾":"flag-belarus","🇧🇿":"flag-belize","🇨🇦":"flag-canada","🇨🇨":"flag-cocos-keeling-islands","🇨🇩":"flag-congo-kinshasa","🇨🇫":"flag-central-african-republic","🇨🇬":"flag-congo-brazzaville","🇨🇭":"flag-switzerland","🇨🇮":"flag-cote-d-ivoire","🇨🇰":"flag-cook-islands","🇨🇱":"flag-chile","🇨🇲":"flag-cameroon","🇨🇳":"flag-china","🇨🇴":"flag-colombia","🇨🇵":"flag-clipperton-island","🇨🇷":"flag-costa-rica","🇨🇺":"flag-cuba","🇨🇻":"flag-cape-verde","🇨🇼":"flag-curacao","🇨🇽":"flag-christmas-island","🇨🇾":"flag-cyprus","🇨🇿":"flag-czechia","🇩🇪":"flag-germany","🇩🇬":"flag-diego-garcia","🇩🇯":"flag-djibouti","🇩🇰":"flag-denmark","🇩🇲":"flag-dominica","🇩🇴":"flag-dominican-republic","🇩🇿":"flag-algeria","🇪🇦":"flag-ceuta-&-melilla","🇪🇨":"flag-ecuador","🇪🇪":"flag-estonia","🇪🇬":"flag-egypt","🇪🇭":"flag-western-sahara","🇪🇷":"flag-eritrea","🇪🇸":"flag-spain","🇪🇹":"flag-ethiopia","🇪🇺":"flag-european-union","🇫🇮":"flag-finland","🇫🇯":"flag-fiji","🇫🇰":"flag-falkland-islands","🇫🇲":"flag-micronesia","🇫🇴":"flag-faroe-islands","🇫🇷":"flag-france","🇬🇦":"flag-gabon","🇬🇧":"flag-united-kingdom","🇬🇩":"flag-grenada","🇬🇪":"flag-georgia","🇬🇫":"flag-french-guiana","🇬🇬":"flag-guernsey","🇬🇭":"flag-ghana","🇬🇮":"flag-gibraltar","🇬🇱":"flag-greenland","🇬🇲":"flag-gambia","🇬🇳":"flag-guinea","🇬🇵":"flag-guadeloupe","🇬🇶":"flag-equatorial-guinea","🇬🇷":"flag-greece","🇬🇸":"flag-south-georgia-&-south-sandwich-islands","🇬🇹":"flag-guatemala","🇬🇺":"flag-guam","🇬🇼":"flag-guinea-bissau","🇬🇾":"flag-guyana","🇭🇰":"flag-hong-kong-sar-china","🇭🇲":"flag-heard-&-mcdonald-islands","🇭🇳":"flag-honduras","🇭🇷":"flag-croatia","🇭🇹":"flag-haiti","🇭🇺":"flag-hungary","🇮🇨":"flag-canary-islands","🇮🇩":"flag-indonesia","🇮🇪":"flag-ireland","🇮🇱":"flag-israel","🇮🇲":"flag-isle-of-man","🇮🇳":"flag-india","🇮🇴":"flag-british-indian-ocean-territory","🇮🇶":"flag-iraq","🇮🇷":"flag-iran","🇮🇸":"flag-iceland","🇮🇹":"flag-italy","🇯🇪":"flag-jersey","🇯🇲":"flag-jamaica","🇯🇴":"flag-jordan","🇯🇵":"flag-japan","🇰🇪":"flag-kenya","🇰🇬":"flag-kyrgyzstan","🇰🇭":"flag-cambodia","🇰🇮":"flag-kiribati","🇰🇲":"flag-comoros","🇰🇳":"flag-st-kitts-&-nevis","🇰🇵":"flag-north-korea","🇰🇷":"flag-south-korea","🇰🇼":"flag-kuwait","🇰🇾":"flag-cayman-islands","🇰🇿":"flag-kazakhstan","🇱🇦":"flag-laos","🇱🇧":"flag-lebanon","🇱🇨":"flag-st-lucia","🇱🇮":"flag-liechtenstein","🇱🇰":"flag-sri-lanka","🇱🇷":"flag-liberia","🇱🇸":"flag-lesotho","🇱🇹":"flag-lithuania","🇱🇺":"flag-luxembourg","🇱🇻":"flag-latvia","🇱🇾":"flag-libya","🇲🇦":"flag-morocco","🇲🇨":"flag-monaco","🇲🇩":"flag-moldova","🇲🇪":"flag-montenegro","🇲🇫":"flag-st-martin","🇲🇬":"flag-madagascar","🇲🇭":"flag-marshall-islands","🇲🇰":"flag-north-macedonia","🇲🇱":"flag-mali","🇲🇲":"flag-myanmar-burma","🇲🇳":"flag-mongolia","🇲🇴":"flag-macao-sar-china","🇲🇵":"flag-northern-mariana-islands","🇲🇶":"flag-martinique","🇲🇷":"flag-mauritania","🇲🇸":"flag-montserrat","🇲🇹":"flag-malta","🇲🇺":"flag-mauritius","🇲🇻":"flag-maldives","🇲🇼":"flag-malawi","🇲🇽":"flag-mexico","🇲🇾":"flag-malaysia","🇲🇿":"flag-mozambique","🇳🇦":"flag-namibia","🇳🇨":"flag-new-caledonia","🇳🇪":"flag-niger","🇳🇫":"flag-norfolk-island","🇳🇬":"flag-nigeria","🇳🇮":"flag-nicaragua","🇳🇱":"flag-netherlands","🇳🇴":"flag-norway","🇳🇵":"flag-nepal","🇳🇷":"flag-nauru","🇳🇺":"flag-niue","🇳🇿":"flag-new-zealand","🇴🇲":"flag-oman","🇵🇦":"flag-panama","🇵🇪":"flag-peru","🇵🇫":"flag-french-polynesia","🇵🇬":"flag-papua-new-guinea","🇵🇭":"flag-philippines","🇵🇰":"flag-pakistan","🇵🇱":"flag-poland","🇵🇲":"flag-st-pierre-&-miquelon","🇵🇳":"flag-pitcairn-islands","🇵🇷":"flag-puerto-rico","🇵🇸":"flag-palestinian-territories","🇵🇹":"flag-portugal","🇵🇼":"flag-palau","🇵🇾":"flag-paraguay","🇶🇦":"flag-qatar","🇷🇪":"flag-reunion","🇷🇴":"flag-romania","🇷🇸":"flag-serbia","🇷🇺":"flag-russia","🇷🇼":"flag-rwanda","🇸🇦":"flag-saudi-arabia","🇸🇧":"flag-solomon-islands","🇸🇨":"flag-seychelles","🇸🇩":"flag-sudan","🇸🇪":"flag-sweden","🇸🇬":"flag-singapore","🇸🇭":"flag-st-helena","🇸🇮":"flag-slovenia","🇸🇯":"flag-svalbard-&-jan-mayen","🇸🇰":"flag-slovakia","🇸🇱":"flag-sierra-leone","🇸🇲":"flag-san-marino","🇸🇳":"flag-senegal","🇸🇴":"flag-somalia","🇸🇷":"flag-suriname","🇸🇸":"flag-south-sudan","🇸🇹":"flag-sao-tome-&-principe","🇸🇻":"flag-el-salvador","🇸🇽":"flag-sint-maarten","🇸🇾":"flag-syria","🇸🇿":"flag-eswatini","🇹🇦":"flag-tristan-da-cunha","🇹🇨":"flag-turks-&-caicos-islands","🇹🇩":"flag-chad","🇹🇫":"flag-french-southern-territories","🇹🇬":"flag-togo","🇹🇭":"flag-thailand","🇹🇯":"flag-tajikistan","🇹🇰":"flag-tokelau","🇹🇱":"flag-timor-leste","🇹🇲":"flag-turkmenistan","🇹🇳":"flag-tunisia","🇹🇴":"flag-tonga","🇹🇷":"flag-turkey","🇹🇹":"flag-trinidad-&-tobago","🇹🇻":"flag-tuvalu","🇹🇼":"flag-taiwan","🇹🇿":"flag-tanzania","🇺🇦":"flag-ukraine","🇺🇬":"flag-uganda","🇺🇲":"flag-us-outlying-islands","🇺🇳":"flag-united-nations","🇺🇸":"flag-united-states","🇺🇾":"flag-uruguay","🇺🇿":"flag-uzbekistan","🇻🇦":"flag-vatican-city","🇻🇨":"flag-st-vincent-&-grenadines","🇻🇪":"flag-venezuela","🇻🇬":"flag-british-virgin-islands","🇻🇮":"flag-us-virgin-islands","🇻🇳":"flag-vietnam","🇻🇺":"flag-vanuatu","🇼🇫":"flag-wallis-&-futuna","🇼🇸":"flag-samoa","🇽🇰":"flag-kosovo","🇾🇪":"flag-yemen","🇾🇹":"flag-mayotte","🇿🇦":"flag-south-africa","🇿🇲":"flag-zambia","🇿🇼":"flag-zimbabwe","🏴󠁧󠁢󠁥󠁮󠁧󠁿":"flag-england","🏴󠁧󠁢󠁳󠁣󠁴󠁿":"flag-scotland","🏴󠁧󠁢󠁷󠁬󠁳󠁿":"flag-wales"}},{}],11:[function(e,t,a){t.exports={10:["🔟"],grin:["😀","😃","😄","😆","😅","😺","😸"],face:["😀","😃","😄","😁","😆","😅","😂","🙂","🙃","🫠","😉","😊","😇","🥰","😍","😘","😗","☺️","😚","😙","🥲","😋","😛","😜","🤪","😝","🤑","🤗","🤭","🫢","🫣","🤫","🤔","🫡","🤐","🤨","😐","😑","😶","🫥","😶‍🌫️","😏","😒","🙄","😬","😮‍💨","🤥","🫨","😌","😔","😪","🤤","😴","😷","🤒","🤕","🤢","🤮","🤧","🥵","🥶","🥴","😵","😵‍💫","🤠","🥳","🥸","😎","🤓","🧐","😕","🫤","😟","🙁","☹️","😮","😯","😲","😳","🥺","🥹","😦","😧","😨","😰","😥","😢","😭","😱","😖","😣","😞","😓","😩","😫","🥱","😤","😡","😠","🤬","😈","👿","🤡","🤛","🤜","🐵","🐶","🐱","🐯","🐴","🐮","🐷","🐭","🐰","🐥","🐲","🌚","🌛","🌜","🌝","🌞","🌬️","📄"],big:["😃"],eyes:["😃","😄","😁","😊","😍","😚","😙","🫢","🙄","😵","😵‍💫","😸","😻","👀"],smile:["😄","😁","🙂","😊","😇","🥰","😍","☺️","😙","🥲","🤗","😎","😈","😸","😻","😼"],beam:["😁"],squint:["😆","😝"],sweat:["😅","😰","😓","💦"],roll:["🤣","🙄","🧻"],on:["🤣","🤬","❤️‍🔥","🍖","⛱️","🔛"],the:["🤣","🤘","🫵"],floor:["🤣"],laugh:["🤣"],tears:["😂","🥹","😹"],joy:["😂","😹"],slightly:["🙂","🙁"],upside:["🙃"],down:["🙃","🫳","👇","👎","↘️","⬇️","↙️","↕️","⤵️","⏬","🔻"],melt:["🫠"],wink:["😉","😜"],halo:["😇"],hearts:["🥰","💞","💕"],heart:["😍","😻","💘","💝","💖","💗","💓","💟","❣️","💔","❤️‍🔥","❤️‍🩹","❤️","🩷","🧡","💛","💚","💙","🩵","💜","🤎","🖤","🩶","🤍","🫶","🫀","💑","👩‍❤️‍👨","👨‍❤️‍👨","👩‍❤️‍👩","♥️"],star:["🤩","⭐","🌟","🌠","✡️","☪️","🔯","✴️"],struck:["🤩"],blow:["😘"],kiss:["😘","😗","😚","😙","😽","💋","💏","👩‍❤️‍💋‍👨","👨‍❤️‍💋‍👨","👩‍❤️‍💋‍👩"],closed:["😚","🌂","📕","📫","📪"],tear:["🥲","📆"],savore:["😋"],food:["😋","🥘","🍲","🥫"],tongue:["😛","😜","😝","👅"],zany:["🤪"],money:["🤑","💰","💸"],mouth:["🤑","🤭","🫢","🤐","😶","🫤","😮","😦","🤬","👄"],open:["🤗","🫢","😮","😦","👐","📖","📬","📭","📂","🈺"],hands:["🤗","👏","🙌","🫶","👐","🙏","🧑‍🤝‍🧑","👭","👫","👬"],hand:["🤭","🫢","👋","🤚","🖐️","✋","🫱","🫲","🫳","🫴","🫷","🫸","👌","🤏","✌️","🫰","🤙","✍️","💁","💁‍♂️","💁‍♀️","🙋","🙋‍♂️","🙋‍♀️","🪭"],over:["🤭","🫢","🌄"],and:["🫢","☠️","🫰","👫","🍽️","🍴","⛈️","⚒️","🛠️","🏹","🔩","🛋️","☪️"],peek:["🫣"],eye:["🫣","👁️‍🗨️","👁️"],shush:["🤫"],think:["🤔"],salute:["🫡","🖖"],zipper:["🤐"],raised:["🤨","🤚","✋","✊","📫","📬"],eyebrow:["🤨"],neutral:["😐"],expressionless:["😑"],without:["😶","🍵","⛄"],dotted:["🫥","🔯"],line:["🫥"],in:["😶‍🌫️","😱","👁️‍🗨️","🤵","🤵‍♂️","🤵‍♀️","🧑‍🦼","👨‍🦼","👩‍🦼","🧑‍🦽","👨‍🦽","👩‍🦽","🕴️","🧖","🧖‍♂️","🧖‍♀️","🧘","🧘‍♂️","🧘‍♀️","🛌","👤","👥","🍃","⛳","🚮"],clouds:["😶‍🌫️"],smirk:["😏"],unamused:["😒"],grimace:["😬"],exhale:["😮‍💨"],lying:["🤥"],shake:["🫨"],relieved:["😌","😥"],pensive:["😔"],sleepy:["😪"],drool:["🤤"],sleep:["😴"],medical:["😷","⚕️"],mask:["😷","🤿"],thermometer:["🤒","🌡️"],head:["🤕","🤯","🗣️"],bandage:["🤕","🩹"],nauseated:["🤢"],vomite:["🤮"],sneez:["🤧"],hot:["🥵","🌶️","🌭","☕","♨️"],cold:["🥶"],woozy:["🥴"],crossed:["😵","🤞","🫰","⚔️","🎌"],out:["😵"],spiral:["😵‍💫","🐚","🗒️","🗓️"],explode:["🤯"],cowboy:["🤠"],hat:["🤠","👒","🎩"],party:["🥳","🎉"],disguised:["🥸"],sunglasses:["😎","🕶️"],nerd:["🤓"],monocle:["🧐"],confused:["😕"],diagonal:["🫤"],worried:["😟"],frown:["🙁","☹️","😦","🙍","🙍‍♂️","🙍‍♀️"],hushed:["😯"],astonished:["😲"],flushed:["😳"],plead:["🥺"],hold:["🥹","🧑‍🤝‍🧑","👭","👫","👬"],back:["🥹","🤚","🔙"],anguished:["😧"],fearful:["😨"],anxious:["😰"],sad:["😥"],but:["😥"],cry:["😢","😭","😿"],loudly:["😭"],scream:["😱"],fear:["😱"],confounded:["😖"],persever:["😣"],disappointed:["😞"],downcast:["😓"],weary:["😩","🙀"],tired:["😫"],yawn:["🥱"],steam:["😤","🍜"],from:["😤"],nose:["😤","👃","🐽"],enraged:["😡"],angry:["😠","👿"],symbols:["🤬","🔣"],horns:["😈","👿","🤘"],skull:["💀","☠️"],crossbones:["☠️"],pile:["💩"],poo:["💩"],clown:["🤡"],ogre:["👹"],goblin:["👺"],ghost:["👻"],alien:["👽","👾"],monster:["👾"],robot:["🤖"],cat:["😺","😸","😹","😻","😼","😽","🙀","😿","😾","🐱","🐈","🐈‍⬛"],wry:["😼"],pout:["😾","🙎","🙎‍♂️","🙎‍♀️"],see:["🙈"],no:["🙈","🙉","🙊","🙅","🙅‍♂️","🙅‍♀️","⛔","🚳","🚭","🚯","🚷","📵","🔞","🈵"],evil:["🙈","🙉","🙊"],monkey:["🙈","🙉","🙊","🐵","🐒"],hear:["🙉","🦻"],speak:["🙊","🗣️"],love:["💌","🤟","🏩"],letter:["💌"],arrow:["💘","📲","📩","🏹","⬆️","↗️","➡️","↘️","⬇️","↙️","⬅️","↖️","↕️","↔️","↩️","↪️","⤴️","⤵️","🔙","🔚","🔛","🔜","🔝"],ribbon:["💝","🎀","🎗️"],sparkle:["💖","❇️"],grow:["💗"],beat:["💓"],revolve:["💞"],two:["💕","🐫","🕑","🕝"],decoration:["💟","🎍"],exclamation:["❣️","‼️","⁉️","❕","❗"],broken:["💔"],fire:["❤️‍🔥","🚒","🔥","🧯"],mend:["❤️‍🩹"],red:["❤️","👨‍🦰","👩‍🦰","🧑‍🦰","🍎","🧧","🀄","🏮","❓","❗","⭕","🔴","🟥","🔺","🔻"],pink:["🩷"],orange:["🧡","📙","🟠","🟧","🔶","🔸"],yellow:["💛","🟡","🟨"],green:["💚","🍏","🥬","🥗","📗","🟢","🟩"],blue:["💙","🩵","📘","🔵","🟦","🔷","🔹"],light:["🩵","🚈","🚨","🚥","🚦","💡"],purple:["💜","🟣","🟪"],brown:["🤎","🟤","🟫"],black:["🖤","🐈‍⬛","🐦‍⬛","✒️","⚫","⬛","◼️","◾","▪️","🔲","🏴"],grey:["🩶"],white:["🤍","👨‍🦳","👩‍🦳","🧑‍🦳","🧑‍🦯","👨‍🦯","👩‍🦯","💮","🦯","❔","❕","⚪","⬜","◻️","◽","▫️","🔳","🏳️"],mark:["💋","‼️","⁉️","❓","❔","❕","❗","✅","✔️","❌","❎","〽️","™️"],hundred:["💯"],points:["💯"],anger:["💢","🗯️"],symbol:["💢","♿","🚼","⚛️","☮️","⚧️","⚕️","♻️","🔰"],collision:["💥"],dizzy:["💫"],droplets:["💦"],dash:["💨","〰️"],away:["💨"],hole:["🕳️","⛳"],speech:["💬","👁️‍🗨️","🗨️"],balloon:["💬","💭","🎈"],bubble:["👁️‍🗨️","🗨️","🗯️","🧋"],left:["🗨️","👈","🤛","🔍","🛅","↙️","⬅️","↖️","↔️","↩️","↪️"],right:["🗯️","👉","🤜","🔎","↗️","➡️","↘️","↔️","↩️","↪️","⤴️","⤵️"],thought:["💭"],zzz:["💤"],wave:["👋","🌊"],fingers:["🖐️","🤌","🤞"],splayed:["🖐️"],vulcan:["🖖"],rightwards:["🫱","🫸"],leftwards:["🫲","🫷"],palm:["🫳","🫴","🌴"],up:["🫴","👆","☝️","👍","🤲","📄","🗞️","⬆️","↗️","↖️","↕️","⤴️","⏫","🆙","🔺"],push:["🫷","🫸"],ok:["👌","🙆","🙆‍♂️","🙆‍♀️","🆗"],pinched:["🤌"],pinch:["🤏"],victory:["✌️"],index:["🫰","👈","👉","👆","👇","☝️","🫵","🗂️","📇"],finger:["🫰","🖕"],thumb:["🫰"],you:["🤟"],gesture:["🤟","🙅","🙅‍♂️","🙅‍♀️","🙆","🙆‍♂️","🙆‍♀️"],sign:["🤘","🛑","🏧","🚮","♀️","♂️","🟰","💲"],call:["🤙"],me:["🤙"],backhand:["👈","👉","👆","👇"],point:["👈","👉","👆","👇","☝️","🫵"],middle:["🖕"],at:["🫵","🌆","🌉"],viewer:["🫵"],thumbs:["👍","👎"],fist:["✊","👊","🤛","🤜"],oncome:["👊","🚍","🚔","🚖","🚘"],clap:["👏"],raise:["🙌","🙋","🙋‍♂️","🙋‍♀️"],palms:["🤲"],together:["🤲"],handshake:["🤝"],folded:["🙏"],write:["✍️"],nail:["💅"],polish:["💅"],selfie:["🤳"],flexed:["💪"],biceps:["💪"],mechanical:["🦾","🦿"],arm:["🦾"],leg:["🦿","🦵","🍗"],foot:["🦶"],ear:["👂","🦻","🌽"],aid:["🦻"],brain:["🧠"],anatomical:["🫀"],lungs:["🫁"],tooth:["🦷"],bone:["🦴","🍖"],bite:["🫦"],lip:["🫦"],baby:["👶","👩‍🍼","👨‍🍼","🧑‍🍼","👼","🐤","🐥","🍼","🚼"],child:["🧒"],boy:["👦","👨‍👩‍👦","👨‍👩‍👧‍👦","👨‍👩‍👦‍👦","👨‍👨‍👦","👨‍👨‍👧‍👦","👨‍👨‍👦‍👦","👩‍👩‍👦","👩‍👩‍👧‍👦","👩‍👩‍👦‍👦","👨‍👦","👨‍👦‍👦","👨‍👧‍👦","👩‍👦","👩‍👦‍👦","👩‍👧‍👦"],girl:["👧","👨‍👩‍👧","👨‍👩‍👧‍👦","👨‍👩‍👧‍👧","👨‍👨‍👧","👨‍👨‍👧‍👦","👨‍👨‍👧‍👧","👩‍👩‍👧","👩‍👩‍👧‍👦","👩‍👩‍👧‍👧","👨‍👧","👨‍👧‍👦","👨‍👧‍👧","👩‍👧","👩‍👧‍👦","👩‍👧‍👧"],person:["🧑","👱","🧔","🧑‍🦰","🧑‍🦱","🧑‍🦳","🧑‍🦲","🧓","🙍","🙎","🙅","🙆","💁","🙋","🧏","🙇","🤦","🤷","🫅","👳","👲","🤵","👰","🫄","🧑‍🍼","💆","💇","🚶","🧍","🧎","🧑‍🦯","🧑‍🦼","🧑‍🦽","🏃","🕴️","🧖","🧗","🤺","🏌️","🏄","🚣","🏊","⛹️","🏋️","🚴","🚵","🤸","🤽","🤾","🤹","🧘","🛀","🛌"],blond:["👱","👱‍♀️","👱‍♂️"],hair:["👱","👨‍🦰","👨‍🦱","👨‍🦳","👩‍🦰","🧑‍🦰","👩‍🦱","🧑‍🦱","👩‍🦳","🧑‍🦳","👱‍♀️","👱‍♂️","🪮"],man:["👨","🧔‍♂️","👨‍🦰","👨‍🦱","👨‍🦳","👨‍🦲","👱‍♂️","👴","🙍‍♂️","🙎‍♂️","🙅‍♂️","🙆‍♂️","💁‍♂️","🙋‍♂️","🧏‍♂️","🙇‍♂️","🤦‍♂️","🤷‍♂️","👨‍⚕️","👨‍🎓","👨‍🏫","👨‍⚖️","👨‍🌾","👨‍🍳","👨‍🔧","👨‍🏭","👨‍💼","👨‍🔬","👨‍💻","👨‍🎤","👨‍🎨","👨‍✈️","👨‍🚀","👨‍🚒","👮‍♂️","🕵️‍♂️","💂‍♂️","👷‍♂️","👳‍♂️","🤵‍♂️","👰‍♂️","🫃","👨‍🍼","🦸‍♂️","🦹‍♂️","🧙‍♂️","🧚‍♂️","🧛‍♂️","🧝‍♂️","🧞‍♂️","🧟‍♂️","💆‍♂️","💇‍♂️","🚶‍♂️","🧍‍♂️","🧎‍♂️","👨‍🦯","👨‍🦼","👨‍🦽","🏃‍♂️","🕺","🧖‍♂️","🧗‍♂️","🏌️‍♂️","🏄‍♂️","🚣‍♂️","🏊‍♂️","⛹️‍♂️","🏋️‍♂️","🚴‍♂️","🚵‍♂️","🤸‍♂️","🤽‍♂️","🤾‍♂️","🤹‍♂️","🧘‍♂️","👫","👩‍❤️‍💋‍👨","👨‍❤️‍💋‍👨","👩‍❤️‍👨","👨‍❤️‍👨","👨‍👩‍👦","👨‍👩‍👧","👨‍👩‍👧‍👦","👨‍👩‍👦‍👦","👨‍👩‍👧‍👧","👨‍👨‍👦","👨‍👨‍👧","👨‍👨‍👧‍👦","👨‍👨‍👦‍👦","👨‍👨‍👧‍👧","👨‍👦","👨‍👦‍👦","👨‍👧","👨‍👧‍👦","👨‍👧‍👧","👞","🇮🇲"],beard:["🧔","🧔‍♂️","🧔‍♀️"],woman:["🧔‍♀️","👩","👩‍🦰","👩‍🦱","👩‍🦳","👩‍🦲","👱‍♀️","👵","🙍‍♀️","🙎‍♀️","🙅‍♀️","🙆‍♀️","💁‍♀️","🙋‍♀️","🧏‍♀️","🙇‍♀️","🤦‍♀️","🤷‍♀️","👩‍⚕️","👩‍🎓","👩‍🏫","👩‍⚖️","👩‍🌾","👩‍🍳","👩‍🔧","👩‍🏭","👩‍💼","👩‍🔬","👩‍💻","👩‍🎤","👩‍🎨","👩‍✈️","👩‍🚀","👩‍🚒","👮‍♀️","🕵️‍♀️","💂‍♀️","👷‍♀️","👳‍♀️","🧕","🤵‍♀️","👰‍♀️","🤰","👩‍🍼","🦸‍♀️","🦹‍♀️","🧙‍♀️","🧚‍♀️","🧛‍♀️","🧝‍♀️","🧞‍♀️","🧟‍♀️","💆‍♀️","💇‍♀️","🚶‍♀️","🧍‍♀️","🧎‍♀️","👩‍🦯","👩‍🦼","👩‍🦽","🏃‍♀️","💃","🧖‍♀️","🧗‍♀️","🏌️‍♀️","🏄‍♀️","🚣‍♀️","🏊‍♀️","⛹️‍♀️","🏋️‍♀️","🚴‍♀️","🚵‍♀️","🤸‍♀️","🤽‍♀️","🤾‍♀️","🤹‍♀️","🧘‍♀️","👫","👩‍❤️‍💋‍👨","👩‍❤️‍💋‍👩","👩‍❤️‍👨","👩‍❤️‍👩","👨‍👩‍👦","👨‍👩‍👧","👨‍👩‍👧‍👦","👨‍👩‍👦‍👦","👨‍👩‍👧‍👧","👩‍👩‍👦","👩‍👩‍👧","👩‍👩‍👧‍👦","👩‍👩‍👦‍👦","👩‍👩‍👧‍👧","👩‍👦","👩‍👦‍👦","👩‍👧","👩‍👧‍👦","👩‍👧‍👧","👚","👡","👢","👒"],curly:["👨‍🦱","👩‍🦱","🧑‍🦱","➰","➿"],bald:["👨‍🦲","👩‍🦲","🧑‍🦲"],older:["🧓"],old:["👴","👵","🗝️"],tip:["💁","💁‍♂️","💁‍♀️"],deaf:["🧏","🧏‍♂️","🧏‍♀️"],bow:["🙇","🙇‍♂️","🙇‍♀️","🏹"],facepalm:["🤦","🤦‍♂️","🤦‍♀️"],shrug:["🤷","🤷‍♂️","🤷‍♀️"],health:["🧑‍⚕️","👨‍⚕️","👩‍⚕️"],worker:["🧑‍⚕️","👨‍⚕️","👩‍⚕️","🧑‍🏭","👨‍🏭","👩‍🏭","🧑‍💼","👨‍💼","👩‍💼","👷","👷‍♂️","👷‍♀️","⛑️"],student:["🧑‍🎓","👨‍🎓","👩‍🎓"],teacher:["🧑‍🏫","👨‍🏫","👩‍🏫"],judge:["🧑‍⚖️","👨‍⚖️","👩‍⚖️"],farmer:["🧑‍🌾","👨‍🌾","👩‍🌾"],cook:["🧑‍🍳","👨‍🍳","👩‍🍳","🍳","🇨🇰"],mechanic:["🧑‍🔧","👨‍🔧","👩‍🔧"],factory:["🧑‍🏭","👨‍🏭","👩‍🏭","🏭"],office:["🧑‍💼","👨‍💼","👩‍💼","🏢","🏣","🏤"],scientist:["🧑‍🔬","👨‍🔬","👩‍🔬"],technologist:["🧑‍💻","👨‍💻","👩‍💻"],singer:["🧑‍🎤","👨‍🎤","👩‍🎤"],artist:["🧑‍🎨","👨‍🎨","👩‍🎨","🎨"],pilot:["🧑‍✈️","👨‍✈️","👩‍✈️"],astronaut:["🧑‍🚀","👨‍🚀","👩‍🚀"],firefighter:["🧑‍🚒","👨‍🚒","👩‍🚒"],police:["👮","👮‍♂️","👮‍♀️","🚓","🚔","🚨"],officer:["👮","👮‍♂️","👮‍♀️"],detective:["🕵️","🕵️‍♂️","🕵️‍♀️"],guard:["💂","💂‍♂️","💂‍♀️"],ninja:["🥷"],construction:["👷","👷‍♂️","👷‍♀️","🏗️","🚧"],crown:["🫅","👑"],prince:["🤴"],princess:["👸"],wear:["👳","👳‍♂️","👳‍♀️"],turban:["👳","👳‍♂️","👳‍♀️"],skullcap:["👲"],headscarf:["🧕"],tuxedo:["🤵","🤵‍♂️","🤵‍♀️"],veil:["👰","👰‍♂️","👰‍♀️"],pregnant:["🤰","🫃","🫄"],breast:["🤱"],feed:["🤱","👩‍🍼","👨‍🍼","🧑‍🍼"],angel:["👼"],santa:["🎅"],claus:["🎅","🤶","🧑‍🎄"],mrs:["🤶"],mx:["🧑‍🎄"],superhero:["🦸","🦸‍♂️","🦸‍♀️"],supervillain:["🦹","🦹‍♂️","🦹‍♀️"],mage:["🧙","🧙‍♂️","🧙‍♀️"],fairy:["🧚","🧚‍♂️","🧚‍♀️"],vampire:["🧛","🧛‍♂️","🧛‍♀️"],merperson:["🧜"],merman:["🧜‍♂️"],mermaid:["🧜‍♀️"],elf:["🧝","🧝‍♂️","🧝‍♀️"],genie:["🧞","🧞‍♂️","🧞‍♀️"],zombie:["🧟","🧟‍♂️","🧟‍♀️"],troll:["🧌"],gett:["💆","💆‍♂️","💆‍♀️","💇","💇‍♂️","💇‍♀️"],massage:["💆","💆‍♂️","💆‍♀️"],haircut:["💇","💇‍♂️","💇‍♀️"],walk:["🚶","🚶‍♂️","🚶‍♀️"],stand:["🧍","🧍‍♂️","🧍‍♀️"],kneel:["🧎","🧎‍♂️","🧎‍♀️"],cane:["🧑‍🦯","👨‍🦯","👩‍🦯","🦯"],motorized:["🧑‍🦼","👨‍🦼","👩‍🦼","🦼"],wheelchair:["🧑‍🦼","👨‍🦼","👩‍🦼","🧑‍🦽","👨‍🦽","👩‍🦽","🦽","🦼","♿"],manual:["🧑‍🦽","👨‍🦽","👩‍🦽","🦽"],run:["🏃","🏃‍♂️","🏃‍♀️","🎽","👟"],dance:["💃","🕺"],suit:["🕴️","♠️","♥️","♦️","♣️"],levitate:["🕴️"],people:["👯","🤼","🧑‍🤝‍🧑","🫂"],bunny:["👯","👯‍♂️","👯‍♀️"],ears:["👯","👯‍♂️","👯‍♀️"],men:["👯‍♂️","🤼‍♂️","👬","🚹"],women:["👯‍♀️","🤼‍♀️","👭","🚺"],steamy:["🧖","🧖‍♂️","🧖‍♀️"],room:["🧖","🧖‍♂️","🧖‍♀️","🚹","🚺"],climb:["🧗","🧗‍♂️","🧗‍♀️"],fence:["🤺"],horse:["🏇","🐴","🐎","🎠"],race:["🏇","🏎️"],skier:["⛷️"],snowboarder:["🏂"],golf:["🏌️","🏌️‍♂️","🏌️‍♀️"],surf:["🏄","🏄‍♂️","🏄‍♀️"],row:["🚣","🚣‍♂️","🚣‍♀️"],boat:["🚣","🚣‍♂️","🚣‍♀️","🛥️"],swim:["🏊","🏊‍♂️","🏊‍♀️"],bounce:["⛹️","⛹️‍♂️","⛹️‍♀️"],ball:["⛹️","⛹️‍♂️","⛹️‍♀️","🍙","🎊","⚽","🎱","🔮","🪩"],lift:["🏋️","🏋️‍♂️","🏋️‍♀️"],weights:["🏋️","🏋️‍♂️","🏋️‍♀️"],bike:["🚴","🚴‍♂️","🚴‍♀️","🚵","🚵‍♂️","🚵‍♀️"],mountain:["🚵","🚵‍♂️","🚵‍♀️","🏔️","⛰️","🚞","🚠"],cartwheel:["🤸","🤸‍♂️","🤸‍♀️"],wrestle:["🤼","🤼‍♂️","🤼‍♀️"],play:["🤽","🤽‍♂️","🤽‍♀️","🤾","🤾‍♂️","🤾‍♀️","🎴","▶️","⏯️"],water:["🤽","🤽‍♂️","🤽‍♀️","🐃","🌊","🔫","🚰","🚾","🚱"],polo:["🤽","🤽‍♂️","🤽‍♀️"],handball:["🤾","🤾‍♂️","🤾‍♀️"],juggle:["🤹","🤹‍♂️","🤹‍♀️"],lotus:["🧘","🧘‍♂️","🧘‍♀️","🪷"],position:["🧘","🧘‍♂️","🧘‍♀️"],take:["🛀"],bath:["🛀"],bed:["🛌","🛏️"],couple:["💑","👩‍❤️‍👨","👨‍❤️‍👨","👩‍❤️‍👩"],family:["👪","👨‍👩‍👦","👨‍👩‍👧","👨‍👩‍👧‍👦","👨‍👩‍👦‍👦","👨‍👩‍👧‍👧","👨‍👨‍👦","👨‍👨‍👧","👨‍👨‍👧‍👦","👨‍👨‍👦‍👦","👨‍👨‍👧‍👧","👩‍👩‍👦","👩‍👩‍👧","👩‍👩‍👧‍👦","👩‍👩‍👦‍👦","👩‍👩‍👧‍👧","👨‍👦","👨‍👦‍👦","👨‍👧","👨‍👧‍👦","👨‍👧‍👧","👩‍👦","👩‍👦‍👦","👩‍👧","👩‍👧‍👦","👩‍👧‍👧"],bust:["👤"],silhouette:["👤","👥"],busts:["👥"],hug:["🫂"],footprints:["👣"],gorilla:["🦍"],orangutan:["🦧"],dog:["🐶","🐕","🦮","🐕‍🦺","🌭"],guide:["🦮"],service:["🐕‍🦺","🈂️"],poodle:["🐩"],wolf:["🐺"],fox:["🦊"],raccoon:["🦝"],lion:["🦁"],tiger:["🐯","🐅"],leopard:["🐆"],moose:["🫎"],donkey:["🫏"],unicorn:["🦄"],zebra:["🦓"],deer:["🦌"],bison:["🦬"],cow:["🐮","🐄"],ox:["🐂"],buffalo:["🐃"],pig:["🐷","🐖","🐽"],boar:["🐗"],ram:["🐏"],ewe:["🐑"],goat:["🐐"],camel:["🐪","🐫"],hump:["🐫"],llama:["🦙"],giraffe:["🦒"],elephant:["🐘"],mammoth:["🦣"],rhinoceros:["🦏"],hippopotamus:["🦛"],mouse:["🐭","🐁","🖱️","🪤"],rat:["🐀"],hamster:["🐹"],rabbit:["🐰","🐇"],chipmunk:["🐿️"],beaver:["🦫"],hedgehog:["🦔"],bat:["🦇"],bear:["🐻","🐻‍❄️","🧸"],polar:["🐻‍❄️"],koala:["🐨"],panda:["🐼"],sloth:["🦥"],otter:["🦦"],skunk:["🦨"],kangaroo:["🦘"],badger:["🦡"],paw:["🐾"],prints:["🐾"],turkey:["🦃","🇹🇷"],chicken:["🐔"],rooster:["🐓"],hatch:["🐣"],chick:["🐣","🐤","🐥"],front:["🐥"],bird:["🐦","🐦‍⬛"],penguin:["🐧"],dove:["🕊️"],eagle:["🦅"],duck:["🦆"],swan:["🦢"],owl:["🦉"],dodo:["🦤"],feather:["🪶"],flamingo:["🦩"],peacock:["🦚"],parrot:["🦜"],wing:["🪽"],goose:["🪿"],frog:["🐸"],crocodile:["🐊"],turtle:["🐢"],lizard:["🦎"],snake:["🐍"],dragon:["🐲","🐉","🀄"],sauropod:["🦕"],rex:["🦖"],spout:["🐳"],whale:["🐳","🐋"],dolphin:["🐬"],seal:["🦭"],fish:["🐟","🐠","🍥","🎣"],tropical:["🐠","🍹"],blowfish:["🐡"],shark:["🦈"],octopus:["🐙"],shell:["🐚"],coral:["🪸"],jellyfish:["🪼"],snail:["🐌"],butterfly:["🦋"],bug:["🐛"],ant:["🐜"],honeybee:["🐝"],beetle:["🪲","🐞"],lady:["🐞"],cricket:["🦗","🏏"],cockroach:["🪳"],spider:["🕷️","🕸️"],web:["🕸️"],scorpion:["🦂"],mosquito:["🦟"],fly:["🪰","🛸","🥏"],worm:["🪱"],microbe:["🦠"],bouquet:["💐"],cherry:["🌸"],blossom:["🌸","🌼"],flower:["💮","🥀","🎴"],rosette:["🏵️"],rose:["🌹"],wilted:["🥀"],hibiscus:["🌺"],sunflower:["🌻"],tulip:["🌷"],hyacinth:["🪻"],seedle:["🌱"],potted:["🪴"],plant:["🪴"],evergreen:["🌲"],tree:["🌲","🌳","🌴","🎄","🎋"],deciduous:["🌳"],cactus:["🌵"],sheaf:["🌾"],rice:["🌾","🍘","🍙","🍚","🍛"],herb:["🌿"],shamrock:["☘️"],four:["🍀","🕓","🕟"],leaf:["🍀","🍁","🍂","🍃"],clover:["🍀"],maple:["🍁"],fallen:["🍂"],flutter:["🍃"],wind:["🍃","🌬️","🎐"],empty:["🪹"],nest:["🪹","🪺","🪆"],eggs:["🪺"],mushroom:["🍄"],grapes:["🍇"],melon:["🍈"],watermelon:["🍉"],tangerine:["🍊"],lemon:["🍋"],banana:["🍌"],pineapple:["🍍"],mango:["🥭"],apple:["🍎","🍏"],pear:["🍐"],peach:["🍑"],cherries:["🍒"],strawberry:["🍓"],blueberries:["🫐"],kiwi:["🥝"],fruit:["🥝"],tomato:["🍅"],olive:["🫒"],coconut:["🥥"],avocado:["🥑"],eggplant:["🍆"],potato:["🥔","🍠"],carrot:["🥕"],corn:["🌽"],pepper:["🌶️","🫑"],bell:["🫑","🛎️","🔔","🔕"],cucumber:["🥒"],leafy:["🥬"],broccoli:["🥦"],garlic:["🧄"],onion:["🧅"],peanuts:["🥜"],beans:["🫘"],chestnut:["🌰"],ginger:["🫚"],root:["🫚"],pea:["🫛"],pod:["🫛"],bread:["🍞","🥖"],croissant:["🥐"],baguette:["🥖"],flatbread:["🫓","🥙"],pretzel:["🥨"],bagel:["🥯"],pancakes:["🥞"],waffle:["🧇"],cheese:["🧀"],wedge:["🧀"],meat:["🍖","🥩"],poultry:["🍗"],cut:["🥩"],bacon:["🥓"],hamburger:["🍔"],french:["🍟","🇬🇫","🇵🇫","🇹🇫"],fries:["🍟"],pizza:["🍕"],sandwich:["🥪","🇬🇸"],taco:["🌮"],burrito:["🌯"],tamale:["🫔"],stuffed:["🥙"],falafel:["🧆"],egg:["🥚"],shallow:["🥘"],pan:["🥘"],pot:["🍲","🍯"],fondue:["🫕"],bowl:["🥣","🍜","🎳"],spoon:["🥣","🥄"],salad:["🥗"],popcorn:["🍿"],butter:["🧈"],salt:["🧂"],canned:["🥫"],bento:["🍱"],box:["🍱","🥡","🧃","🥊","🗳️","🗃️","☑️"],cracker:["🍘"],cooked:["🍚"],curry:["🍛"],spaghetti:["🍝"],roasted:["🍠"],sweet:["🍠"],oden:["🍢"],sushi:["🍣"],fried:["🍤"],shrimp:["🍤","🦐"],cake:["🍥","🥮","🎂"],swirl:["🍥"],moon:["🥮","🌑","🌒","🌓","🌔","🌕","🌖","🌗","🌘","🌙","🌚","🌛","🌜","🌝","🎑"],dango:["🍡"],dumple:["🥟"],fortune:["🥠"],cookie:["🥠","🍪"],takeout:["🥡"],crab:["🦀"],lobster:["🦞"],squid:["🦑"],oyster:["🦪"],soft:["🍦"],ice:["🍦","🍧","🍨","🧊","🏒","⛸️"],cream:["🍦","🍨"],shaved:["🍧"],doughnut:["🍩"],birthday:["🎂"],shortcake:["🍰"],cupcake:["🧁"],pie:["🥧"],chocolate:["🍫"],bar:["🍫","📊"],candy:["🍬"],lollipop:["🍭"],custard:["🍮"],honey:["🍯"],bottle:["🍼","🍾","🧴"],glass:["🥛","🍷","🍸","🥃","🔍","🔎"],milk:["🥛"],beverage:["☕","🧃"],teapot:["🫖"],teacup:["🍵"],handle:["🍵"],sake:["🍶"],pop:["🍾"],cork:["🍾"],wine:["🍷"],cocktail:["🍸"],drink:["🍹"],beer:["🍺","🍻"],mug:["🍺"],clink:["🍻","🥂"],mugs:["🍻"],glasses:["🥂","👓"],tumbler:["🥃"],pour:["🫗"],liquid:["🫗"],cup:["🥤"],straw:["🥤"],tea:["🧋"],mate:["🧉"],chopsticks:["🥢"],fork:["🍽️","🍴"],knife:["🍽️","🍴","🔪"],plate:["🍽️"],kitchen:["🔪"],jar:["🫙"],amphora:["🏺"],globe:["🌍","🌎","🌏","🌐"],show:["🌍","🌎","🌏"],europe:["🌍"],africa:["🌍","🇿🇦"],americas:["🌎"],asia:["🌏"],australia:["🌏","🇦🇺"],meridians:["🌐"],world:["🗺️"],map:["🗺️","🗾"],japan:["🗾","🇯🇵"],compass:["🧭"],snow:["🏔️","🌨️","⛄"],capped:["🏔️"],volcano:["🌋"],mount:["🗻"],fuji:["🗻"],camp:["🏕️"],beach:["🏖️"],umbrella:["🏖️","🌂","☂️","☔","⛱️"],desert:["🏜️","🏝️"],island:["🏝️","🇦🇨","🇧🇻","🇨🇵","🇨🇽","🇳🇫"],national:["🏞️"],park:["🏞️"],stadium:["🏟️"],classical:["🏛️"],build:["🏛️","🏗️","🏢"],brick:["🧱"],rock:["🪨"],wood:["🪵"],hut:["🛖"],houses:["🏘️"],derelict:["🏚️"],house:["🏚️","🏠","🏡"],garden:["🏡"],japanese:["🏣","🏯","🎎","🔰","🈁","🈂️","🈷️","🈶","🈯","🉐","🈹","🈚","🈲","🉑","🈸","🈴","🈳","㊗️","㊙️","🈺","🈵"],post:["🏣","🏤"],hospital:["🏥"],bank:["🏦"],hotel:["🏨","🏩"],convenience:["🏪"],store:["🏪","🏬"],school:["🏫"],department:["🏬"],castle:["🏯","🏰"],wed:["💒"],tokyo:["🗼"],tower:["🗼"],statue:["🗽"],liberty:["🗽"],church:["⛪"],mosque:["🕌"],hindu:["🛕"],temple:["🛕"],synagogue:["🕍"],shinto:["⛩️"],shrine:["⛩️"],kaaba:["🕋"],fountain:["⛲","🖋️"],tent:["⛺","🎪"],foggy:["🌁"],night:["🌃","🌉"],stars:["🌃"],cityscape:["🏙️","🌆"],sunrise:["🌄","🌅"],mountains:["🌄"],dusk:["🌆"],sunset:["🌇"],bridge:["🌉"],springs:["♨️"],carousel:["🎠"],playground:["🛝"],slide:["🛝"],ferris:["🎡"],wheel:["🎡","🛞","☸️"],roller:["🎢","🛼"],coaster:["🎢"],barber:["💈"],pole:["💈","🎣"],circus:["🎪"],locomotive:["🚂"],railway:["🚃","🚞","🛤️","🚟"],car:["🚃","🚋","🚓","🚔","🏎️","🚨"],high:["🚄","⚡","👠","🔊"],speed:["🚄"],train:["🚄","🚅","🚆"],bullet:["🚅"],metro:["🚇"],rail:["🚈"],station:["🚉"],tram:["🚊","🚋"],monorail:["🚝"],bus:["🚌","🚍","🚏"],trolleybus:["🚎"],minibus:["🚐"],ambulance:["🚑"],engine:["🚒"],taxi:["🚕","🚖"],automobile:["🚗","🚘"],sport:["🚙"],utility:["🚙"],vehicle:["🚙"],pickup:["🛻"],truck:["🛻","🚚"],delivery:["🚚"],articulated:["🚛"],lorry:["🚛"],tractor:["🚜"],motorcycle:["🏍️"],motor:["🛵","🛥️"],scooter:["🛵","🛴"],auto:["🛺"],rickshaw:["🛺"],bicycle:["🚲"],kick:["🛴"],skateboard:["🛹"],skate:["🛼","⛸️"],stop:["🚏","🛑","⏹️"],motorway:["🛣️"],track:["🛤️","⏭️","⏮️"],oil:["🛢️"],drum:["🛢️","🥁","🪘"],fuel:["⛽"],pump:["⛽"],horizontal:["🚥"],traffic:["🚥","🚦"],vertical:["🚦","🔃"],anchor:["⚓"],ring:["🛟","💍"],buoy:["🛟"],sailboat:["⛵"],canoe:["🛶"],speedboat:["🚤"],passenger:["🛳️"],ship:["🛳️","🚢"],ferry:["⛴️"],airplane:["✈️","🛩️","🛫","🛬"],small:["🛩️","🌤️","◾","◽","▪️","▫️","🔸","🔹"],departure:["🛫"],arrival:["🛬"],parachute:["🪂"],seat:["💺"],helicopter:["🚁"],suspension:["🚟"],cableway:["🚠"],aerial:["🚡"],tramway:["🚡"],satellite:["🛰️","📡"],rocket:["🚀"],saucer:["🛸"],bellhop:["🛎️"],luggage:["🧳","🛅"],hourglass:["⌛","⏳"],done:["⌛","⏳"],not:["⏳","🈶"],watch:["⌚"],alarm:["⏰"],clock:["⏰","⏲️","🕰️","🕛","🕐","🕑","🕒","🕓","🕔","🕕","🕖","🕗","🕘","🕙","🕚"],stopwatch:["⏱️"],timer:["⏲️"],mantelpiece:["🕰️"],twelve:["🕛","🕧"],thirty:["🕧","🕜","🕝","🕞","🕟","🕠","🕡","🕢","🕣","🕤","🕥","🕦"],one:["🕐","🕜","🩱","🔞"],three:["🕒","🕞"],five:["🕔","🕠"],six:["🕕","🕡","🔯"],seven:["🕖","🕢"],eight:["🕗","🕣","✳️","✴️"],nine:["🕘","🕤"],ten:["🕙","🕥"],eleven:["🕚","🕦"],new:["🌑","🌚","🆕","🇳🇨","🇳🇿","🇵🇬"],wax:["🌒","🌔"],crescent:["🌒","🌘","🌙","☪️"],first:["🌓","🌛"],quarter:["🌓","🌗","🌛","🌜"],gibbous:["🌔","🌖"],full:["🌕","🌝"],wane:["🌖","🌘"],last:["🌗","🌜","⏮️"],sun:["☀️","🌞","⛅","🌤️","🌥️","🌦️"],ringed:["🪐"],planet:["🪐"],glow:["🌟"],shoot:["🌠"],milky:["🌌"],way:["🌌"],cloud:["☁️","⛅","⛈️","🌤️","🌥️","🌦️","🌧️","🌨️","🌩️"],behind:["⛅","🌤️","🌥️","🌦️"],lightning:["⛈️","🌩️"],rain:["⛈️","🌦️","🌧️","☔"],large:["🌥️","⬛","⬜","🔶","🔷"],tornado:["🌪️"],fog:["🌫️"],cyclone:["🌀"],rainbow:["🌈","🏳️‍🌈"],drops:["☔"],ground:["⛱️"],voltage:["⚡"],snowflake:["❄️"],snowman:["☃️","⛄"],comet:["☄️"],droplet:["💧"],jack:["🎃"],lantern:["🎃","🏮"],christmas:["🎄","🇨🇽"],fireworks:["🎆"],sparkler:["🎇"],firecracker:["🧨"],sparkles:["✨"],popper:["🎉"],confetti:["🎊"],tanabata:["🎋"],pine:["🎍"],dolls:["🎎","🪆"],carp:["🎏"],streamer:["🎏"],chime:["🎐"],view:["🎑"],ceremony:["🎑"],envelope:["🧧","✉️","📨","📩"],wrapped:["🎁"],gift:["🎁"],reminder:["🎗️"],admission:["🎟️"],tickets:["🎟️"],ticket:["🎫"],military:["🎖️","🪖"],medal:["🎖️","🏅","🥇","🥈","🥉"],trophy:["🏆"],sports:["🏅"],"1st":["🥇"],place:["🥇","🥈","🥉","🛐"],"2nd":["🥈"],"3rd":["🥉"],soccer:["⚽"],baseball:["⚾"],softball:["🥎"],basketball:["🏀"],volleyball:["🏐"],american:["🏈","🇦🇸"],football:["🏈","🏉"],rugby:["🏉"],tennis:["🎾"],disc:["🥏"],game:["🏏","🎮","🎲"],field:["🏑"],hockey:["🏑","🏒"],lacrosse:["🥍"],ping:["🏓"],pong:["🏓"],badminton:["🏸"],glove:["🥊"],martial:["🥋"],arts:["🥋","🎭"],uniform:["🥋"],goal:["🥅"],net:["🥅"],flag:["⛳","📫","📪","📬","📭","🏁","🚩","🏴","🏳️","🏳️‍🌈","🏳️‍⚧️","🏴‍☠️","🇦🇨","🇦🇩","🇦🇪","🇦🇫","🇦🇬","🇦🇮","🇦🇱","🇦🇲","🇦🇴","🇦🇶","🇦🇷","🇦🇸","🇦🇹","🇦🇺","🇦🇼","🇦🇽","🇦🇿","🇧🇦","🇧🇧","🇧🇩","🇧🇪","🇧🇫","🇧🇬","🇧🇭","🇧🇮","🇧🇯","🇧🇱","🇧🇲","🇧🇳","🇧🇴","🇧🇶","🇧🇷","🇧🇸","🇧🇹","🇧🇻","🇧🇼","🇧🇾","🇧🇿","🇨🇦","🇨🇨","🇨🇩","🇨🇫","🇨🇬","🇨🇭","🇨🇮","🇨🇰","🇨🇱","🇨🇲","🇨🇳","🇨🇴","🇨🇵","🇨🇷","🇨🇺","🇨🇻","🇨🇼","🇨🇽","🇨🇾","🇨🇿","🇩🇪","🇩🇬","🇩🇯","🇩🇰","🇩🇲","🇩🇴","🇩🇿","🇪🇦","🇪🇨","🇪🇪","🇪🇬","🇪🇭","🇪🇷","🇪🇸","🇪🇹","🇪🇺","🇫🇮","🇫🇯","🇫🇰","🇫🇲","🇫🇴","🇫🇷","🇬🇦","🇬🇧","🇬🇩","🇬🇪","🇬🇫","🇬🇬","🇬🇭","🇬🇮","🇬🇱","🇬🇲","🇬🇳","🇬🇵","🇬🇶","🇬🇷","🇬🇸","🇬🇹","🇬🇺","🇬🇼","🇬🇾","🇭🇰","🇭🇲","🇭🇳","🇭🇷","🇭🇹","🇭🇺","🇮🇨","🇮🇩","🇮🇪","🇮🇱","🇮🇲","🇮🇳","🇮🇴","🇮🇶","🇮🇷","🇮🇸","🇮🇹","🇯🇪","🇯🇲","🇯🇴","🇯🇵","🇰🇪","🇰🇬","🇰🇭","🇰🇮","🇰🇲","🇰🇳","🇰🇵","🇰🇷","🇰🇼","🇰🇾","🇰🇿","🇱🇦","🇱🇧","🇱🇨","🇱🇮","🇱🇰","🇱🇷","🇱🇸","🇱🇹","🇱🇺","🇱🇻","🇱🇾","🇲🇦","🇲🇨","🇲🇩","🇲🇪","🇲🇫","🇲🇬","🇲🇭","🇲🇰","🇲🇱","🇲🇲","🇲🇳","🇲🇴","🇲🇵","🇲🇶","🇲🇷","🇲🇸","🇲🇹","🇲🇺","🇲🇻","🇲🇼","🇲🇽","🇲🇾","🇲🇿","🇳🇦","🇳🇨","🇳🇪","🇳🇫","🇳🇬","🇳🇮","🇳🇱","🇳🇴","🇳🇵","🇳🇷","🇳🇺","🇳🇿","🇴🇲","🇵🇦","🇵🇪","🇵🇫","🇵🇬","🇵🇭","🇵🇰","🇵🇱","🇵🇲","🇵🇳","🇵🇷","🇵🇸","🇵🇹","🇵🇼","🇵🇾","🇶🇦","🇷🇪","🇷🇴","🇷🇸","🇷🇺","🇷🇼","🇸🇦","🇸🇧","🇸🇨","🇸🇩","🇸🇪","🇸🇬","🇸🇭","🇸🇮","🇸🇯","🇸🇰","🇸🇱","🇸🇲","🇸🇳","🇸🇴","🇸🇷","🇸🇸","🇸🇹","🇸🇻","🇸🇽","🇸🇾","🇸🇿","🇹🇦","🇹🇨","🇹🇩","🇹🇫","🇹🇬","🇹🇭","🇹🇯","🇹🇰","🇹🇱","🇹🇲","🇹🇳","🇹🇴","🇹🇷","🇹🇹","🇹🇻","🇹🇼","🇹🇿","🇺🇦","🇺🇬","🇺🇲","🇺🇳","🇺🇸","🇺🇾","🇺🇿","🇻🇦","🇻🇨","🇻🇪","🇻🇬","🇻🇮","🇻🇳","🇻🇺","🇼🇫","🇼🇸","🇽🇰","🇾🇪","🇾🇹","🇿🇦","🇿🇲","🇿🇼","🏴󠁧󠁢󠁥󠁮󠁧󠁿","🏴󠁧󠁢󠁳󠁣󠁴󠁿","🏴󠁧󠁢󠁷󠁬󠁳󠁿"],dive:["🤿"],shirt:["🎽","👕"],skis:["🎿"],sled:["🛷"],curl:["🥌","📃"],stone:["🥌","💎"],bullseye:["🎯"],yo:["🪀"],kite:["🪁"],pistol:["🔫"],pool:["🎱"],crystal:["🔮"],magic:["🪄"],wand:["🪄"],video:["🎮","📹"],joystick:["🕹️"],slot:["🎰"],machine:["🎰","📠"],die:["🎲"],puzzle:["🧩"],piece:["🧩","🩱"],teddy:["🧸"],pinata:["🪅"],mirror:["🪩","🪞"],spade:["♠️"],diamond:["♦️","🔶","🔷","🔸","🔹","💠"],club:["♣️"],chess:["♟️"],pawn:["♟️"],joker:["🃏"],mahjong:["🀄"],cards:["🎴"],perform:["🎭"],framed:["🖼️"],picture:["🖼️"],palette:["🎨"],thread:["🧵"],sew:["🪡"],needle:["🪡"],yarn:["🧶"],knot:["🪢"],goggles:["🥽"],lab:["🥼"],coat:["🥼","🧥"],safety:["🦺","🧷"],vest:["🦺"],necktie:["👔"],jeans:["👖"],scarf:["🧣"],gloves:["🧤"],socks:["🧦"],dress:["👗"],kimono:["👘"],sari:["🥻"],swimsuit:["🩱"],briefs:["🩲"],shorts:["🩳"],bikini:["👙"],clothes:["👚"],fold:["🪭"],fan:["🪭"],purse:["👛"],handbag:["👜"],clutch:["👝"],bag:["👝","💰"],shop:["🛍️","🛒"],bags:["🛍️"],backpack:["🎒"],thong:["🩴"],sandal:["🩴","👡"],shoe:["👞","👟","🥿","👠"],hike:["🥾"],boot:["🥾","👢"],flat:["🥿"],heeled:["👠"],ballet:["🩰"],shoes:["🩰"],pick:["🪮","⛏️","⚒️"],top:["🎩","🔝"],graduation:["🎓"],cap:["🎓","🧢"],billed:["🧢"],helmet:["🪖","⛑️"],rescue:["⛑️"],prayer:["📿"],beads:["📿"],lipstick:["💄"],gem:["💎"],muted:["🔇"],speaker:["🔇","🔈","🔉","🔊"],low:["🔈","🪫"],volume:["🔈","🔉","🔊"],medium:["🔉","◼️","◻️","◾","◽"],loudspeaker:["📢"],megaphone:["📣"],postal:["📯"],horn:["📯"],slash:["🔕"],musical:["🎼","🎵","🎶","🎹"],score:["🎼"],note:["🎵"],notes:["🎶"],studio:["🎙️"],microphone:["🎙️","🎤"],level:["🎚️"],slider:["🎚️"],control:["🎛️","🛂"],knobs:["🎛️"],headphone:["🎧"],radio:["📻","🔘"],saxophone:["🎷"],accordion:["🪗"],guitar:["🎸"],keyboard:["🎹","⌨️"],trumpet:["🎺"],violin:["🎻"],banjo:["🪕"],long:["🪘"],maracas:["🪇"],flute:["🪈"],mobile:["📱","📲","📵","📴"],phone:["📱","📲","📴"],telephone:["☎️","📞"],receiver:["📞"],pager:["📟"],fax:["📠"],battery:["🔋","🪫"],electric:["🔌"],plug:["🔌"],laptop:["💻"],desktop:["🖥️"],computer:["🖥️","🖱️","💽"],printer:["🖨️"],trackball:["🖲️"],disk:["💽","💾","💿"],floppy:["💾"],optical:["💿"],dvd:["📀"],abacus:["🧮"],movie:["🎥"],camera:["🎥","📷","📸","📹"],film:["🎞️","📽️"],frames:["🎞️"],projector:["📽️"],clapper:["🎬"],board:["🎬"],television:["📺"],flash:["📸"],videocassette:["📼"],magnify:["🔍","🔎"],tilted:["🔍","🔎"],candle:["🕯️"],bulb:["💡"],flashlight:["🔦"],paper:["🏮","🧻"],diya:["🪔"],lamp:["🪔","🛋️"],notebook:["📔","📓"],decorative:["📔"],cover:["📔"],book:["📕","📖","📗","📘","📙"],books:["📚"],ledger:["📒"],page:["📃","📄"],scroll:["📜"],newspaper:["📰","🗞️"],rolled:["🗞️"],bookmark:["📑","🔖"],tabs:["📑"],label:["🏷️"],coin:["🪙"],yen:["💴","💹"],banknote:["💴","💵","💶","💷"],dollar:["💵","💲"],euro:["💶"],pound:["💷"],wings:["💸"],credit:["💳"],card:["💳","🗂️","📇","🗃️","🪪"],receipt:["🧾"],chart:["💹","📈","📉","📊"],increase:["💹","📈"],mail:["📧"],income:["📨"],outbox:["📤"],tray:["📤","📥"],inbox:["📥"],package:["📦"],mailbox:["📫","📪","📬","📭"],lowered:["📪","📭"],postbox:["📮"],ballot:["🗳️"],pencil:["✏️"],nib:["✒️"],pen:["🖋️","🖊️","🔏"],paintbrush:["🖌️"],crayon:["🖍️"],memo:["📝"],briefcase:["💼"],file:["📁","📂","🗃️","🗄️"],folder:["📁","📂"],dividers:["🗂️"],calendar:["📅","📆","🗓️"],off:["📆","📴"],notepad:["🗒️"],decrease:["📉"],clipboard:["📋"],pushpin:["📌","📍"],round:["📍"],paperclip:["📎"],linked:["🖇️"],paperclips:["🖇️"],straight:["📏"],ruler:["📏","📐"],triangular:["📐","🚩"],scissors:["✂️"],cabinet:["🗄️"],wastebasket:["🗑️"],locked:["🔒","🔏","🔐"],unlocked:["🔓"],key:["🔐","🔑","🗝️"],hammer:["🔨","⚒️","🛠️"],axe:["🪓"],wrench:["🛠️","🔧"],dagger:["🗡️"],swords:["⚔️"],bomb:["💣"],boomerang:["🪃"],shield:["🛡️"],carpentry:["🪚"],saw:["🪚"],screwdriver:["🪛"],nut:["🔩"],bolt:["🔩"],gear:["⚙️"],clamp:["🗜️"],balance:["⚖️"],scale:["⚖️"],link:["🔗"],chains:["⛓️"],hook:["🪝"],toolbox:["🧰"],magnet:["🧲"],ladder:["🪜"],alembic:["⚗️"],test:["🧪"],tube:["🧪"],petri:["🧫"],dish:["🧫"],dna:["🧬"],microscope:["🔬"],telescope:["🔭"],antenna:["📡","📶"],syringe:["💉"],drop:["🩸"],blood:["🩸","🅰️","🆎","🅱️","🅾️"],pill:["💊"],adhesive:["🩹"],crutch:["🩼"],stethoscope:["🩺"],ray:["🩻"],door:["🚪"],elevator:["🛗"],window:["🪟"],couch:["🛋️"],chair:["🪑"],toilet:["🚽"],plunger:["🪠"],shower:["🚿"],bathtub:["🛁"],trap:["🪤"],razor:["🪒"],lotion:["🧴"],pin:["🧷"],broom:["🧹"],basket:["🧺"],bucket:["🪣"],soap:["🧼"],bubbles:["🫧"],toothbrush:["🪥"],sponge:["🧽"],extinguisher:["🧯"],cart:["🛒"],cigarette:["🚬"],coffin:["⚰️"],headstone:["🪦"],funeral:["⚱️"],urn:["⚱️"],nazar:["🧿"],amulet:["🧿"],hamsa:["🪬"],moai:["🗿"],placard:["🪧"],identification:["🪪"],atm:["🏧"],litter:["🚮","🚯"],bin:["🚮"],potable:["🚰","🚱"],restroom:["🚻"],closet:["🚾"],passport:["🛂"],customs:["🛃"],baggage:["🛄"],claim:["🛄"],warn:["⚠️"],children:["🚸"],cross:["🚸","✝️","☦️","❌","❎"],entry:["⛔"],prohibited:["🚫","🈲"],bicycles:["🚳"],smoke:["🚭"],non:["🚱"],pedestrians:["🚷"],phones:["📵"],under:["🔞"],eighteen:["🔞"],radioactive:["☢️"],biohazard:["☣️"],curve:["↩️","↪️","⤴️","⤵️"],clockwise:["🔃"],arrows:["🔃","🔄"],counterclockwise:["🔄"],button:["🔄","🔀","🔁","🔂","▶️","⏩","⏭️","⏯️","◀️","⏪","⏮️","🔼","⏫","🔽","⏬","⏸️","⏹️","⏺️","⏏️","🔅","🔆","✅","❎","🅰️","🆎","🅱️","🆑","🆒","🆓","🆔","🆕","🆖","🅾️","🆗","🅿️","🆘","🆙","🆚","🈁","🈂️","🈷️","🈶","🈯","🉐","🈹","🈚","🈲","🉑","🈸","🈴","🈳","㊗️","㊙️","🈺","🈵","🔘","🔳","🔲"],end:["🔚"],soon:["🔜"],worship:["🛐"],atom:["⚛️"],om:["🕉️"],david:["✡️"],dharma:["☸️"],yin:["☯️"],yang:["☯️"],latin:["✝️","🔠","🔡","🔤"],orthodox:["☦️"],peace:["☮️"],menorah:["🕎"],pointed:["🔯","✴️","🔺","🔻"],khanda:["🪯"],aries:["♈"],taurus:["♉"],gemini:["♊"],cancer:["♋"],leo:["♌"],virgo:["♍"],libra:["♎"],scorpio:["♏"],sagittarius:["♐"],capricorn:["♑"],aquarius:["♒"],pisces:["♓"],ophiuchus:["⛎"],shuffle:["🔀"],tracks:["🔀"],repeat:["🔁","🔂"],single:["🔂"],fast:["⏩","⏪","⏫","⏬"],forward:["⏩"],next:["⏭️"],or:["⏯️"],pause:["⏯️","⏸️"],reverse:["◀️","⏪"],upwards:["🔼"],downwards:["🔽"],record:["⏺️"],eject:["⏏️"],cinema:["🎦"],dim:["🔅"],bright:["🔆"],bars:["📶"],wireless:["🛜"],vibration:["📳"],mode:["📳"],female:["♀️"],male:["♂️"],transgender:["⚧️","🏳️‍⚧️"],multiply:["✖️"],plus:["➕"],minus:["➖"],divide:["➗"],heavy:["🟰","💲"],equals:["🟰"],infinity:["♾️"],double:["‼️","➿"],question:["⁉️","❓","❔"],wavy:["〰️"],currency:["💱"],exchange:["💱"],recycle:["♻️"],fleur:["⚜️"],de:["⚜️"],lis:["⚜️"],trident:["🔱"],emblem:["🔱"],name:["📛"],badge:["📛"],for:["🔰","🈺"],beginner:["🔰"],hollow:["⭕"],circle:["⭕","🔴","🟠","🟡","🟢","🔵","🟣","🟤","⚫","⚪"],check:["✅","☑️","✔️"],loop:["➰","➿"],part:["〽️"],alternation:["〽️"],spoked:["✳️"],asterisk:["✳️"],copyright:["©️"],registered:["®️"],trade:["™️"],keycap:["#️⃣","*️⃣","0️⃣","1️⃣","2️⃣","3️⃣","4️⃣","5️⃣","6️⃣","7️⃣","8️⃣","9️⃣","🔟"],input:["🔠","🔡","🔢","🔣","🔤"],uppercase:["🔠"],lowercase:["🔡"],numbers:["🔢"],letters:["🔤"],type:["🅰️","🆎","🅱️","🅾️"],ab:["🆎"],cl:["🆑"],cool:["🆒"],free:["🆓","🈶","🈚"],information:["ℹ️"],id:["🆔"],circled:["Ⓜ️"],ng:["🆖"],sos:["🆘"],vs:["🆚"],here:["🈁"],charge:["🈂️","🈶","🈚"],monthly:["🈷️"],amount:["🈷️"],reserved:["🈯"],bargain:["🉐"],discount:["🈹"],acceptable:["🉑"],application:["🈸"],pass:["🈴"],grade:["🈴"],vacancy:["🈳","🈵"],congratulations:["㊗️"],secret:["㊙️"],business:["🈺"],square:["🟥","🟧","🟨","🟩","🟦","🟪","🟫","⬛","⬜","◼️","◻️","◾","◽","▪️","▫️","🔳","🔲"],triangle:["🔺","🔻"],dot:["💠"],chequered:["🏁"],flags:["🎌"],pirate:["🏴‍☠️"],ascension:["🇦🇨"],andorra:["🇦🇩"],united:["🇦🇪","🇬🇧","🇺🇳","🇺🇸"],arab:["🇦🇪"],emirates:["🇦🇪"],afghanistan:["🇦🇫"],antigua:["🇦🇬"],barbuda:["🇦🇬"],anguilla:["🇦🇮"],albania:["🇦🇱"],armenia:["🇦🇲"],angola:["🇦🇴"],antarctica:["🇦🇶"],argentina:["🇦🇷"],samoa:["🇦🇸","🇼🇸"],austria:["🇦🇹"],aruba:["🇦🇼"],aland:["🇦🇽"],islands:["🇦🇽","🇨🇨","🇨🇰","🇫🇰","🇫🇴","🇬🇸","🇭🇲","🇮🇨","🇰🇾","🇲🇭","🇲🇵","🇵🇳","🇸🇧","🇹🇨","🇺🇲","🇻🇬","🇻🇮"],azerbaijan:["🇦🇿"],bosnia:["🇧🇦"],herzegovina:["🇧🇦"],barbados:["🇧🇧"],bangladesh:["🇧🇩"],belgium:["🇧🇪"],burkina:["🇧🇫"],faso:["🇧🇫"],bulgaria:["🇧🇬"],bahrain:["🇧🇭"],burundi:["🇧🇮"],benin:["🇧🇯"],st:["🇧🇱","🇰🇳","🇱🇨","🇲🇫","🇵🇲","🇸🇭","🇻🇨"],barthelemy:["🇧🇱"],bermuda:["🇧🇲"],brunei:["🇧🇳"],bolivia:["🇧🇴"],caribbean:["🇧🇶"],netherlands:["🇧🇶","🇳🇱"],brazil:["🇧🇷"],bahamas:["🇧🇸"],bhutan:["🇧🇹"],bouvet:["🇧🇻"],botswana:["🇧🇼"],belarus:["🇧🇾"],belize:["🇧🇿"],canada:["🇨🇦"],cocos:["🇨🇨"],keel:["🇨🇨"],congo:["🇨🇩","🇨🇬"],kinshasa:["🇨🇩"],central:["🇨🇫"],african:["🇨🇫"],republic:["🇨🇫","🇩🇴"],brazzaville:["🇨🇬"],switzerland:["🇨🇭"],cote:["🇨🇮"],ivoire:["🇨🇮"],chile:["🇨🇱"],cameroon:["🇨🇲"],china:["🇨🇳","🇭🇰","🇲🇴"],colombia:["🇨🇴"],clipperton:["🇨🇵"],costa:["🇨🇷"],rica:["🇨🇷"],cuba:["🇨🇺"],cape:["🇨🇻"],verde:["🇨🇻"],curacao:["🇨🇼"],cyprus:["🇨🇾"],czechia:["🇨🇿"],germany:["🇩🇪"],diego:["🇩🇬"],garcia:["🇩🇬"],djibouti:["🇩🇯"],denmark:["🇩🇰"],dominica:["🇩🇲"],dominican:["🇩🇴"],algeria:["🇩🇿"],ceuta:["🇪🇦"],melilla:["🇪🇦"],ecuador:["🇪🇨"],estonia:["🇪🇪"],egypt:["🇪🇬"],western:["🇪🇭"],sahara:["🇪🇭"],eritrea:["🇪🇷"],spain:["🇪🇸"],ethiopia:["🇪🇹"],european:["🇪🇺"],union:["🇪🇺"],finland:["🇫🇮"],fiji:["🇫🇯"],falkland:["🇫🇰"],micronesia:["🇫🇲"],faroe:["🇫🇴"],france:["🇫🇷"],gabon:["🇬🇦"],kingdom:["🇬🇧"],grenada:["🇬🇩"],georgia:["🇬🇪","🇬🇸"],guiana:["🇬🇫"],guernsey:["🇬🇬"],ghana:["🇬🇭"],gibraltar:["🇬🇮"],greenland:["🇬🇱"],gambia:["🇬🇲"],guinea:["🇬🇳","🇬🇶","🇬🇼","🇵🇬"],guadeloupe:["🇬🇵"],equatorial:["🇬🇶"],greece:["🇬🇷"],south:["🇬🇸","🇰🇷","🇸🇸","🇿🇦"],guatemala:["🇬🇹"],guam:["🇬🇺"],bissau:["🇬🇼"],guyana:["🇬🇾"],hong:["🇭🇰"],kong:["🇭🇰"],sar:["🇭🇰","🇲🇴"],heard:["🇭🇲"],mcdonald:["🇭🇲"],honduras:["🇭🇳"],croatia:["🇭🇷"],haiti:["🇭🇹"],hungary:["🇭🇺"],canary:["🇮🇨"],indonesia:["🇮🇩"],ireland:["🇮🇪"],israel:["🇮🇱"],isle:["🇮🇲"],india:["🇮🇳"],british:["🇮🇴","🇻🇬"],indian:["🇮🇴"],ocean:["🇮🇴"],territory:["🇮🇴"],iraq:["🇮🇶"],iran:["🇮🇷"],iceland:["🇮🇸"],italy:["🇮🇹"],jersey:["🇯🇪"],jamaica:["🇯🇲"],jordan:["🇯🇴"],kenya:["🇰🇪"],kyrgyzstan:["🇰🇬"],cambodia:["🇰🇭"],kiribati:["🇰🇮"],comoros:["🇰🇲"],kitts:["🇰🇳"],nevis:["🇰🇳"],north:["🇰🇵","🇲🇰"],korea:["🇰🇵","🇰🇷"],kuwait:["🇰🇼"],cayman:["🇰🇾"],kazakhstan:["🇰🇿"],laos:["🇱🇦"],lebanon:["🇱🇧"],lucia:["🇱🇨"],liechtenstein:["🇱🇮"],sri:["🇱🇰"],lanka:["🇱🇰"],liberia:["🇱🇷"],lesotho:["🇱🇸"],lithuania:["🇱🇹"],luxembourg:["🇱🇺"],latvia:["🇱🇻"],libya:["🇱🇾"],morocco:["🇲🇦"],monaco:["🇲🇨"],moldova:["🇲🇩"],montenegro:["🇲🇪"],martin:["🇲🇫"],madagascar:["🇲🇬"],marshall:["🇲🇭"],macedonia:["🇲🇰"],mali:["🇲🇱"],myanmar:["🇲🇲"],burma:["🇲🇲"],mongolia:["🇲🇳"],macao:["🇲🇴"],northern:["🇲🇵"],mariana:["🇲🇵"],martinique:["🇲🇶"],mauritania:["🇲🇷"],montserrat:["🇲🇸"],malta:["🇲🇹"],mauritius:["🇲🇺"],maldives:["🇲🇻"],malawi:["🇲🇼"],mexico:["🇲🇽"],malaysia:["🇲🇾"],mozambique:["🇲🇿"],namibia:["🇳🇦"],caledonia:["🇳🇨"],niger:["🇳🇪"],norfolk:["🇳🇫"],nigeria:["🇳🇬"],nicaragua:["🇳🇮"],norway:["🇳🇴"],nepal:["🇳🇵"],nauru:["🇳🇷"],niue:["🇳🇺"],zealand:["🇳🇿"],oman:["🇴🇲"],panama:["🇵🇦"],peru:["🇵🇪"],polynesia:["🇵🇫"],papua:["🇵🇬"],philippines:["🇵🇭"],pakistan:["🇵🇰"],poland:["🇵🇱"],pierre:["🇵🇲"],miquelon:["🇵🇲"],pitcairn:["🇵🇳"],puerto:["🇵🇷"],rico:["🇵🇷"],palestinian:["🇵🇸"],territories:["🇵🇸","🇹🇫"],portugal:["🇵🇹"],palau:["🇵🇼"],paraguay:["🇵🇾"],qatar:["🇶🇦"],reunion:["🇷🇪"],romania:["🇷🇴"],serbia:["🇷🇸"],russia:["🇷🇺"],rwanda:["🇷🇼"],saudi:["🇸🇦"],arabia:["🇸🇦"],solomon:["🇸🇧"],seychelles:["🇸🇨"],sudan:["🇸🇩","🇸🇸"],sweden:["🇸🇪"],singapore:["🇸🇬"],helena:["🇸🇭"],slovenia:["🇸🇮"],svalbard:["🇸🇯"],jan:["🇸🇯"],mayen:["🇸🇯"],slovakia:["🇸🇰"],sierra:["🇸🇱"],leone:["🇸🇱"],san:["🇸🇲"],marino:["🇸🇲"],senegal:["🇸🇳"],somalia:["🇸🇴"],suriname:["🇸🇷"],sao:["🇸🇹"],tome:["🇸🇹"],principe:["🇸🇹"],el:["🇸🇻"],salvador:["🇸🇻"],sint:["🇸🇽"],maarten:["🇸🇽"],syria:["🇸🇾"],eswatini:["🇸🇿"],tristan:["🇹🇦"],da:["🇹🇦"],cunha:["🇹🇦"],turks:["🇹🇨"],caicos:["🇹🇨"],chad:["🇹🇩"],southern:["🇹🇫"],togo:["🇹🇬"],thailand:["🇹🇭"],tajikistan:["🇹🇯"],tokelau:["🇹🇰"],timor:["🇹🇱"],leste:["🇹🇱"],turkmenistan:["🇹🇲"],tunisia:["🇹🇳"],tonga:["🇹🇴"],trinidad:["🇹🇹"],tobago:["🇹🇹"],tuvalu:["🇹🇻"],taiwan:["🇹🇼"],tanzania:["🇹🇿"],ukraine:["🇺🇦"],uganda:["🇺🇬"],us:["🇺🇲","🇻🇮"],outly:["🇺🇲"],nations:["🇺🇳"],states:["🇺🇸"],uruguay:["🇺🇾"],uzbekistan:["🇺🇿"],vatican:["🇻🇦"],city:["🇻🇦"],vincent:["🇻🇨"],grenadines:["🇻🇨"],venezuela:["🇻🇪"],virgin:["🇻🇬","🇻🇮"],vietnam:["🇻🇳"],vanuatu:["🇻🇺"],wallis:["🇼🇫"],futuna:["🇼🇫"],kosovo:["🇽🇰"],yemen:["🇾🇪"],mayotte:["🇾🇹"],zambia:["🇿🇲"],zimbabwe:["🇿🇼"],england:["🏴󠁧󠁢󠁥󠁮󠁧󠁿"],scotland:["🏴󠁧󠁢󠁳󠁣󠁴󠁿"],wales:["🏴󠁧󠁢󠁷󠁬󠁳󠁿"]}},{}],12:[function(e,t,a){t.exports={"߀":"0","́":""," ":" ","Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ɓ":"B","ｃ":"C","Ⓒ":"C","Ｃ":"C","Ꜿ":"C","Ḉ":"C","Ç":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ɗ":"D","Ɖ":"D","ᴅ":"D","Ꝺ":"D","Ð":"Dh","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","ɛ":"E","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","ᴇ":"E","ꝼ":"F","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","ɢ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","ȷ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","ϻ":"M","Ꞥ":"N","Ƞ":"N","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ɲ":"N","Ꞑ":"N","ᴎ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Œ":"OE","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Þ":"Th","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ɑ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","Ƃ":"b","ⓒ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c",C:"c","Ć":"c","Ĉ":"c","Ċ":"c","Č":"c","Ƈ":"c","Ȼ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","Ƌ":"d","Ꮷ":"d","ԁ":"d","Ɦ":"d","ð":"dh","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ﬀ":"ff","ﬁ":"fi","ﬂ":"fl","ﬃ":"ffi","ﬄ":"ffl","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ꝿ":"g","ᵹ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ɭ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ԉ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","ɔ":"o","ᴑ":"o","œ":"oe","ƣ":"oi","ꝏ":"oo","ȣ":"ou","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ρ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ʂ":"s","ß":"ss","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","þ":"th","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z"}},{}],13:[function(e,t,a){t.exports=[{s:9728,e:9747,w:1},{s:9748,e:9749,w:2},{s:9750,e:9799,w:1},{s:9800,e:9811,w:2},{s:9812,e:9854,w:1},{s:9855,e:9855,w:2},{s:9856,e:9874,w:1},{s:9875,e:9875,w:2},{s:9876,e:9888,w:1},{s:9889,e:9889,w:2},{s:9890,e:9897,w:1},{s:9898,e:9899,w:2},{s:9900,e:9916,w:1},{s:9917,e:9918,w:2},{s:9919,e:9923,w:1},{s:9924,e:9925,w:2},{s:9926,e:9933,w:1},{s:9934,e:9934,w:2},{s:9935,e:9939,w:1},{s:9940,e:9940,w:2},{s:9941,e:9961,w:1},{s:9962,e:9962,w:2},{s:9963,e:9969,w:1},{s:9970,e:9971,w:2},{s:9972,e:9972,w:1},{s:9973,e:9973,w:2},{s:9974,e:9977,w:1},{s:9978,e:9978,w:2},{s:9979,e:9980,w:1},{s:9981,e:9981,w:2},{s:9982,e:9983,w:1},{s:9984,e:9988,w:1},{s:9989,e:9989,w:2},{s:9990,e:9993,w:1},{s:9994,e:9995,w:2},{s:9996,e:10023,w:1},{s:10024,e:10024,w:2},{s:10025,e:10059,w:1},{s:10060,e:10060,w:2},{s:10061,e:10061,w:1},{s:10062,e:10062,w:2},{s:10063,e:10066,w:1},{s:10067,e:10069,w:2},{s:10070,e:10070,w:1},{s:10071,e:10071,w:2},{s:10072,e:10132,w:1},{s:10133,e:10135,w:2},{s:10136,e:10159,w:1},{s:10160,e:10160,w:2},{s:10161,e:10174,w:1},{s:10175,e:10175,w:2},{s:126976,e:126979,w:1},{s:126980,e:126980,w:2},{s:126981,e:127182,w:1},{s:127183,e:127183,w:2},{s:127184,e:127373,w:1},{s:127374,e:127374,w:2},{s:127375,e:127376,w:1},{s:127377,e:127386,w:2},{s:127387,e:127487,w:1},{s:127744,e:127776,w:2},{s:127777,e:127788,w:1},{s:127789,e:127797,w:2},{s:127798,e:127798,w:1},{s:127799,e:127868,w:2},{s:127869,e:127869,w:1},{s:127870,e:127891,w:2},{s:127892,e:127903,w:1},{s:127904,e:127946,w:2},{s:127947,e:127950,w:1},{s:127951,e:127955,w:2},{s:127956,e:127967,w:1},{s:127968,e:127984,w:2},{s:127985,e:127987,w:1},{s:127988,e:127988,w:2},{s:127989,e:127991,w:1},{s:127992,e:127994,w:2},{s:128e3,e:128062,w:2},{s:128063,e:128063,w:1},{s:128064,e:128064,w:2},{s:128065,e:128065,w:1},{s:128066,e:128252,w:2},{s:128253,e:128254,w:1},{s:128255,e:128317,w:2},{s:128318,e:128330,w:1},{s:128331,e:128334,w:2},{s:128335,e:128335,w:1},{s:128336,e:128359,w:2},{s:128360,e:128377,w:1},{s:128378,e:128378,w:2},{s:128379,e:128404,w:1},{s:128405,e:128406,w:2},{s:128407,e:128419,w:1},{s:128420,e:128420,w:2},{s:128421,e:128506,w:1},{s:128507,e:128591,w:2},{s:128592,e:128639,w:1},{s:128640,e:128709,w:2},{s:128710,e:128715,w:1},{s:128716,e:128716,w:2},{s:128717,e:128719,w:1},{s:128720,e:128722,w:2},{s:128723,e:128724,w:1},{s:128725,e:128727,w:2},{s:128728,e:128746,w:1},{s:128747,e:128748,w:2},{s:128749,e:128755,w:1},{s:128756,e:128764,w:2},{s:128765,e:128991,w:1},{s:128992,e:129003,w:2},{s:129004,e:129291,w:1},{s:129292,e:129338,w:2},{s:129339,e:129339,w:1},{s:129340,e:129349,w:2},{s:129350,e:129350,w:1},{s:129351,e:129400,w:2},{s:129401,e:129401,w:1},{s:129402,e:129483,w:2},{s:129484,e:129484,w:1},{s:129485,e:129535,w:2},{s:129536,e:129647,w:1},{s:129648,e:129652,w:2},{s:129653,e:129655,w:1},{s:129656,e:129658,w:2},{s:129659,e:129663,w:1},{s:129664,e:129670,w:2},{s:129671,e:129679,w:1},{s:129680,e:129704,w:2},{s:129705,e:129711,w:1},{s:129712,e:129718,w:2},{s:129719,e:129727,w:1},{s:129728,e:129730,w:2},{s:129731,e:129743,w:1},{s:129744,e:129750,w:2},{s:129751,e:129791,w:1}]},{}],14:[function(e,t,a){"use strict";const r=e("./json-data/latinize-map.json");t.exports=function(e){return e.replace(/[^\u0000-\u007e]/g,e=>{return r[e]||e})}},{"./json-data/latinize-map.json":12}],15:[function(e,t,a){"use strict";a.resize=function(e,t){if(e.length===t){return e}else if(e.length>t){return e.slice(0,t)}return e+" ".repeat(t-e.length)};a.occurrenceCount=function(e,t,a=false){if(!e||!t){return 0}var r=0,n=0,i=a?1:t.length;while((n=e.indexOf(t,n))!==-1){r++;n+=i}return r}},{}],16:[function(e,t,a){"use strict";const v=1;const C=2;const S=3;const x=4;const j=5;function I(e,t){if(M(t)){return C}if(t<=31||t===127){return v}if(z(t)){return x}if(e.toUpperCase()!==e.toLowerCase()){return S}return j}function M(e){if(e===32||e===9||e===160||e===45||e===95){return true}return false}function z(e){if(e>=48&&e<=57){return true}return false}function r(e,t){e=""+e;t=""+t;var a,r,n,i,o,s,l,c=e.trim(),u=c.length,g,f,p,h,d,m,b,y=t.trim(),w=y.length,k=0;for(a=g=0;a<u&&g<w;a++,g++){n=c[a];p=y[g];i=c.charCodeAt(a);h=y.charCodeAt(g);o=I(n,i);d=I(p,h);if(o!==d){return o-d}switch(o){case C:while(M(c.charCodeAt(a+1))){a++}while(M(y.charCodeAt(g+1))){g++}break;case v:case j:if(i!==h){return i-h}break;case S:s=n.toLowerCase();m=p.toLowerCase();if(s!==m){return s>m?1:-1}if(!k&&n!==p){k=n!==s?-1:1}break;case x:r=a+1;while(z(c.charCodeAt(r))){r++}l=parseFloat(c.slice(a,r));f=g+1;while(z(y.charCodeAt(f))){f++}b=parseFloat(y.slice(g,f));if(l!==b){return l-b}if(!k&&r-a!==f-g){k=r-a-(f-g)}a=r-1;g=f-1;break}}if(k){return k}return u-a-(w-g)||u-w||e.length-t.length}t.exports=r},{}],17:[function(e,t,a){"use strict";var n=e("./escape.js");a.regexp={};a.regexp.array2alternatives=function e(t){var a,r=t.slice();r.sort((e,t)=>{return t.length-e.length});for(a=0;a<r.length;a++){r[a]=n.regExpPattern(r[a])}return r.join("|")}},{"./escape.js":6}],18:[function(e,t,a){"use strict";const r={};t.exports=r;Object.assign(r,{escape:e("./escape.js")},{ansi:e("./ansi.js")},{unicode:e("./unicode.js")});Object.assign(r,e("./format.js"),e("./misc.js"),e("./inspect.js"),e("./regexp.js"),e("./camel.js"),{latinize:e("./latinize.js"),toTitleCase:e("./toTitleCase.js"),wordwrap:e("./wordwrap.js"),naturalSort:e("./naturalSort.js"),fuzzy:e("./fuzzy.js"),StringNumber:e("./StringNumber.js"),english:e("./english.js"),emoji:e("./emoji.js")})},{"./StringNumber.js":1,"./ansi.js":2,"./camel.js":3,"./emoji.js":4,"./english.js":5,"./escape.js":6,"./format.js":7,"./fuzzy.js":8,"./inspect.js":9,"./latinize.js":14,"./misc.js":15,"./naturalSort.js":16,"./regexp.js":17,"./toTitleCase.js":19,"./unicode.js":20,"./wordwrap.js":21}],19:[function(e,t,a){"use strict";const s={underscoreToSpace:true,lowerCaseWords:new Set(["a","an","the","for","and","nor","but","or","yet","so","of","on","off","in","into","by","with","to","at","up","down","as"])};t.exports=(a,e=s)=>{if(!a||typeof a!=="string"){return""}var t=e.dashToSpace??s.dashToSpace,r=e.underscoreToSpace??s.underscoreToSpace,n=e.zealous??s.zealous,i=e.preserveAllCaps??s.preserveAllCaps,o=e.lowerCaseWords??s.lowerCaseWords;o=o instanceof Set?o:Array.isArray(o)?new Set(o):null;if(t){a=a.replace(/-+/g," ")}if(r){a=a.replace(/_+/g," ")}a=a.replace(/ +/g," ").trim();return a.replace(/[^\s_-]+/g,(t,e)=>{if(o&&e&&e+t.length<a.length){let e=t.toLowerCase();if(o.has(e)){return e}}if(n){if(i&&t===t.toUpperCase()){return t}return t[0].toUpperCase()+t.slice(1).toLowerCase()}return t[0].toUpperCase()+t.slice(1)})}},{}],20:[function(t,e,a){"use strict";const u={};e.exports=u;u.encode=e=>String.fromCodePoint(...e);u.decode=e=>Array.from(e,e=>e.codePointAt(0));u.firstCodePoint=e=>e.codePointAt(0);u.firstChar=e=>e.length?String.fromCodePoint(e.codePointAt(0)):undefined;u.toArray=e=>Array.from(e);u.toCells=(e,t,a=4,r=0,...n)=>{var i,o,s,l,c=[];for(i of t){o=i.codePointAt(0);if(o===10){r=0}else if(o===9){s=a-r%a-1;c.push(new e("\t",1,...n));r+=1+s;while(s--){c.push(new e(" ",-2,...n))}}else{l=u.codePointWidth(o),c.push(new e(i,l,...n));r+=l;while(--l>0){c.push(new e(" ",-1,...n))}}}return c};u.fromCells=e=>{var t,a="";for(t of e){if(!t.filler){a+=t.char}}return a};u.length=e=>{var t,a=0;for(t of e){a++}return a};u.truncateLength=u.truncate=(t,a)=>{var r=0,n=0;for(let e of t){if(n===a){return t.slice(0,r)}n++;r+=e.length}return t};u.width=e=>{var t,a=0;for(t of e){a+=u.codePointWidth(t.codePointAt(0))}return a};u.arrayWidth=(e,t)=>{var a,r=0;if(t===undefined){t=e.length}for(a=0;a<t;a++){r+=u.isFullWidth(e[a])?2:1}return r};var i=0;u.getLastTruncateWidth=()=>i;u.widthLimit=u.truncateWidth=(e,t)=>{var a,r,n=0;i=0;for(a of e){r=u.codePointWidth(a.codePointAt(0));if(i+r>t){return e.slice(0,n)}i+=r;n+=a.length}return e};u.surrogatePair=e=>{var t=e.charCodeAt(0);if(t<55296||t>=57344){return 0}else if(t<56320){return 1}return-1};u.isFullWidth=e=>u.isFullWidthCodePoint(e.codePointAt(0));u.charWidth=e=>u.codePointWidth(e.codePointAt(0));const r=new Map;(function(){var e=t("./json-data/unicode-emoji-width-ranges.json");for(let t of e){for(let e=t.s;e<=t.e;e++){r.set(e,t.w)}}})();u.codePointWidth=e=>{if(u.isEmojiCodePoint(e)){return r.get(e)??2}if(e>=4352&&(e<=4447||e===9001||e===9002||11904<=e&&e<=12871&&e!==12351||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141)){return 2}if(u.isEmojiModifierCodePoint(e)||u.isZeroWidthDiacriticCodePoint(e)){return 0}return 1};u.isFullWidthCodePoint=e=>u.codePointWidth(e)===2;u.toFullWidth=e=>{return String.fromCodePoint(...Array.from(e,e=>{var t=e.codePointAt(0);return t>=33&&t<=126?65280+t-32:t}))};u.isZeroWidthDiacritic=e=>u.isZeroWidthDiacriticCodePoint(e.codePointAt(0));u.isZeroWidthDiacriticCodePoint=e=>768<=e&&e<=879||6832<=e&&e<=6911||7616<=e&&e<=7679||8400<=e&&e<=8447||65056<=e&&e<=65071||e===12441||e===12442||2304<=e&&e<=2307||2362<=e&&e<=2391&&e!==2365&&e!==2384||e===2402||e===2403||e===3633||3636<=e&&e<=3642||3655<=e&&e<=3662;u.isEmoji=e=>u.isEmojiCodePoint(e.codePointAt(0));u.isEmojiCodePoint=e=>9728<=e&&e<=9983||9984<=e&&e<=10175||126976<=e&&e<=127487||127744<=e&&e<=127994||128e3<=e&&e<=129791;u.isEmojiModifier=e=>u.isEmojiModifierCodePoint(e.codePointAt(0));u.isEmojiModifierCodePoint=e=>127995<=e&&e<=127999||e===65039},{"./json-data/unicode-emoji-width-ranges.json":13}],21:[function(e,t,a){"use strict";const k=e("./unicode.js");const v={"!":true,"?":true,":":true,";":true};t.exports=function e(t,a){var r=0,n,i,o,s,l,c,u,g,f,p,h=true,d=k.toArray(t),m=false,b,y=[];if(typeof a!=="object"){a={width:a}}if(!a.width||typeof a.width!=="number"||a.width<=0){a.width=80}o=a.offset?a.width-a.offset:a.width;if(typeof a.glue!=="string"){a.glue="\n"}if(a.regroupFn){d=a.regroupFn(d);p=a.charWidthFn||k.width}else{p=a.charWidthFn||k.charWidth}c=d.length;var w=()=>{if(!h||m){while(d[r]===" "){r++}if(m&&d[r]==="\n"){h=true;r++}}if(r>=c){return null}h=false;m=false;f=false;n=u=r;l=g=0;for(;;){if(n>=c){return d.slice(r,n).join("")}if(d[n]==="\n"){h=true;s=d.slice(r,n++).join("");if(a.fill){s+=" ".repeat(o-l)}return s}if(a.skipFn){i=a.skipFn(d,n);if(i!==n){n=i;continue}}if(d[n]===" "&&!f&&!v[d[n+1]]){u=n;g=l}else{f=false}l+=p(d[n]);if(l>o){m=true;if(u!==r){n=u}else if(o<a.width){n=r;return""}s=d.slice(r,n).join("");if(a.fill){s+=" ".repeat(o-g)}return s}n++}};while(r<c&&(b=w())!==null){y.push(b);r=n;o=a.width}if(h){y.push("")}if(!a.noTrim&&!a.fill){y=y.map((e,t)=>t===y.length-1?e:e.trimRight())}if(!a.noJoin){y=y.join(a.glue)}if(a.updateOffset){a.offset=l}return y}},{"./unicode.js":20}],22:[function(e,t,a){},{}],23:[function(e,t,a){t.exports=function(e){return e!=null&&(r(e)||n(e)||!!e._isBuffer)};function r(e){return!!e.constructor&&typeof e.constructor.isBuffer==="function"&&e.constructor.isBuffer(e)}function n(e){return typeof e.readFloatLE==="function"&&typeof e.slice==="function"&&r(e.slice(0,0))}},{}],24:[function(e,t,a){var r=t.exports={};var n;var i;function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}(function(){try{if(typeof setTimeout==="function"){n=setTimeout}else{n=o}}catch(e){n=o}try{if(typeof clearTimeout==="function"){i=clearTimeout}else{i=s}}catch(e){i=s}})();function l(t){if(n===setTimeout){return setTimeout(t,0)}if((n===o||!n)&&setTimeout){n=setTimeout;return setTimeout(t,0)}try{return n(t,0)}catch(e){try{return n.call(null,t,0)}catch(e){return n.call(this,t,0)}}}function c(t){if(i===clearTimeout){return clearTimeout(t)}if((i===s||!i)&&clearTimeout){i=clearTimeout;return clearTimeout(t)}try{return i(t)}catch(e){try{return i.call(null,t)}catch(e){return i.call(this,t)}}}var u=[];var g=false;var f;var p=-1;function h(){if(!g||!f){return}g=false;if(f.length){u=f.concat(u)}else{p=-1}if(u.length){d()}}function d(){if(g){return}var e=l(h);g=true;var t=u.length;while(t){f=u;u=[];while(++p<t){if(f){f[p].run()}}p=-1;t=u.length}f=null;g=false;c(e)}r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var a=1;a<arguments.length;a++){t[a-1]=arguments[a]}}u.push(new m(e,t));if(u.length===1&&!g){l(d)}};function m(e,t){this.fun=e;this.array=t}m.prototype.run=function(){this.fun.apply(null,this.array)};r.title="browser";r.browser=true;r.env={};r.argv=[];r.version="";r.versions={};function b(){}r.on=b;r.addListener=b;r.once=b;r.off=b;r.removeListener=b;r.removeAllListeners=b;r.emit=b;r.prependListener=b;r.prependOnceListener=b;r.listeners=function(e){return[]};r.binding=function(e){throw new Error("process.binding is not supported")};r.cwd=function(){return"/"};r.chdir=function(e){throw new Error("process.chdir is not supported")};r.umask=function(){return 0}},{}]},{},[18])(18)});