{"name": "tree-kit", "version": "0.8.8", "description": "Tree utilities which provides a full-featured extend and object-cloning facility, and various tools to deal with nested object structures.", "main": "lib/tree.js", "directories": {"test": "test", "bench": "bench"}, "engines": {"node": ">=16.13.0"}, "devDependencies": {"browserify": "^17.0.0", "uglify-es": "^3.3.9"}, "scripts": {"test": "tea-time -R dot"}, "repository": {"type": "git", "url": "https://github.com/cronvel/tree-kit.git"}, "keywords": ["tree", "extend", "clone", "prototype", "inherit", "deep", "diff", "mask"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/cronvel/tree-kit/issues"}, "config": {"tea-time": {"coverDir": ["lib"]}}, "copyright": {"title": "Tree Kit", "years": [2014, 2021], "owner": "<PERSON><PERSON><PERSON>"}}