"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemTool = void 0;
const os_1 = __importDefault(require("os"));
const cross_spawn_1 = require("cross-spawn");
const nanoid_1 = require("nanoid");
class SystemTool {
    async getInfo(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const type = args.type || 'basic';
            let output = '';
            switch (type) {
                case 'basic':
                    output = await this.getBasicInfo();
                    break;
                case 'detailed':
                    output = await this.getDetailedInfo();
                    break;
                case 'processes':
                    output = await this.getProcessInfo();
                    break;
                case 'network':
                    output = await this.getNetworkInfo();
                    break;
                default:
                    throw new Error(`Unknown info type: ${type}`);
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output,
                executionTime: Date.now() - startTime,
                metadata: {
                    type,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async getBasicInfo() {
        const info = {
            platform: os_1.default.platform(),
            arch: os_1.default.arch(),
            release: os_1.default.release(),
            hostname: os_1.default.hostname(),
            uptime: this.formatUptime(os_1.default.uptime()),
            nodeVersion: process.version,
            workingDirectory: process.cwd(),
            homeDirectory: os_1.default.homedir(),
            totalMemory: this.formatBytes(os_1.default.totalmem()),
            freeMemory: this.formatBytes(os_1.default.freemem()),
            cpuCount: os_1.default.cpus().length,
            cpuModel: os_1.default.cpus()[0]?.model || 'Unknown',
        };
        return this.formatInfo('Basic System Information', info);
    }
    async getDetailedInfo() {
        const basicInfo = await this.getBasicInfo();
        const cpus = os_1.default.cpus();
        const networkInterfaces = os_1.default.networkInterfaces();
        let detailedInfo = basicInfo + '\n\n';
        // CPU Information
        detailedInfo += '=== CPU Information ===\n';
        detailedInfo += `CPU Count: ${cpus.length}\n`;
        detailedInfo += `CPU Model: ${cpus[0]?.model || 'Unknown'}\n`;
        detailedInfo += `CPU Speed: ${cpus[0]?.speed || 'Unknown'} MHz\n\n`;
        // Memory Information
        const memUsage = process.memoryUsage();
        detailedInfo += '=== Memory Information ===\n';
        detailedInfo += `Total System Memory: ${this.formatBytes(os_1.default.totalmem())}\n`;
        detailedInfo += `Free System Memory: ${this.formatBytes(os_1.default.freemem())}\n`;
        detailedInfo += `Process RSS: ${this.formatBytes(memUsage.rss)}\n`;
        detailedInfo += `Process Heap Used: ${this.formatBytes(memUsage.heapUsed)}\n`;
        detailedInfo += `Process Heap Total: ${this.formatBytes(memUsage.heapTotal)}\n`;
        detailedInfo += `Process External: ${this.formatBytes(memUsage.external)}\n\n`;
        // Network Interfaces
        detailedInfo += '=== Network Interfaces ===\n';
        for (const [name, interfaces] of Object.entries(networkInterfaces)) {
            if (interfaces) {
                detailedInfo += `${name}:\n`;
                for (const iface of interfaces) {
                    detailedInfo += `  ${iface.family}: ${iface.address}\n`;
                }
            }
        }
        // Environment Variables (selected)
        detailedInfo += '\n=== Environment Variables ===\n';
        const envVars = ['PATH', 'HOME', 'USER', 'SHELL', 'TERM', 'NODE_ENV'];
        for (const envVar of envVars) {
            if (process.env[envVar]) {
                detailedInfo += `${envVar}: ${process.env[envVar]}\n`;
            }
        }
        return detailedInfo;
    }
    async getProcessInfo() {
        let output = '=== Process Information ===\n';
        // Current process info
        output += `Current Process:\n`;
        output += `  PID: ${process.pid}\n`;
        output += `  PPID: ${process.ppid}\n`;
        output += `  Title: ${process.title}\n`;
        output += `  Version: ${process.version}\n`;
        output += `  Platform: ${process.platform}\n`;
        output += `  Arch: ${process.arch}\n`;
        output += `  Uptime: ${this.formatUptime(process.uptime())}\n\n`;
        // Try to get system processes (platform-specific)
        try {
            const processes = await this.getSystemProcesses();
            if (processes.length > 0) {
                output += 'System Processes (top 10 by memory):\n';
                output += 'PID\tNAME\t\tMEMORY\n';
                output += '---\t----\t\t------\n';
                processes.slice(0, 10).forEach(proc => {
                    const name = proc.name.padEnd(15);
                    const memory = proc.memoryUsage ? this.formatBytes(proc.memoryUsage) : 'N/A';
                    output += `${proc.pid}\t${name}\t${memory}\n`;
                });
            }
        }
        catch (error) {
            output += 'Could not retrieve system processes\n';
        }
        return output;
    }
    async getNetworkInfo() {
        let output = '=== Network Information ===\n';
        const networkInterfaces = os_1.default.networkInterfaces();
        for (const [name, interfaces] of Object.entries(networkInterfaces)) {
            if (interfaces) {
                output += `\n${name}:\n`;
                for (const iface of interfaces) {
                    output += `  Family: ${iface.family}\n`;
                    output += `  Address: ${iface.address}\n`;
                    output += `  Netmask: ${iface.netmask}\n`;
                    output += `  MAC: ${iface.mac}\n`;
                    output += `  Internal: ${iface.internal}\n`;
                    output += `  Scope ID: ${iface.scopeid || 'N/A'}\n`;
                    output += '  ---\n';
                }
            }
        }
        // Try to get additional network info
        try {
            const networkStats = await this.getNetworkStats();
            output += '\n' + networkStats;
        }
        catch (error) {
            output += '\nCould not retrieve network statistics\n';
        }
        return output;
    }
    async getSystemProcesses() {
        return new Promise((resolve) => {
            // Platform-specific process listing
            let command;
            let args;
            switch (os_1.default.platform()) {
                case 'win32':
                    command = 'tasklist';
                    args = ['/fo', 'csv'];
                    break;
                case 'darwin':
                case 'linux':
                    command = 'ps';
                    args = ['aux'];
                    break;
                default:
                    resolve([]);
                    return;
            }
            const child = (0, cross_spawn_1.spawn)(command, args, { stdio: 'pipe' });
            let output = '';
            child.stdout?.on('data', (data) => {
                output += data.toString();
            });
            child.on('close', () => {
                try {
                    const parsed = this.parseProcessOutput(output, os_1.default.platform());
                    resolve(parsed);
                }
                catch (error) {
                    resolve([]);
                }
            });
            child.on('error', () => {
                resolve([]);
            });
        });
    }
    parseProcessOutput(output, platform) {
        const processes = [];
        const lines = output.split('\n').slice(1); // Skip header
        for (const line of lines) {
            if (!line.trim())
                continue;
            try {
                let process;
                if (platform === 'win32') {
                    // Parse Windows tasklist output
                    const parts = line.split(',').map(p => p.replace(/"/g, ''));
                    if (parts.length >= 5) {
                        process = {
                            pid: parseInt(parts[1]) || 0,
                            name: parts[0] || 'Unknown',
                            command: parts[0] || 'Unknown',
                            status: 'running',
                            startTime: new Date(), // Not available in tasklist
                            memoryUsage: this.parseMemoryString(parts[4]),
                        };
                    }
                    else {
                        continue;
                    }
                }
                else {
                    // Parse Unix ps output
                    const parts = line.trim().split(/\s+/);
                    if (parts.length >= 11) {
                        process = {
                            pid: parseInt(parts[1]) || 0,
                            name: parts[10] || 'Unknown',
                            command: parts.slice(10).join(' ') || 'Unknown',
                            status: 'running',
                            startTime: new Date(), // Would need additional parsing
                            cpuUsage: parseFloat(parts[2]) || 0,
                            memoryUsage: parseFloat(parts[3]) || 0,
                        };
                    }
                    else {
                        continue;
                    }
                }
                processes.push(process);
            }
            catch (error) {
                // Skip malformed lines
            }
        }
        return processes.sort((a, b) => (b.memoryUsage || 0) - (a.memoryUsage || 0));
    }
    async getNetworkStats() {
        return new Promise((resolve) => {
            let command;
            let args;
            switch (os_1.default.platform()) {
                case 'win32':
                    command = 'netstat';
                    args = ['-e'];
                    break;
                case 'darwin':
                    command = 'netstat';
                    args = ['-i'];
                    break;
                case 'linux':
                    command = 'cat';
                    args = ['/proc/net/dev'];
                    break;
                default:
                    resolve('Network statistics not available on this platform');
                    return;
            }
            const child = (0, cross_spawn_1.spawn)(command, args, { stdio: 'pipe' });
            let output = '';
            child.stdout?.on('data', (data) => {
                output += data.toString();
            });
            child.on('close', () => {
                resolve(output || 'No network statistics available');
            });
            child.on('error', () => {
                resolve('Could not retrieve network statistics');
            });
        });
    }
    formatInfo(title, info) {
        let output = `=== ${title} ===\n`;
        for (const [key, value] of Object.entries(info)) {
            const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
            output += `${formattedKey}: ${value}\n`;
        }
        return output;
    }
    formatBytes(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        if (bytes === 0)
            return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        const parts = [];
        if (days > 0)
            parts.push(`${days}d`);
        if (hours > 0)
            parts.push(`${hours}h`);
        if (minutes > 0)
            parts.push(`${minutes}m`);
        if (secs > 0)
            parts.push(`${secs}s`);
        return parts.join(' ') || '0s';
    }
    parseMemoryString(memStr) {
        const match = memStr.match(/(\d+(?:,\d+)*)\s*(\w+)?/);
        if (!match)
            return 0;
        const num = parseInt(match[1].replace(/,/g, ''));
        const unit = match[2]?.toLowerCase();
        switch (unit) {
            case 'kb':
            case 'k':
                return num * 1024;
            case 'mb':
            case 'm':
                return num * 1024 * 1024;
            case 'gb':
            case 'g':
                return num * 1024 * 1024 * 1024;
            default:
                return num;
        }
    }
}
exports.SystemTool = SystemTool;
//# sourceMappingURL=system.js.map