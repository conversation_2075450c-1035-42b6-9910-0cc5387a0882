(function(e){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=e()}else if(typeof define==="function"&&define.amd){define([],e)}else{var t;if(typeof window!=="undefined"){t=window}else if(typeof global!=="undefined"){t=global}else if(typeof self!=="undefined"){t=self}else{t=this}t.NextGenEvents=e()}})(function(){var e,t,n;return function(){function e(t,n,i){function r(o,c){if(!n[o]){if(!t[o]){var a="function"==typeof require&&require;if(!c&&a)return a(o,!0);if(s)return s(o,!0);var u=new Error("Cannot find module '"+o+"'");throw u.code="MODULE_NOT_FOUND",u}var f=n[o]={exports:{}};t[o][0].call(f.exports,function(e){var n=t[o][1][e];return r(n||e)},f,f.exports,e,t,n,i)}return n[o].exports}for(var s="function"==typeof require&&require,o=0;o<i.length;o++)r(i[o]);return r}return e}()({1:[function(e,t,n){(function(n,i){"use strict";const r=n.browser?window.setImmediate:n.nextTick;if(!i.__NEXTGEN_EVENTS__){i.__NEXTGEN_EVENTS__={recursions:0}}var s=i.__NEXTGEN_EVENTS__;function o(){}t.exports=o;o.prototype.__prototypeUID__="nextgen-events/NextGenEvents";o.prototype.__prototypeVersion__=e("../package.json").version;o.SYNC=-Infinity;o.DESYNC=-1;o.defaultMaxListeners=Infinity;o.init=function(){Object.defineProperty(this,"__ngev",{configurable:true,value:new o.Internal})};o.Internal=function(e){this.nice=o.SYNC;this.interruptible=false;this.contexts={};this.desync=setImmediate;this.depth=0;this.states={};this.stateGroups={};this.listeners={error:[],interrupt:[],newListener:[],removeListener:[]};this.hasListenerPriority=false;this.maxListeners=o.defaultMaxListeners;if(e){this.nice=e.nice;this.interruptible=e.interruptible;Object.assign(this.states,e.states),Object.assign(this.stateGroups,e.stateGroups),Object.keys(e.listeners).forEach(t=>{this.listeners[t]=e.listeners[t].slice()});Object.keys(e.contexts).forEach(t=>{var n=e.contexts[t];this.contexts[t]={nice:n.nice,ready:true,status:n.status,serial:n.serial,scopes:{}}})}};o.initFrom=function(e){if(!e.__ngev){o.init.call(e)}Object.defineProperty(this,"__ngev",{configurable:true,value:new o.Internal(e.__ngev)})};o.mergeListeners=function(e){if(!this.__ngev){o.init.call(this)}var t=this.__ngev.listeners;this.__ngev.listeners={};Object.keys(t).forEach(e=>{this.__ngev.listeners[e]=[]});e.forEach(e=>{if(!e.__ngev){o.init.call(e)}Object.keys(e.__ngev.listeners).forEach(e=>{if(!this.__ngev.listeners[e]){this.__ngev.listeners[e]=[]}})});Object.keys(this.__ngev.listeners).forEach(n=>{var i,r,s=[];if(t[n]){t[n].forEach(t=>{for(i=0,r=e.length;i<r;i++){if(!e[i].__ngev.listeners[n]||e[i].__ngev.listeners[n].indexOf(t)===-1){s.push(t);break}}})}e.forEach(e=>{e.__ngev.listeners[n].forEach(e=>{if(this.__ngev.listeners[n].indexOf(e)===-1&&s.indexOf(e)===-1){this.__ngev.listeners[n].push(e)}})})})};o.filterOutCallback=function(e,t){return e!==t};o.prototype.addListener=function(e,t,i){var r,s;if(!this.__ngev){o.init.call(this)}if(!this.__ngev.listeners[e]){this.__ngev.listeners[e]=[]}if(!e||typeof e!=="string"){throw new TypeError(".addListener(): argument #0 should be a non-empty string")}if(typeof t==="function"){r={};if(!i||typeof i!=="object"){i={}}}else if(i===true&&t&&typeof t==="object"){i=r=t;t=undefined}else{i=t;if(!i||typeof i!=="object"){throw new TypeError(".addListener(): a function or an object with a 'fn' property which value is a function should be provided")}t=undefined;r={}}r.fn=t||i.fn;r.id=i.id!==undefined?i.id:r.fn;if(i.unique){if(this.__ngev.listeners[e].find(e=>e.id===r.id)){return}}r.once=!!i.once;r.async=!!i.async;r.eventObject=!!i.eventObject;r.nice=i.nice!==undefined?Math.floor(i.nice):o.SYNC;r.priority=+i.priority||0;r.context=i.context&&(typeof i.context==="string"||typeof i.context==="object")?i.context:null;if(typeof r.fn!=="function"){throw new TypeError(".addListener(): a function or an object with a 'fn' property which value is a function should be provided")}if(typeof r.context==="string"){r.context=this.__ngev.contexts[r.context]||this.addListenerContext(r.context)}r.event=e;if(this.__ngev.listeners.newListener.length){s=this.__ngev.listeners.newListener.slice();this.__ngev.listeners[e].push(r);o.emitEvent({emitter:this,name:"newListener",args:[[r]],listeners:s});if(this.__ngev.states[e]){o.emitToOneListener(this.__ngev.states[e],r)}return this}this.__ngev.listeners[e].push(r);if(this.__ngev.hasListenerPriority){this.__ngev.listeners[e].sort((e,t)=>t.priority-e.priority)}if(this.__ngev.listeners[e].length===this.__ngev.maxListeners+1){n.emitWarning("Possible NextGenEvents memory leak detected. "+this.__ngev.listeners[e].length+" "+e+" listeners added. Use emitter.setMaxListeners() to increase limit",{type:"MaxListenersExceededWarning"})}if(this.__ngev.states[e]){o.emitToOneListener(this.__ngev.states[e],r)}return this};o.prototype.on=o.prototype.addListener;o.prototype.once=function(e,t,n){if(t&&typeof t==="object"){t.once=true}else if(n&&typeof n==="object"){n.once=true}else{n={once:true}}return this.addListener(e,t,n)};o.prototype.waitFor=function(e){return new Promise(t=>{this.addListener(e,e=>t(e),{once:true})})};o.prototype.waitForAll=function(e){return new Promise(t=>{this.addListener(e,(...e)=>t(e),{once:true})})};o.prototype.removeListener=function(e,t){if(!e||typeof e!=="string"){throw new TypeError(".removeListener(): argument #0 should be a non-empty string")}if(!this.__ngev){o.init.call(this)}var n=this.__ngev.listeners[e];if(!n||!n.length){return this}var i,r,s=0,c=n.length,a=this.__ngev.listeners.removeListener.length;if(a){r=[]}for(i=0;i<c;i++){if(n[i].id===t){s++;if(a){r.push(n[i])}}else if(s){n[i-s]=n[i]}}if(s){n.length-=s}if(a&&r.length){this.emit("removeListener",r)}return this};o.prototype.off=o.prototype.removeListener;o.prototype.removeAllListeners=function(e){var t;if(!this.__ngev){o.init.call(this)}if(e){if(!e||typeof e!=="string"){throw new TypeError(".removeAllListeners(): argument #0 should be undefined or a non-empty string")}if(!this.__ngev.listeners[e]){this.__ngev.listeners[e]=[]}t=this.__ngev.listeners[e];this.__ngev.listeners[e]=[];if(t.length&&this.__ngev.listeners.removeListener.length){this.emit("removeListener",t)}}else{this.__ngev.listeners={error:[],interrupt:[],newListener:[],removeListener:[]}}return this};o.listenerWrapper=function(e,t,n,i,r){var c,a,u=t.master||t,f=!!t.master||t.emitter.__ngev.interruptible;if(u.interrupt){return}if(e.async){if(n){n.ready=!i}if(r<0){if(s.recursions>=-r){t.emitter.__ngev.desync(o.listenerWrapper.bind(undefined,e,t,n,i,o.SYNC));return}}else{setTimeout(o.listenerWrapper.bind(undefined,e,t,n,i,o.SYNC),r);return}a=(e=>{u.listenersDone++;if(e&&f&&!u.interrupt&&t.name!=="interrupt"){u.interrupt=e;if(u.callback){o.emitCallback(t)}t.emitter.emit("interrupt",u.interrupt)}else if(u.listenersDone>=u.listeners.length&&u.callback){o.emitCallback(t)}if(i){o.processScopeQueue(n,true,true)}});if(e.eventObject){e.fn(t,a)}else{c=e.fn(...t.args,a)}}else{if(r<0){if(s.recursions>=-r){t.emitter.__ngev.desync(o.listenerWrapper.bind(undefined,e,t,n,i,o.SYNC));return}}else{setTimeout(o.listenerWrapper.bind(undefined,e,t,n,i,o.SYNC),r);return}if(e.eventObject){e.fn(t)}else{c=e.fn(...t.args)}u.listenersDone++}if(c&&f&&!u.interrupt&&t.name!=="interrupt"){u.interrupt=c;if(u.callback){o.emitCallback(t)}t.emitter.emit("interrupt",u.interrupt)}else if(u.listenersDone>=u.listeners.length&&u.callback){o.emitCallback(t)}};var c=0;o.prototype.emit=function(...e){var t=o.createEvent(this,...e);return o.emitEvent(t)};o.prototype.emitIfListener=function(...e){var t=typeof e[0]==="number"?e[1]:e[0];if(!this.__ngev||!this.__ngev.listeners[t]||!this.__ngev.listeners[t].length){return null}var n=o.createEvent(this,...e);return o.emitEvent(n)};o.prototype.waitForEmit=function(...e){return new Promise(t=>{this.emit(...e,e=>t(e))})};o.createEvent=function(e,...t){var n={emitter:e,interrupt:null,master:null,sync:true};if(typeof t[0]==="number"){n.nice=Math.floor(t[0]);n.name=t[1];if(!n.name||typeof n.name!=="string"){throw new TypeError(".emit(): when argument #0 is a number, argument #1 should be a non-empty string")}if(typeof t[t.length-1]==="function"){n.callback=t[t.length-1];n.args=t.slice(2,-1)}else{n.args=t.slice(2)}}else{n.name=t[0];if(!n.name||typeof n.name!=="string"){throw new TypeError(".emit(): argument #0 should be an number or a non-empty string")}if(typeof t[t.length-1]==="function"){n.callback=t[t.length-1];n.args=t.slice(1,-1)}else{n.args=t.slice(1)}}return n};o.emitEvent=function(e){var t=e.emitter,n,i,r=0,a,u;if(!t.__ngev){o.init.call(t)}a=t.__ngev.states[e.name];if(a!==undefined){if(a&&e.args.length===a.args.length&&e.args.every((e,t)=>e===a.args[t])){return}t.__ngev.stateGroups[e.name].forEach(e=>{t.__ngev.states[e]=null});t.__ngev.states[e.name]=e}if(!t.__ngev.listeners[e.name]){t.__ngev.listeners[e.name]=[]}e.id=c++;e.listenersDone=0;if(e.nice===undefined||e.nice===null){e.nice=t.__ngev.nice}if(!e.listeners){e.listeners=t.__ngev.listeners[e.name].slice()}s.recursions++;e.depth=t.__ngev.depth++;u=[];try{for(n=0,i=e.listeners.length;n<i;n++){r++;o.emitToOneListener(e,e.listeners[n],u)}}catch(e){s.recursions--;t.__ngev.depth--;throw e}s.recursions--;if(!e.callback){t.__ngev.depth--}if(u.length&&t.__ngev.listeners.removeListener.length){t.emit("removeListener",u)}if(!r){if(e.name==="error"){if(e.args[0]){throw e.args[0]}else{throw Error("Uncaught, unspecified 'error' event.")}}if(e.callback){o.emitCallback(e)}}e.sync=false;return e};o.emitIntricatedEvents=function(e,t){var n,i,r=0,a;if(!Array.isArray(e)){throw new TypeError(".emitCorrelatedEvents() argument should be an array")}var u=[],f={nice:o.DESYNC,ready:true,status:o.CONTEXT_ENABLED,serial:true,scopes:{}},l={sync:false,nice:o.DESYNC,context:f,interrupt:null,listeners:u,listenersDone:0,depth:0,callback:t};e.forEach(e=>{var t=o.createEvent(...e);t.master=l;if(!t.emitter.__ngev){o.init.call(t.emitter)}if(!t.emitter.__ngev.listeners[t.name]){t.emitter.__ngev.listeners[t.name]=[]}t.listeners=t.emitter.__ngev.listeners[t.name].slice();t.id=c++;t.listeners.forEach(e=>u.push({event:t,listener:e}))});u.sort((e,t)=>t.listener.priority-e.listener.priority);s.recursions++;a=[];try{for(n=0,i=u.length;n<i;n++){r++;o.emitToOneListener(u[n].event,u[n].listener,a)}}catch(e){s.recursions--;throw e}s.recursions--;if(!r&&l.callback){o.emitCallback(event)}l.sync=false};o.emitToOneListener=function(e,t,n){var i=e.emitter,r=e.master||e,s=e.master?e.master.context:t.context,c,a,u,f=false;if(s){if(s.status===o.CONTEXT_DISABLED){return}u=Math.max(r.nice,t.nice,s.nice);a=s.serial;c=o.getContextScope(s,r.depth)}else{u=Math.max(r.nice,t.nice)}if(t.once&&i.__ngev.listeners[e.name]){i.__ngev.listeners[e.name]=i.__ngev.listeners[e.name].filter(o.filterOutCallback.bind(undefined,t));if(n){n.push(t)}else{f=true}}if(s&&(s.status===o.CONTEXT_QUEUED||!c.ready)){c.queue.push({event:e,listener:t,nice:u})}else{o.listenerWrapper(t,e,c,a,u)}if(f&&i.__ngev.listeners.removeListener.length){i.emit("removeListener",[t])}};o.emitCallback=function(e){var t;if(e.master){t=e.master.callback;delete e.master.callback;if(e.master.sync){r(()=>t(e.master.interrupt,e))}else{t(e.master.interrupt,e)}return}t=e.callback;delete e.callback;if(e.sync&&e.emitter.__ngev.nice!==o.SYNC){e.emitter.__ngev.desync(()=>{e.emitter.__ngev.depth--;t(e.interrupt,e)})}else{e.emitter.__ngev.depth--;t(e.interrupt,e)}};o.prototype.listeners=function(e){if(!e||typeof e!=="string"){throw new TypeError(".listeners(): argument #0 should be a non-empty string")}if(!this.__ngev){o.init.call(this)}if(!this.__ngev.listeners[e]){this.__ngev.listeners[e]=[]}return this.__ngev.listeners[e].slice()};o.listenerCount=function(e,t){if(!e||!(e instanceof o)){throw new TypeError(".listenerCount(): argument #0 should be an instance of NextGenEvents")}return e.listenerCount(t)};o.prototype.listenerCount=function(e){if(!e||typeof e!=="string"){throw new TypeError(".listenerCount(): argument #1 should be a non-empty string")}if(!this.__ngev||!this.__ngev.listeners[e]){return 0}return this.__ngev.listeners[e].length};o.prototype.setNice=function(e){if(!this.__ngev){o.init.call(this)}this.__ngev.nice=Math.floor(+e||0)};o.prototype.desyncUseNextTick=function(e){if(!this.__ngev){o.init.call(this)}this.__ngev.desync=e?r:setImmediate};o.prototype.setInterruptible=function(e){if(!this.__ngev){o.init.call(this)}this.__ngev.interruptible=!!e};o.prototype.setListenerPriority=function(e){if(!this.__ngev){o.init.call(this)}this.__ngev.hasListenerPriority=!!e};o.share=function(e,t){if(!(e instanceof o)||!(t instanceof o)){throw new TypeError("NextGenEvents.share() arguments should be instances of NextGenEvents")}if(!e.__ngev){o.init.call(e)}Object.defineProperty(t,"__ngev",{configurable:true,value:e.__ngev})};o.reset=function(e){Object.defineProperty(e,"__ngev",{configurable:true,value:null})};o.prototype.getMaxListeners=function(){if(!this.__ngev){o.init.call(this)}return this.__ngev.maxListeners};o.prototype.setMaxListeners=function(e){if(!this.__ngev){o.init.call(this)}this.__ngev.maxListeners=typeof e==="number"&&!Number.isNaN(e)?Math.floor(e):o.defaultMaxListeners;return this};o.noop=(()=>undefined);o.prototype.defineStates=function(...e){if(!this.__ngev){o.init.call(this)}e.forEach(t=>{this.__ngev.states[t]=null;this.__ngev.stateGroups[t]=e})};o.prototype.hasState=function(e){if(!this.__ngev){o.init.call(this)}return!!this.__ngev.states[e]};o.prototype.getAllStates=function(){if(!this.__ngev){o.init.call(this)}return Object.keys(this.__ngev.states).filter(e=>this.__ngev.states[e])};o.groupAddListener=function(e,t,n,i){if(typeof n!=="function"){i=n;n=undefined}if(!i||typeof i!=="object"){i={}}n=n||i.fn;delete i.fn;i.id=i.id||n;e.forEach(e=>{e.addListener(t,n.bind(undefined,e),i)})};o.groupOn=o.groupAddListener;o.groupOnce=function(e,t,n,i){if(n&&typeof n==="object"){n.once=true}else if(i&&typeof i==="object"){i.once=true}else{i={once:true}}return this.groupAddListener(e,t,n,i)};o.groupWaitFor=function(e,t){return Promise.all(e.map(e=>e.waitFor(t)))};o.groupWaitForAll=function(e,t){return Promise.all(e.map(e=>e.waitForAll(t)))};o.groupOnceFirst=function(e,t,n,i){var r,s=false;if(typeof n!=="function"){i=n;n=undefined}if(!i||typeof i!=="object"){i={}}n=n||i.fn;delete i.fn;i.id=i.id||n;r=((...r)=>{if(s){return}s=true;o.groupRemoveListener(e,t,i.id);n(...r)});e.forEach(e=>{e.once(t,r.bind(undefined,e),i)})};o.groupWaitForFirst=function(e,t){return new Promise(n=>{o.groupOnceFirst(e,t,e=>n(e))})};o.groupWaitForFirstAll=function(e,t){return new Promise(n=>{o.groupOnceFirst(e,t,(...e)=>n(e))})};o.groupOnceLast=function(e,t,n,i){var r,s=false,o=e.length;if(typeof n!=="function"){i=n;n=undefined}if(!i||typeof i!=="object"){i={}}n=n||i.fn;delete i.fn;i.id=i.id||n;r=((...e)=>{if(s){return}if(--o){return}s=true;n(...e)});e.forEach(e=>{e.once(t,r.bind(undefined,e),i)})};o.groupWaitForLast=function(e,t){return new Promise(n=>{o.groupOnceLast(e,t,e=>n(e))})};o.groupWaitForLastAll=function(e,t){return new Promise(n=>{o.groupOnceLast(e,t,(...e)=>n(e))})};o.groupRemoveListener=function(e,t,n){e.forEach(e=>{e.removeListener(t,n)})};o.groupOff=o.groupRemoveListener;o.groupRemoveAllListeners=function(e,t){e.forEach(e=>{e.removeAllListeners(t)})};o.groupEmit=function(e,...t){var n,i,r=1,s,c=e.length,a,u,f=false;if(typeof t[t.length-1]==="function"){s=-1;a=t[t.length-1];u=(e=>{if(f){return}if(e){f=true;a(e)}else if(!--c){f=true;a()}})}if(typeof t[0]==="number"){r=2;i=typeof t[0]}n=t[r-1];t=t.slice(r,s);e.forEach(e=>{o.emitEvent({emitter:e,name:n,args:t,nice:i,callback:u})})};o.groupWaitForEmit=function(e,...t){return new Promise(n=>{o.groupEmit(e,...t,e=>n(e))})};o.groupDefineStates=function(e,...t){e.forEach(e=>{e.defineStates(...t)})};o.groupGlobalOnce=o.groupOnceFirst;o.groupGlobalOnceAll=o.groupOnceLast;o.CONTEXT_ENABLED=0;o.CONTEXT_DISABLED=1;o.CONTEXT_QUEUED=2;o.prototype.addListenerContext=function(e,t){if(!this.__ngev){o.init.call(this)}if(!e||typeof e!=="string"){throw new TypeError(".addListenerContext(): argument #0 should be a non-empty string")}if(!t||typeof t!=="object"){t={}}var n=this.__ngev.contexts[e];if(!n){n=this.__ngev.contexts[e]={nice:o.SYNC,ready:true,status:o.CONTEXT_ENABLED,serial:false,scopes:{}}}if(t.nice!==undefined){n.nice=Math.floor(t.nice)}if(t.status!==undefined){n.status=t.status}if(t.serial!==undefined){n.serial=!!t.serial}return n};o.prototype.getListenerContext=function(e){return this.__ngev.contexts[e]};o.getContextScope=function(e,t){var n=e.scopes[t];if(!n){n=e.scopes[t]={ready:true,queue:[]}}return n};o.prototype.disableListenerContext=function(e){if(!this.__ngev){o.init.call(this)}if(!e||typeof e!=="string"){throw new TypeError(".disableListenerContext(): argument #0 should be a non-empty string")}if(!this.__ngev.contexts[e]){this.addListenerContext(e)}this.__ngev.contexts[e].status=o.CONTEXT_DISABLED;return this};o.prototype.enableListenerContext=function(e){if(!this.__ngev){o.init.call(this)}if(!e||typeof e!=="string"){throw new TypeError(".enableListenerContext(): argument #0 should be a non-empty string")}if(!this.__ngev.contexts[e]){this.addListenerContext(e)}var t=this.__ngev.contexts[e];t.status=o.CONTEXT_ENABLED;Object.values(t.scopes).forEach(e=>{if(e.queue.length>0){o.processScopeQueue(e,t.serial)}});return this};o.prototype.queueListenerContext=function(e){if(!this.__ngev){o.init.call(this)}if(!e||typeof e!=="string"){throw new TypeError(".queueListenerContext(): argument #0 should be a non-empty string")}if(!this.__ngev.contexts[e]){this.addListenerContext(e)}this.__ngev.contexts[e].status=o.CONTEXT_QUEUED;return this};o.prototype.serializeListenerContext=function(e,t){if(!this.__ngev){o.init.call(this)}if(!e||typeof e!=="string"){throw new TypeError(".serializeListenerContext(): argument #0 should be a non-empty string")}if(!this.__ngev.contexts[e]){this.addListenerContext(e)}this.__ngev.contexts[e].serial=t===undefined?true:!!t;return this};o.prototype.setListenerContextNice=function(e,t){if(!this.__ngev){o.init.call(this)}if(!e||typeof e!=="string"){throw new TypeError(".setListenerContextNice(): argument #0 should be a non-empty string")}if(!this.__ngev.contexts[e]){this.addListenerContext(e)}this.__ngev.contexts[e].nice=Math.floor(t);return this};o.prototype.destroyListenerContext=function(e){var t,n,i,r,s,c=[];if(!e||typeof e!=="string"){throw new TypeError(".disableListenerContext(): argument #0 should be a non-empty string")}if(!this.__ngev){o.init.call(this)}i=this.__ngev.contexts[e];if(!i){return}for(r in this.__ngev.listeners){s=null;n=this.__ngev.listeners[r].length;for(t=0;t<n;t++){if(this.__ngev.listeners[r][t].context===i){s=[];c.push(this.__ngev.listeners[r][t])}else if(s){s.push(this.__ngev.listeners[r][t])}}if(s){this.__ngev.listeners[r]=s}}delete this.__ngev.contexts[e];if(c.length&&this.__ngev.listeners.removeListener.length){this.emit("removeListener",c)}return this};o.processScopeQueue=function(e,t,n){var i,r,c,a;if(n){e.ready=true}s.recursions++;while(e.ready&&e.queue.length){i=e.queue.shift();r=i.event;c=r.master||r;a=r.emitter;if(c.interrupt){continue}o.listenerWrapper(i.listener,r,e,t,i.nice)}s.recursions--};o.on=o.prototype.on;o.once=o.prototype.once;o.off=o.prototype.off;if(i.AsyncTryCatch){o.prototype.asyncTryCatchId=i.AsyncTryCatch.NextGenEvents.length;i.AsyncTryCatch.NextGenEvents.push(o);if(i.AsyncTryCatch.substituted){i.AsyncTryCatch.substitute()}}o.Proxy=e("./Proxy.js")}).call(this,e("_process"),typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"../package.json":5,"./Proxy.js":2,_process:4}],2:[function(e,t,n){"use strict";function i(){this.localServices={};this.remoteServices={};this.nextAckId=1}t.exports=i;var r=e("./NextGenEvents.js");var s="NextGenEvents/message";function o(){}i.create=((...e)=>new i(...e));i.prototype.addLocalService=function(e,t,n){this.localServices[e]=c.create(this,e,t,n);return this.localServices[e]};i.prototype.addRemoteService=function(e){this.remoteServices[e]=a.create(this,e);return this.remoteServices[e]};i.prototype.destroy=function(){Object.keys(this.localServices).forEach(e=>{this.localServices[e].destroy();delete this.localServices[e]});Object.keys(this.remoteServices).forEach(e=>{this.remoteServices[e].destroy();delete this.remoteServices[e]});this.receive=this.send=o};i.prototype.push=function(e){if(e.__type!==s||!e.service||typeof e.service!=="string"||!e.event||typeof e.event!=="string"||!e.method){return}switch(e.method){case"event":return this.remoteServices[e.service]&&this.remoteServices[e.service].receiveEvent(e);case"ackEmit":return this.remoteServices[e.service]&&this.remoteServices[e.service].receiveAckEmit(e);case"emit":return this.localServices[e.service]&&this.localServices[e.service].receiveEmit(e);case"listen":return this.localServices[e.service]&&this.localServices[e.service].receiveListen(e);case"ignore":return this.localServices[e.service]&&this.localServices[e.service].receiveIgnore(e);case"ackEvent":return this.localServices[e.service]&&this.localServices[e.service].receiveAckEvent(e);default:return}};i.prototype.receive=function(e){this.push(e)};i.prototype.send=function(){throw new Error("The send() method of the Proxy MUST be extended/overwritten")};function c(e,t,n,i){return c.create(e,t,n,i)}i.LocalService=c;c.create=function(e,t,n,i){var s=Object.create(c.prototype,{proxy:{value:e,enumerable:true},id:{value:t,enumerable:true},emitter:{value:n,writable:true,enumerable:true},internalEvents:{value:Object.create(r.prototype),writable:true,enumerable:true},events:{value:{},enumerable:true},canListen:{value:!!i.listen,writable:true,enumerable:true},canEmit:{value:!!i.emit,writable:true,enumerable:true},canAck:{value:!!i.ack,writable:true,enumerable:true},canRpc:{value:!!i.rpc,writable:true,enumerable:true},destroyed:{value:false,writable:true,enumerable:true}});return s};c.prototype.destroy=function(){Object.keys(this.events).forEach(e=>{this.emitter.off(e,this.events[e]);delete this.events[e]});this.emitter=null;this.destroyed=true};c.prototype.receiveEmit=function(e){if(this.destroyed||!this.canEmit||e.ack&&!this.canAck){return}var t={emitter:this.emitter,name:e.event,args:e.args||[]};if(e.ack){t.callback=(t=>{this.proxy.send({__type:s,service:this.id,method:"ackEmit",ack:e.ack,event:e.event,interruption:t})})}r.emitEvent(t)};c.prototype.receiveListen=function(e){if(this.destroyed||!this.canListen||e.ack&&!this.canAck){return}if(e.ack){if(this.events[e.event]){if(this.events[e.event].ack){return}this.emitter.off(e.event,this.events[e.event])}this.events[e.event]=c.forwardWithAck.bind(this);this.events[e.event].ack=true;this.emitter.on(e.event,this.events[e.event],{eventObject:true,async:true})}else{if(this.events[e.event]){if(!this.events[e.event].ack){return}this.emitter.off(e.event,this.events[e.event])}this.events[e.event]=c.forward.bind(this);this.events[e.event].ack=false;this.emitter.on(e.event,this.events[e.event],{eventObject:true})}};c.prototype.receiveIgnore=function(e){if(this.destroyed||!this.canListen){return}if(!this.events[e.event]){return}this.emitter.off(e.event,this.events[e.event]);this.events[e.event]=null};c.prototype.receiveAckEvent=function(e){if(this.destroyed||!this.canListen||!this.canAck||!e.ack||!this.events[e.event]||!this.events[e.event].ack){return}this.internalEvents.emit("ack",e)};c.forward=function(e){if(this.destroyed){return}this.proxy.send({__type:s,service:this.id,method:"event",event:e.name,args:e.args})};c.forward.ack=false;c.forwardWithAck=function(e,t){if(this.destroyed){return}if(!e.callback){this.proxy.send({__type:s,service:this.id,method:"event",event:e.name,args:e.args});t();return}var n=false;var i=this.proxy.nextAckId++;var r=e=>{if(n||e.ack!==i){return}n=true;this.internalEvents.off("ack",r);t()};this.internalEvents.on("ack",r);this.proxy.send({__type:s,service:this.id,method:"event",event:e.name,ack:i,args:e.args})};c.forwardWithAck.ack=true;function a(e,t){return a.create(e,t)}i.RemoteService=a;var u=1;var f=2;a.create=function(e,t){var n=Object.create(a.prototype,{proxy:{value:e,enumerable:true},id:{value:t,enumerable:true},emitter:{value:Object.create(r.prototype),writable:true,enumerable:true},internalEvents:{value:Object.create(r.prototype),writable:true,enumerable:true},events:{value:{},enumerable:true},destroyed:{value:false,writable:true,enumerable:true}});return n};a.prototype.destroy=function(){this.emitter.removeAllListeners();this.emitter=null;Object.keys(this.events).forEach(e=>{delete this.events[e]});this.destroyed=true};a.prototype.emit=function(e,...t){if(this.destroyed){return}var n,i,r;if(typeof e==="number"){throw new TypeError("Cannot emit with a nice value on a remote service")}if(typeof t[t.length-1]!=="function"){this.proxy.send({__type:s,service:this.id,method:"emit",event:e,args:t});return}n=t.pop();i=this.proxy.nextAckId++;r=false;var o=e=>{if(r||e.ack!==i){return}r=true;this.internalEvents.off("ack",o);n(e.interruption)};this.internalEvents.on("ack",o);this.proxy.send({__type:s,service:this.id,method:"emit",ack:i,event:e,args:t})};a.prototype.addListener=function(e,t,n){if(this.destroyed){return}if(typeof t!=="function"){n=t;t=undefined}if(!n||typeof n!=="object"){n={}}n.fn=t||n.fn;this.emitter.addListener(e,n);if(!this.emitter.__ngev.listeners[e]||!this.emitter.__ngev.listeners[e].length){return}if(n.async&&this.events[e]!==f){this.events[e]=f;this.proxy.send({__type:s,service:this.id,method:"listen",ack:true,event:e})}else if(!n.async&&!this.events[e]){this.events[e]=u;this.proxy.send({__type:s,service:this.id,method:"listen",event:e})}};a.prototype.on=a.prototype.addListener;a.prototype.once=r.prototype.once;a.prototype.removeListener=function(e,t){if(this.destroyed){return}this.emitter.removeListener(e,t);if((!this.emitter.__ngev.listeners[e]||!this.emitter.__ngev.listeners[e].length)&&this.events[e]){this.events[e]=0;this.proxy.send({__type:s,service:this.id,method:"ignore",event:e})}};a.prototype.off=a.prototype.removeListener;a.prototype.receiveEvent=function(e){if(this.destroyed||!this.events[e.event]){return}var t={emitter:this.emitter,name:e.event,args:e.args||[]};if(e.ack){t.callback=(()=>{this.proxy.send({__type:s,service:this.id,method:"ackEvent",ack:e.ack,event:e.event})})}r.emitEvent(t);var n=t.name;if(!this.emitter.__ngev.listeners[n]||!this.emitter.__ngev.listeners[n].length){this.events[n]=0;this.proxy.send({__type:s,service:this.id,method:"ignore",event:n})}};a.prototype.receiveAckEmit=function(e){if(this.destroyed||!e.ack||this.events[e.event]!==f){return}this.internalEvents.emit("ack",e)}},{"./NextGenEvents.js":1}],3:[function(e,t,n){(function(n){"use strict";if(typeof window.setImmediate!=="function"){window.setImmediate=(e=>setTimeout(e,0))}if(!n.emitWarning){n.emitWarning=(()=>undefined)}t.exports=e("./NextGenEvents.js");t.exports.isBrowser=true}).call(this,e("_process"))},{"./NextGenEvents.js":1,_process:4}],4:[function(e,t,n){var i=t.exports={};var r;var s;function o(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}(function(){try{if(typeof setTimeout==="function"){r=setTimeout}else{r=o}}catch(e){r=o}try{if(typeof clearTimeout==="function"){s=clearTimeout}else{s=c}}catch(e){s=c}})();function a(e){if(r===setTimeout){return setTimeout(e,0)}if((r===o||!r)&&setTimeout){r=setTimeout;return setTimeout(e,0)}try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function u(e){if(s===clearTimeout){return clearTimeout(e)}if((s===c||!s)&&clearTimeout){s=clearTimeout;return clearTimeout(e)}try{return s(e)}catch(t){try{return s.call(null,e)}catch(t){return s.call(this,e)}}}var f=[];var l=false;var h;var v=-1;function p(){if(!l||!h){return}l=false;if(h.length){f=h.concat(f)}else{v=-1}if(f.length){d()}}function d(){if(l){return}var e=a(p);l=true;var t=f.length;while(t){h=f;f=[];while(++v<t){if(h){h[v].run()}}v=-1;t=f.length}h=null;l=false;u(e)}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var n=1;n<arguments.length;n++){t[n-1]=arguments[n]}}f.push(new _(e,t));if(f.length===1&&!l){a(d)}};function _(e,t){this.fun=e;this.array=t}_.prototype.run=function(){this.fun.apply(null,this.array)};i.title="browser";i.browser=true;i.env={};i.argv=[];i.version="";i.versions={};function g(){}i.on=g;i.addListener=g;i.once=g;i.off=g;i.removeListener=g;i.removeAllListeners=g;i.emit=g;i.prependListener=g;i.prependOnceListener=g;i.listeners=function(e){return[]};i.binding=function(e){throw new Error("process.binding is not supported")};i.cwd=function(){return"/"};i.chdir=function(e){throw new Error("process.chdir is not supported")};i.umask=function(){return 0}},{}],5:[function(e,t,n){t.exports={name:"nextgen-events",version:"1.5.2",description:"The next generation of events handling for javascript! New: abstract away the network!",main:"lib/NextGenEvents.js",engines:{node:">=6.0.0"},directories:{test:"test",bench:"bench"},dependencies:{},devDependencies:{browserify:"^17.0.0","uglify-js-es6":"^2.8.9",ws:"^7.4.6"},scripts:{test:"tea-time -R dot"},repository:{type:"git",url:"https://github.com/cronvel/nextgen-events.git"},keywords:["events","async","emit","listener","context","series","serialize","namespace","proxy","network"],author:"Cédric Ronvel",license:"MIT",bugs:{url:"https://github.com/cronvel/nextgen-events/issues"},config:{"tea-time":{coverDir:["lib"]}},copyright:{title:"Next-Gen Events",years:[2015,2021],owner:"Cédric Ronvel"}}},{}]},{},[3])(3)});