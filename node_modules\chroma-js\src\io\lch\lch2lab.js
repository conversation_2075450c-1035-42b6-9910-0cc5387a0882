import { unpack, DEG2RAD } from '../../utils/index.js';
const { sin, cos } = Math;

const lch2lab = (...args) => {
    /*
    Convert from a qualitative parameter h and a quantitative parameter l to a 24-bit pixel.
    These formulas were invented by <PERSON> to obtain maximum contrast without going
    out of gamut if the parameters are in the range 0-1.

    A saturation multiplier was added by <PERSON>
    */
    let [l, c, h] = unpack(args, 'lch');
    if (isNaN(h)) h = 0;
    h = h * DEG2RAD;
    return [l, cos(h) * c, sin(h) * c];
};

export default lch2lab;
