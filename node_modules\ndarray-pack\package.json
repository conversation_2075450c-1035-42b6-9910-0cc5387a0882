{"name": "ndarray-pack", "version": "1.2.1", "description": "Packs an array-of-arrays into a single ndarray", "main": "convert.js", "directories": {"test": "test"}, "dependencies": {"cwise-compiler": "^1.1.2", "ndarray": "^1.0.13"}, "devDependencies": {"tap": "~0.4.2", "cwise-bake": "0.0.0"}, "scripts": {"test": "tap test/*.js", "build": "node build.js > doConvert.js"}, "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/ndarray-pack.git"}, "keywords": ["n<PERSON><PERSON>", "numeric", "array", "nested", "pack", "typedarray", "memory"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "readmeFilename": "README.md", "gitHead": "b6a147c9031aefb453d32efad21e7055993e38ff"}