"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileTool = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const glob_1 = require("glob");
const nanoid_1 = require("nanoid");
const logger_1 = require("../utils/logger");
const crypto_1 = require("crypto");
class FileTool {
    maxFileSize = 50 * 1024 * 1024; // 50MB default
    backupDir = '.kritrima-backups';
    async read(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const encoding = args.encoding || 'utf-8';
            const lines = args.lines;
            const offset = args.offset;
            if (!filePath) {
                throw new Error('File path is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Security check - prevent reading outside working directory
            if (!this.isPathSafe(resolvedPath)) {
                throw new Error('Access denied: Path is outside allowed directory');
            }
            // Check if file exists
            await promises_1.default.access(resolvedPath);
            // Get file stats
            const stats = await promises_1.default.stat(resolvedPath);
            if (stats.isDirectory()) {
                throw new Error('Path is a directory, not a file');
            }
            // Check file size
            if (stats.size > this.maxFileSize) {
                throw new Error(`File too large (${this.formatBytes(stats.size)}). Maximum allowed: ${this.formatBytes(this.maxFileSize)}`);
            }
            let content;
            // Handle partial reading if lines or offset specified
            if (lines !== undefined || offset !== undefined) {
                content = await this.readFilePartial(resolvedPath, encoding, lines, offset);
            }
            else {
                content = await promises_1.default.readFile(resolvedPath, encoding);
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: content,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    size: stats.size,
                    encoding,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async write(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const content = args.content;
            const encoding = args.encoding || 'utf-8';
            const backup = args.backup || false;
            if (!filePath) {
                throw new Error('File path is required');
            }
            if (content === undefined) {
                throw new Error('Content is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Create backup if requested and file exists
            if (backup) {
                try {
                    await promises_1.default.access(resolvedPath);
                    const backupPath = `${resolvedPath}.backup.${Date.now()}`;
                    await promises_1.default.copyFile(resolvedPath, backupPath);
                    logger_1.logger.debug('Backup created', { original: resolvedPath, backup: backupPath });
                }
                catch (error) {
                    // File doesn't exist, no backup needed
                }
            }
            // Ensure directory exists
            const dir = path_1.default.dirname(resolvedPath);
            await promises_1.default.mkdir(dir, { recursive: true });
            // Write file
            await promises_1.default.writeFile(resolvedPath, content, encoding);
            const stats = await promises_1.default.stat(resolvedPath);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `File written successfully: ${resolvedPath} (${stats.size} bytes)`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    size: stats.size,
                    encoding,
                    backup,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async create(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const content = args.content || '';
            const encoding = args.encoding || 'utf-8';
            if (!filePath) {
                throw new Error('File path is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Check if file already exists
            try {
                await promises_1.default.access(resolvedPath);
                throw new Error('File already exists');
            }
            catch (error) {
                // File doesn't exist, which is what we want
                if (error.code !== 'ENOENT') {
                    throw error;
                }
            }
            // Ensure directory exists
            const dir = path_1.default.dirname(resolvedPath);
            await promises_1.default.mkdir(dir, { recursive: true });
            // Create file
            await promises_1.default.writeFile(resolvedPath, content, encoding);
            const stats = await promises_1.default.stat(resolvedPath);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `File created successfully: ${resolvedPath} (${stats.size} bytes)`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    size: stats.size,
                    encoding,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async delete(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const filePath = args.path;
            const recursive = args.recursive || false;
            const force = args.force || false;
            if (!filePath) {
                throw new Error('File path is required');
            }
            const resolvedPath = path_1.default.resolve(filePath);
            // Check if path exists
            await promises_1.default.access(resolvedPath);
            const stats = await promises_1.default.stat(resolvedPath);
            if (stats.isDirectory()) {
                if (!recursive) {
                    throw new Error('Path is a directory. Use recursive=true to delete directories.');
                }
                await promises_1.default.rm(resolvedPath, { recursive: true, force });
            }
            else {
                await promises_1.default.unlink(resolvedPath);
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `${stats.isDirectory() ? 'Directory' : 'File'} deleted successfully: ${resolvedPath}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    type: stats.isDirectory() ? 'directory' : 'file',
                    recursive,
                    force,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async move(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const source = args.source;
            const destination = args.destination;
            if (!source || !destination) {
                throw new Error('Source and destination paths are required');
            }
            const resolvedSource = path_1.default.resolve(source);
            const resolvedDestination = path_1.default.resolve(destination);
            // Check if source exists
            await promises_1.default.access(resolvedSource);
            // Ensure destination directory exists
            const destDir = path_1.default.dirname(resolvedDestination);
            await promises_1.default.mkdir(destDir, { recursive: true });
            // Move file/directory
            await promises_1.default.rename(resolvedSource, resolvedDestination);
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `Moved successfully: ${resolvedSource} → ${resolvedDestination}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    source: resolvedSource,
                    destination: resolvedDestination,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async copy(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const source = args.source;
            const destination = args.destination;
            const recursive = args.recursive || false;
            if (!source || !destination) {
                throw new Error('Source and destination paths are required');
            }
            const resolvedSource = path_1.default.resolve(source);
            const resolvedDestination = path_1.default.resolve(destination);
            // Check if source exists
            await promises_1.default.access(resolvedSource);
            const stats = await promises_1.default.stat(resolvedSource);
            // Ensure destination directory exists
            const destDir = path_1.default.dirname(resolvedDestination);
            await promises_1.default.mkdir(destDir, { recursive: true });
            if (stats.isDirectory()) {
                if (!recursive) {
                    throw new Error('Source is a directory. Use recursive=true to copy directories.');
                }
                await this.copyDirectory(resolvedSource, resolvedDestination);
            }
            else {
                await promises_1.default.copyFile(resolvedSource, resolvedDestination);
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: `Copied successfully: ${resolvedSource} → ${resolvedDestination}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    source: resolvedSource,
                    destination: resolvedDestination,
                    type: stats.isDirectory() ? 'directory' : 'file',
                    recursive,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async search(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const pattern = args.pattern;
            const cwd = args.cwd || process.cwd();
            const maxDepth = args.maxDepth || 10;
            if (!pattern) {
                throw new Error('Search pattern is required');
            }
            const files = await (0, glob_1.glob)(pattern, {
                cwd: path_1.default.resolve(cwd),
                maxDepth,
                dot: false,
            });
            const results = files.slice(0, 100); // Limit results for performance
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: results.length > 0
                    ? `Found ${results.length} files:\n${results.join('\n')}`
                    : 'No files found matching the pattern',
                executionTime: Date.now() - startTime,
                metadata: {
                    pattern,
                    cwd,
                    maxDepth,
                    resultCount: results.length,
                    results,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async grep(args) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const pattern = args.pattern;
            const files = args.files;
            const recursive = args.recursive || false;
            const ignoreCase = args.ignoreCase || false;
            const lineNumbers = args.lineNumbers || true;
            if (!pattern) {
                throw new Error('Search pattern is required');
            }
            if (!files || files.length === 0) {
                throw new Error('Files to search are required');
            }
            const regex = new RegExp(pattern, ignoreCase ? 'gi' : 'g');
            const results = [];
            for (const file of files) {
                try {
                    const resolvedPath = path_1.default.resolve(file);
                    const stats = await promises_1.default.stat(resolvedPath);
                    if (stats.isDirectory() && recursive) {
                        // Search in directory recursively
                        const dirFiles = await (0, glob_1.glob)('**/*', {
                            cwd: resolvedPath,
                            nodir: true,
                            maxDepth: 5,
                        });
                        for (const dirFile of dirFiles) {
                            const fullPath = path_1.default.join(resolvedPath, dirFile);
                            const matches = await this.searchInFile(fullPath, regex, lineNumbers);
                            if (matches.length > 0) {
                                results.push(`${fullPath}:\n${matches.join('\n')}`);
                            }
                        }
                    }
                    else if (stats.isFile()) {
                        const matches = await this.searchInFile(resolvedPath, regex, lineNumbers);
                        if (matches.length > 0) {
                            results.push(`${resolvedPath}:\n${matches.join('\n')}`);
                        }
                    }
                }
                catch (error) {
                    // Skip files that can't be read
                    logger_1.logger.debug('Skipping file in grep', { file, error });
                }
            }
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: true,
                output: results.length > 0
                    ? results.join('\n\n')
                    : 'No matches found',
                executionTime: Date.now() - startTime,
                metadata: {
                    pattern,
                    files,
                    recursive,
                    ignoreCase,
                    lineNumbers,
                    matchCount: results.length,
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: args.toolCallId || '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async copyDirectory(source, destination) {
        await promises_1.default.mkdir(destination, { recursive: true });
        const entries = await promises_1.default.readdir(source, { withFileTypes: true });
        for (const entry of entries) {
            const srcPath = path_1.default.join(source, entry.name);
            const destPath = path_1.default.join(destination, entry.name);
            if (entry.isDirectory()) {
                await this.copyDirectory(srcPath, destPath);
            }
            else {
                await promises_1.default.copyFile(srcPath, destPath);
            }
        }
    }
    async searchInFile(filePath, regex, lineNumbers) {
        try {
            const content = await promises_1.default.readFile(filePath, 'utf-8');
            const lines = content.split('\n');
            const matches = [];
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                if (regex.test(line)) {
                    const match = lineNumbers
                        ? `${i + 1}: ${line}`
                        : line;
                    matches.push(match);
                }
            }
            return matches;
        }
        catch (error) {
            return [];
        }
    }
    // Enhanced utility methods
    async readFilePartial(filePath, encoding, lines, offset) {
        const content = await promises_1.default.readFile(filePath, encoding);
        const allLines = content.split('\n');
        if (offset !== undefined) {
            const startLine = Math.max(0, offset);
            const endLine = lines !== undefined ? startLine + lines : allLines.length;
            return allLines.slice(startLine, endLine).join('\n');
        }
        if (lines !== undefined) {
            return allLines.slice(0, lines).join('\n');
        }
        return content;
    }
    isPathSafe(filePath) {
        const cwd = process.cwd();
        const resolved = path_1.default.resolve(filePath);
        return resolved.startsWith(cwd);
    }
    formatBytes(bytes) {
        if (bytes === 0)
            return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    // File operations with enhanced features
    async createBackup(filePath) {
        const resolvedPath = path_1.default.resolve(filePath);
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path_1.default.join(path_1.default.dirname(resolvedPath), this.backupDir, `${path_1.default.basename(resolvedPath)}.${timestamp}.backup`);
        await promises_1.default.mkdir(path_1.default.dirname(backupPath), { recursive: true });
        await promises_1.default.copyFile(resolvedPath, backupPath);
        return backupPath;
    }
    async getFileInfo(filePath) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const resolvedPath = path_1.default.resolve(filePath);
            const stats = await promises_1.default.stat(resolvedPath);
            const info = {
                path: resolvedPath,
                size: stats.size,
                sizeFormatted: this.formatBytes(stats.size),
                type: stats.isDirectory() ? 'directory' : 'file',
                permissions: stats.mode.toString(8),
                created: stats.birthtime,
                modified: stats.mtime,
                accessed: stats.atime,
            };
            if (stats.isFile()) {
                // Calculate file hash
                const content = await promises_1.default.readFile(resolvedPath);
                const hash = (0, crypto_1.createHash)('md5').update(content).digest('hex');
                info.hash = hash;
                // Count lines for text files
                if (this.isTextFile(resolvedPath)) {
                    const textContent = await promises_1.default.readFile(resolvedPath, 'utf-8');
                    info.lines = textContent.split('\n').length;
                }
            }
            return {
                id: resultId,
                toolCallId: '',
                success: true,
                output: JSON.stringify(info, null, 2),
                executionTime: Date.now() - startTime,
                metadata: info,
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    isTextFile(filePath) {
        const textExtensions = ['.txt', '.md', '.js', '.ts', '.json', '.yaml', '.yml', '.xml', '.html', '.css', '.py', '.java', '.cpp', '.c', '.h'];
        const ext = path_1.default.extname(filePath).toLowerCase();
        return textExtensions.includes(ext);
    }
    async listDirectory(dirPath, options = {}) {
        const startTime = Date.now();
        const resultId = (0, nanoid_1.nanoid)();
        try {
            const resolvedPath = path_1.default.resolve(dirPath);
            const stats = await promises_1.default.stat(resolvedPath);
            if (!stats.isDirectory()) {
                throw new Error('Path is not a directory');
            }
            const entries = await this.getDirectoryEntries(resolvedPath, options);
            const output = entries.map(entry => {
                const type = entry.isDirectory ? 'DIR' : 'FILE';
                const size = entry.isDirectory ? '' : this.formatBytes(entry.size);
                const modified = entry.modified.toLocaleDateString();
                return `${type.padEnd(4)} ${size.padEnd(10)} ${modified.padEnd(12)} ${entry.name}`;
            }).join('\n');
            return {
                id: resultId,
                toolCallId: '',
                success: true,
                output: `Directory listing for ${resolvedPath}:\n\n${'TYPE'.padEnd(4)} ${'SIZE'.padEnd(10)} ${'MODIFIED'.padEnd(12)} NAME\n${'----'.padEnd(4)} ${'----'.padEnd(10)} ${'--------'.padEnd(12)} ----\n${output}`,
                executionTime: Date.now() - startTime,
                metadata: {
                    path: resolvedPath,
                    entryCount: entries.length,
                    entries: entries.slice(0, 50), // Limit metadata for performance
                },
            };
        }
        catch (error) {
            return {
                id: resultId,
                toolCallId: '',
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
        }
    }
    async getDirectoryEntries(dirPath, options) {
        const entries = await promises_1.default.readdir(dirPath, { withFileTypes: true });
        const results = [];
        for (const entry of entries) {
            if (!options.showHidden && entry.name.startsWith('.')) {
                continue;
            }
            const fullPath = path_1.default.join(dirPath, entry.name);
            const stats = await promises_1.default.stat(fullPath);
            results.push({
                name: entry.name,
                path: fullPath,
                isDirectory: entry.isDirectory(),
                size: stats.size,
                modified: stats.mtime,
            });
            if (options.recursive && entry.isDirectory()) {
                const subEntries = await this.getDirectoryEntries(fullPath, options);
                results.push(...subEntries.map(sub => ({
                    ...sub,
                    name: path_1.default.join(entry.name, sub.name),
                })));
            }
        }
        return results.sort((a, b) => {
            if (a.isDirectory && !b.isDirectory)
                return -1;
            if (!a.isDirectory && b.isDirectory)
                return 1;
            return a.name.localeCompare(b.name);
        });
    }
}
exports.FileTool = FileTool;
//# sourceMappingURL=files.js.map