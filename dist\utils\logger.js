"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupLogs = exports.logConfigChange = exports.logSessionActivity = exports.logToolExecution = exports.logAIInteraction = exports.auditLog = exports.logDebug = exports.logError = exports.PerformanceLogger = exports.loggers = exports.createLogger = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const os_1 = __importDefault(require("os"));
const fs_1 = __importDefault(require("fs"));
// Ensure log directory exists
const logDir = path_1.default.join(os_1.default.homedir(), '.kritrima', 'logs');
if (!fs_1.default.existsSync(logDir)) {
    fs_1.default.mkdirSync(logDir, { recursive: true });
}
// Custom format for console output
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'HH:mm:ss' }), winston_1.default.format.colorize(), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    let output = `${timestamp} [${level}] ${message}`;
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
        output += ` ${JSON.stringify(meta)}`;
    }
    return output;
}));
// Custom format for file output
const fileFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
// Create logger instance
exports.logger = winston_1.default.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: fileFormat,
    defaultMeta: { service: 'kritrima-cli' },
    transports: [
        // File transport for all logs
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'error.log'),
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        // File transport for combined logs
        new winston_1.default.transports.File({
            filename: path_1.default.join(logDir, 'combined.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
    ],
});
// Add console transport in development or when explicitly requested
if (process.env.NODE_ENV !== 'production' || process.env.LOG_CONSOLE === 'true') {
    exports.logger.add(new winston_1.default.transports.Console({
        format: consoleFormat,
        level: process.env.LOG_LEVEL || 'info',
    }));
}
// Create child loggers for different components
const createLogger = (component) => {
    return exports.logger.child({ component });
};
exports.createLogger = createLogger;
// Utility functions for structured logging
exports.loggers = {
    agent: (0, exports.createLogger)('agent'),
    session: (0, exports.createLogger)('session'),
    context: (0, exports.createLogger)('context'),
    ai: (0, exports.createLogger)('ai'),
    tools: (0, exports.createLogger)('tools'),
    security: (0, exports.createLogger)('security'),
    cli: (0, exports.createLogger)('cli'),
    storage: (0, exports.createLogger)('storage'),
};
// Performance logging utility
class PerformanceLogger {
    startTime;
    operation;
    logger;
    constructor(operation, customLogger) {
        this.operation = operation;
        this.logger = customLogger || exports.logger;
        this.startTime = Date.now();
        this.logger.debug(`Starting operation: ${operation}`);
    }
    end(metadata) {
        const duration = Date.now() - this.startTime;
        this.logger.info(`Operation completed: ${this.operation}`, {
            duration,
            ...metadata,
        });
    }
    error(error, metadata) {
        const duration = Date.now() - this.startTime;
        this.logger.error(`Operation failed: ${this.operation}`, {
            duration,
            error: error.message,
            stack: error.stack,
            ...metadata,
        });
    }
}
exports.PerformanceLogger = PerformanceLogger;
// Error logging utility
const logError = (message, error, metadata) => {
    if (error instanceof Error) {
        exports.logger.error(message, {
            error: error.message,
            stack: error.stack,
            ...metadata,
        });
    }
    else {
        exports.logger.error(message, {
            error: String(error),
            ...metadata,
        });
    }
};
exports.logError = logError;
// Debug logging utility with conditional execution
const logDebug = (message, dataFn) => {
    if (exports.logger.isDebugEnabled()) {
        exports.logger.debug(message, dataFn());
    }
};
exports.logDebug = logDebug;
// Audit logging for security-sensitive operations
const auditLog = (action, user, resource, result, metadata) => {
    exports.logger.info('AUDIT', {
        action,
        user,
        resource,
        result,
        timestamp: new Date().toISOString(),
        ...metadata,
    });
};
exports.auditLog = auditLog;
// Request/Response logging for AI interactions
const logAIInteraction = (provider, model, tokens, duration, success, metadata) => {
    exports.logger.info('AI_INTERACTION', {
        provider,
        model,
        tokens,
        duration,
        success,
        timestamp: new Date().toISOString(),
        ...metadata,
    });
};
exports.logAIInteraction = logAIInteraction;
// Tool execution logging
const logToolExecution = (toolName, args, result, metadata) => {
    exports.logger.info('TOOL_EXECUTION', {
        tool: toolName,
        args,
        success: result.success,
        duration: result.duration,
        hasOutput: !!result.output,
        hasError: !!result.error,
        timestamp: new Date().toISOString(),
        ...metadata,
    });
};
exports.logToolExecution = logToolExecution;
// Session activity logging
const logSessionActivity = (sessionId, activity, metadata) => {
    exports.logger.info('SESSION_ACTIVITY', {
        sessionId,
        activity,
        timestamp: new Date().toISOString(),
        ...metadata,
    });
};
exports.logSessionActivity = logSessionActivity;
// Configuration change logging
const logConfigChange = (component, changes, metadata) => {
    exports.logger.info('CONFIG_CHANGE', {
        component,
        changes,
        timestamp: new Date().toISOString(),
        ...metadata,
    });
};
exports.logConfigChange = logConfigChange;
// Log cleanup utility
const cleanupLogs = async (olderThanDays = 30) => {
    try {
        const files = fs_1.default.readdirSync(logDir);
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
        for (const file of files) {
            const filePath = path_1.default.join(logDir, file);
            const stats = fs_1.default.statSync(filePath);
            if (stats.mtime < cutoffDate) {
                fs_1.default.unlinkSync(filePath);
                exports.logger.info('Log file cleaned up', { file, age: olderThanDays });
            }
        }
    }
    catch (error) {
        exports.logger.error('Failed to cleanup logs', { error });
    }
};
exports.cleanupLogs = cleanupLogs;
// Export default logger
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map