/**
 * chroma.js - JavaScript library for color conversions
 *
 * Copyright (c) 2011-2024, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * 3. The name <PERSON> may not be used to endorse or promote products
 * derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL GREGOR AISCH OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON> DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * -------------------------------------------------------
 *
 * chroma.js includes colors from colorbrewer2.org, which are released under
 * the following license:
 *
 * Copyright (c) 2002 Cynthia Brewer, Mark Harrower,
 * and The Pennsylvania State University.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific
 * language governing permissions and limitations under the License.
 *
 * ------------------------------------------------------
 *
 * Named colors are taken from X11 Color Names.
 * http://www.w3.org/TR/css3-color/#svg-color
 *
 * @preserve
 */

!function(t){"function"==typeof define&&define.amd?define(t):t()}((function(){"use strict";function t(t,r,n){return void 0===n&&(n=1),s(l(r,t),n)}for(var r={},n=0,e=["Boolean","Number","String","Function","Array","Date","RegExp","Undefined","Null"];n<e.length;n+=1){var o=e[n];r["[object "+o+"]"]=o.toLowerCase()}function a(t){return r[Object.prototype.toString.call(t)]||"object"}function i(t,r){return void 0===r&&(r=null),t.length>=3?Array.prototype.slice.call(t):"object"==a(t[0])&&r?r.split("").filter((function(r){return void 0!==t[0][r]})).map((function(r){return t[0][r]})):t[0]}function u(t){if(t.length<2)return null;var r=t.length-1;return"string"==a(t[r])?t[r].toLowerCase():null}var s=Math.min,l=Math.max,c={format:{},autodetect:[]},h=function(){for(var r=[],n=arguments.length;n--;)r[n]=arguments[n];var e=this;if("object"===a(r[0])&&r[0].constructor&&r[0].constructor===this.constructor)return r[0];var o=u(r),i=!1;if(!o){i=!0,c.sorted||(c.autodetect=c.autodetect.sort((function(t,r){return r.p-t.p})),c.sorted=!0);for(var s=0,l=c.autodetect;s<l.length;s+=1){var h=l[s];if(o=h.test.apply(h,r))break}}if(!c.format[o])throw new Error("unknown format: "+r);var f=c.format[o].apply(null,i?r:r.slice(0,-1));e._rgb=function(r){r._clipped=!1,r._unclipped=r.slice(0);for(var n=0;n<=3;n++)n<3?((r[n]<0||r[n]>255)&&(r._clipped=!0),r[n]=t(r[n],0,255)):3===n&&(r[n]=t(r[n],0,1));return r}(f),3===e._rgb.length&&e._rgb.push(1)};h.prototype.toString=function(){return"function"==a(this.hex)?this.hex():"["+this._rgb.join(",")+"]"};var f=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(f.Color,[null].concat(t)))};f.Color=h,f.version="2.6.0";var p=function(t){return Math.round(100*t)/100},g=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n,e,o=(t=i(t,"rgba"))[0],a=t[1],u=t[2],c=s(o/=255,a/=255,u/=255),h=l(o,a,u),f=(h+c)/2;return h===c?(n=0,e=Number.NaN):n=f<.5?(h-c)/(h+c):(h-c)/(2-h-c),o==h?e=(a-u)/(h-c):a==h?e=2+(u-o)/(h-c):u==h&&(e=4+(o-a)/(h-c)),(e*=60)<0&&(e+=360),t.length>3&&void 0!==t[3]?[e,n,f,t[3]]:[e,n,f]},b=Math.round,d=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=i(t,"rgba"),e=u(t)||"rgb";return"hsl"==e.substr(0,3)?function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=i(t,"hsla"),e=u(t)||"lsa";return n[0]=p(n[0]||0),n[1]=p(100*n[1])+"%",n[2]=p(100*n[2])+"%","hsla"===e||n.length>3&&n[3]<1?(n[3]=n.length>3?n[3]:1,e="hsla"):n.length=3,e+"("+n.join(",")+")"}(g(n),e):(n[0]=b(n[0]),n[1]=b(n[1]),n[2]=b(n[2]),("rgba"===e||n.length>3&&n[3]<1)&&(n[3]=n.length>3?n[3]:1,e="rgba"),e+"("+n.slice(0,"rgb"===e?3:4).join(",")+")")},v=Math.round,y=function(){for(var t,r=[],n=arguments.length;n--;)r[n]=arguments[n];var e,o,a,u=(r=i(r,"hsl"))[0],s=r[1],l=r[2];if(0===s)e=o=a=255*l;else{var c=[0,0,0],h=[0,0,0],f=l<.5?l*(1+s):l+s-l*s,p=2*l-f,g=u/360;c[0]=g+1/3,c[1]=g,c[2]=g-1/3;for(var b=0;b<3;b++)c[b]<0&&(c[b]+=1),c[b]>1&&(c[b]-=1),6*c[b]<1?h[b]=p+6*(f-p)*c[b]:2*c[b]<1?h[b]=f:3*c[b]<2?h[b]=p+(f-p)*(2/3-c[b])*6:h[b]=p;e=(t=[v(255*h[0]),v(255*h[1]),v(255*h[2])])[0],o=t[1],a=t[2]}return r.length>3?[e,o,a,r[3]]:[e,o,a,1]},w=/^rgb\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*\)$/,m=/^rgba\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*,\s*([01]|[01]?\.\d+)\)$/,k=/^rgb\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,_=/^rgba\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,M=/^hsl\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,x=/^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,F=Math.round,j=function(t){var r;if(t=t.toLowerCase().trim(),c.format.named)try{return c.format.named(t)}catch(t){}if(r=t.match(w)){for(var n=r.slice(1,4),e=0;e<3;e++)n[e]=+n[e];return n[3]=1,n}if(r=t.match(m)){for(var o=r.slice(1,5),a=0;a<4;a++)o[a]=+o[a];return o}if(r=t.match(k)){for(var i=r.slice(1,4),u=0;u<3;u++)i[u]=F(2.55*i[u]);return i[3]=1,i}if(r=t.match(_)){for(var s=r.slice(1,5),l=0;l<3;l++)s[l]=F(2.55*s[l]);return s[3]=+s[3],s}if(r=t.match(M)){var h=r.slice(1,4);h[1]*=.01,h[2]*=.01;var f=y(h);return f[3]=1,f}if(r=t.match(x)){var p=r.slice(1,4);p[1]*=.01,p[2]*=.01;var g=y(p);return g[3]=+r[4],g}};j.test=function(t){return w.test(t)||m.test(t)||k.test(t)||_.test(t)||M.test(t)||x.test(t)},h.prototype.css=function(t){return d(this._rgb,t)},f.css=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(h,[null].concat(t,["css"])))},c.format.css=j,c.autodetect.push({p:5,test:function(t){for(var r=[],n=arguments.length-1;n-- >0;)r[n]=arguments[n+1];if(!r.length&&"string"===a(t)&&j.test(t))return"css"}});var N=/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,$=/^#?([A-Fa-f0-9]{8}|[A-Fa-f0-9]{4})$/,A=Math.round;h.prototype.hex=function(t){return function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=i(t,"rgba"),e=n[0],o=n[1],a=n[2],s=n[3],l=u(t)||"auto";void 0===s&&(s=1),"auto"===l&&(l=s<1?"rgba":"rgb");var c="000000"+((e=A(e))<<16|(o=A(o))<<8|(a=A(a))).toString(16);c=c.substr(c.length-6);var h="0"+A(255*s).toString(16);switch(h=h.substr(h.length-2),l.toLowerCase()){case"rgba":return"#"+c+h;case"argb":return"#"+h+c;default:return"#"+c}}(this._rgb,t)},f.hex=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(h,[null].concat(t,["hex"])))},c.format.hex=function(t){if(t.match(N)){4!==t.length&&7!==t.length||(t=t.substr(1)),3===t.length&&(t=(t=t.split(""))[0]+t[0]+t[1]+t[1]+t[2]+t[2]);var r=parseInt(t,16);return[r>>16,r>>8&255,255&r,1]}if(t.match($)){5!==t.length&&9!==t.length||(t=t.substr(1)),4===t.length&&(t=(t=t.split(""))[0]+t[0]+t[1]+t[1]+t[2]+t[2]+t[3]+t[3]);var n=parseInt(t,16);return[n>>24&255,n>>16&255,n>>8&255,Math.round((255&n)/255*100)/100]}throw new Error("unknown hex color: "+t)},c.autodetect.push({p:4,test:function(t){for(var r=[],n=arguments.length-1;n-- >0;)r[n]=arguments[n+1];if(!r.length&&"string"===a(t)&&[3,4,5,6,7,8,9].indexOf(t.length)>=0)return"hex"}}),h.prototype.hsl=function(){return g(this._rgb)},f.hsl=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(h,[null].concat(t,["hsl"])))},c.format.hsl=y,c.autodetect.push({p:2,test:function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];if("array"===a(t=i(t,"hsl"))&&3===t.length)return"hsl"}});var C=18,E=.95047,O=1,S=1.08883,L=.137931034,q=.206896552,I=.12841855,B=.008856452,D=Math.pow,R=function(t){return 255*(t<=.00304?12.92*t:1.055*D(t,1/2.4)-.055)},U=function(t){return t>q?t*t*t:I*(t-L)},z=Math.pow,G=function(t){return(t/=255)<=.04045?t/12.92:z((t+.055)/1.055,2.4)},H=function(t){return t>B?z(t,1/3):t/I+L},J=function(t,r,n){return t=G(t),r=G(r),n=G(n),[H((.4124564*t+.3575761*r+.1804375*n)/E),H((.2126729*t+.7151522*r+.072175*n)/O),H((.0193339*t+.119192*r+.9503041*n)/S)]};h.prototype.lab=function(){return function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=i(t,"rgb"),e=n[0],o=n[1],a=n[2],u=J(e,o,a),s=u[0],l=u[1],c=116*l-16;return[c<0?0:c,500*(s-l),200*(l-u[2])]}(this._rgb)},f.lab=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(h,[null].concat(t,["lab"])))},c.format.lab=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n,e,o,a=(t=i(t,"lab"))[0],u=t[1],s=t[2];return e=(a+16)/116,n=isNaN(u)?e:e+u/500,o=isNaN(s)?e:e-s/200,e=O*U(e),n=E*U(n),o=S*U(o),[R(3.2404542*n-1.5371385*e-.4985314*o),R(-.969266*n+1.8760108*e+.041556*o),R(.0556434*n-.2040259*e+1.0572252*o),t.length>3?t[3]:1]},c.autodetect.push({p:2,test:function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];if("array"===a(t=i(t,"lab"))&&3===t.length)return"lab"}});var K=Math.pow,P=Math.sign;function Q(t){var r=Math.abs(t);return r>.0031308?(P(t)||1)*(1.055*K(r,1/2.4)-.055):12.92*t}var T=Math.cbrt,V=Math.pow,W=Math.sign;function X(t){var r=Math.abs(t);return r<.04045?t/12.92:(W(t)||1)*V((r+.055)/1.055,2.4)}h.prototype.oklab=function(){return function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=i(t,"rgb"),e=n[0],o=n[1],a=n[2],u=[X(e/255),X(o/255),X(a/255)],s=u[0],l=u[1],c=u[2],h=T(.4122214708*s+.5363325363*l+.0514459929*c),f=T(.2119034982*s+.6806995451*l+.1073969566*c),p=T(.0883024619*s+.2817188376*l+.6299787005*c);return[.2104542553*h+.793617785*f-.0040720468*p,1.9779984951*h-2.428592205*f+.4505937099*p,.0259040371*h+.7827717662*f-.808675766*p]}(this._rgb)},f.oklab=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(h,[null].concat(t,["oklab"])))},c.format.oklab=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=(t=i(t,"lab"))[0],e=t[1],o=t[2],a=K(n+.3963377774*e+.2158037573*o,3),u=K(n-.1055613458*e-.0638541728*o,3),s=K(n-.0894841775*e-1.291485548*o,3);return[255*Q(4.0767416621*a-3.3077115913*u+.2309699292*s),255*Q(-1.2684380046*a+2.6097574011*u-.3413193965*s),255*Q(-.0041960863*a-.7034186147*u+1.707614701*s),t.length>3?t[3]:1]},c.autodetect.push({p:3,test:function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];if("array"===a(t=i(t,"oklab"))&&3===t.length)return"oklab"}});var Y=Math.round;h.prototype.rgb=function(t){return void 0===t&&(t=!0),!1===t?this._rgb.slice(0,3):this._rgb.slice(0,3).map(Y)},h.prototype.rgba=function(t){return void 0===t&&(t=!0),this._rgb.slice(0,4).map((function(r,n){return n<3?!1===t?r:Y(r):r}))},f.rgb=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(h,[null].concat(t,["rgb"])))},c.format.rgb=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=i(t,"rgba");return void 0===n[3]&&(n[3]=1),n},c.autodetect.push({p:3,test:function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];if("array"===a(t=i(t,"rgba"))&&(3===t.length||4===t.length&&"number"==a(t[3])&&t[3]>=0&&t[3]<=1))return"rgb"}}),h.prototype.alpha=function(t,r){return void 0===r&&(r=!1),void 0!==t&&"number"===a(t)?r?(this._rgb[3]=t,this):new h([this._rgb[0],this._rgb[1],this._rgb[2],t],"rgb"):this._rgb[3]},h.prototype.darken=function(t){void 0===t&&(t=1);var r=this.lab();return r[0]-=C*t,new h(r,"lab").alpha(this.alpha(),!0)},h.prototype.brighten=function(t){return void 0===t&&(t=1),this.darken(-t)},h.prototype.darker=h.prototype.darken,h.prototype.brighter=h.prototype.brighten,h.prototype.get=function(t){var r=t.split("."),n=r[0],e=r[1],o=this[n]();if(e){var a=n.indexOf(e)-("ok"===n.substr(0,2)?2:0);if(a>-1)return o[a];throw new Error("unknown channel "+e+" in mode "+n)}return o};var Z={};function tt(t,r,n){void 0===n&&(n=.5);for(var e=[],o=arguments.length-3;o-- >0;)e[o]=arguments[o+3];var i=e[0]||"lrgb";if(Z[i]||e.length||(i=Object.keys(Z)[0]),!Z[i])throw new Error("interpolation mode "+i+" is not defined");return"object"!==a(t)&&(t=new h(t)),"object"!==a(r)&&(r=new h(r)),Z[i](t,r,n).alpha(t.alpha()+n*(r.alpha()-t.alpha()))}h.prototype.mix=h.prototype.interpolate=function(t,r){void 0===r&&(r=.5);for(var n=[],e=arguments.length-2;e-- >0;)n[e]=arguments[e+2];return tt.apply(void 0,[this,t,r].concat(n))},h.prototype.set=function(t,r,n){void 0===n&&(n=!1);var e=t.split("."),o=e[0],i=e[1],u=this[o]();if(i){var s=o.indexOf(i)-("ok"===o.substr(0,2)?2:0);if(s>-1){if("string"==a(r))switch(r.charAt(0)){case"+":case"-":u[s]+=+r;break;case"*":u[s]*=+r.substr(1);break;case"/":u[s]/=+r.substr(1);break;default:u[s]=+r}else{if("number"!==a(r))throw new Error("unsupported value for Color.set");u[s]=r}var l=new h(u,o);return n?(this._rgb=l._rgb,this):l}throw new Error("unknown channel "+i+" in mode "+o)}return u},h.prototype.tint=function(t){void 0===t&&(t=.5);for(var r=[],n=arguments.length-1;n-- >0;)r[n]=arguments[n+1];return tt.apply(void 0,[this,"white",t].concat(r))},h.prototype.shade=function(t){void 0===t&&(t=.5);for(var r=[],n=arguments.length-1;n-- >0;)r[n]=arguments[n+1];return tt.apply(void 0,[this,"black",t].concat(r))};var rt=Math.sqrt,nt=Math.pow;Z.lrgb=function(t,r,n){var e=t._rgb,o=e[0],a=e[1],i=e[2],u=r._rgb,s=u[0],l=u[1],c=u[2];return new h(rt(nt(o,2)*(1-n)+nt(s,2)*n),rt(nt(a,2)*(1-n)+nt(l,2)*n),rt(nt(i,2)*(1-n)+nt(c,2)*n),"rgb")};Z.oklab=function(t,r,n){var e=t.oklab(),o=r.oklab();return new h(e[0]+n*(o[0]-e[0]),e[1]+n*(o[1]-e[1]),e[2]+n*(o[2]-e[2]),"oklab")},f.mix=f.interpolate=require("./src/generator/mix"),f.valid=require("./src/utils/valid"),module.exports=f}));
