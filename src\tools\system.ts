import os from 'os';
import { spawn } from 'cross-spawn';
import { nanoid } from 'nanoid';
import type { ToolResult, ProcessInfo } from '@/types';

export class SystemTool {
  async getInfo(args: Record<string, unknown>): Promise<ToolResult> {
    const startTime = Date.now();
    const resultId = nanoid();

    try {
      const type = (args.type as string) || 'basic';
      let output = '';

      switch (type) {
        case 'basic':
          output = await this.getBasicInfo();
          break;
        case 'detailed':
          output = await this.getDetailedInfo();
          break;
        case 'processes':
          output = await this.getProcessInfo();
          break;
        case 'network':
          output = await this.getNetworkInfo();
          break;
        default:
          throw new Error(`Unknown info type: ${type}`);
      }

      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: true,
        output,
        executionTime: Date.now() - startTime,
        metadata: {
          type,
        },
      };
    } catch (error) {
      return {
        id: resultId,
        toolCallId: args.toolCallId as string || '',
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };
    }
  }

  private async getBasicInfo(): Promise<string> {
    const info = {
      platform: os.platform(),
      arch: os.arch(),
      release: os.release(),
      hostname: os.hostname(),
      uptime: this.formatUptime(os.uptime()),
      nodeVersion: process.version,
      workingDirectory: process.cwd(),
      homeDirectory: os.homedir(),
      totalMemory: this.formatBytes(os.totalmem()),
      freeMemory: this.formatBytes(os.freemem()),
      cpuCount: os.cpus().length,
      cpuModel: os.cpus()[0]?.model || 'Unknown',
    };

    return this.formatInfo('Basic System Information', info);
  }

  private async getDetailedInfo(): Promise<string> {
    const basicInfo = await this.getBasicInfo();
    
    const cpus = os.cpus();
    const networkInterfaces = os.networkInterfaces();
    
    let detailedInfo = basicInfo + '\n\n';
    
    // CPU Information
    detailedInfo += '=== CPU Information ===\n';
    detailedInfo += `CPU Count: ${cpus.length}\n`;
    detailedInfo += `CPU Model: ${cpus[0]?.model || 'Unknown'}\n`;
    detailedInfo += `CPU Speed: ${cpus[0]?.speed || 'Unknown'} MHz\n\n`;

    // Memory Information
    const memUsage = process.memoryUsage();
    detailedInfo += '=== Memory Information ===\n';
    detailedInfo += `Total System Memory: ${this.formatBytes(os.totalmem())}\n`;
    detailedInfo += `Free System Memory: ${this.formatBytes(os.freemem())}\n`;
    detailedInfo += `Process RSS: ${this.formatBytes(memUsage.rss)}\n`;
    detailedInfo += `Process Heap Used: ${this.formatBytes(memUsage.heapUsed)}\n`;
    detailedInfo += `Process Heap Total: ${this.formatBytes(memUsage.heapTotal)}\n`;
    detailedInfo += `Process External: ${this.formatBytes(memUsage.external)}\n\n`;

    // Network Interfaces
    detailedInfo += '=== Network Interfaces ===\n';
    for (const [name, interfaces] of Object.entries(networkInterfaces)) {
      if (interfaces) {
        detailedInfo += `${name}:\n`;
        for (const iface of interfaces) {
          detailedInfo += `  ${iface.family}: ${iface.address}\n`;
        }
      }
    }

    // Environment Variables (selected)
    detailedInfo += '\n=== Environment Variables ===\n';
    const envVars = ['PATH', 'HOME', 'USER', 'SHELL', 'TERM', 'NODE_ENV'];
    for (const envVar of envVars) {
      if (process.env[envVar]) {
        detailedInfo += `${envVar}: ${process.env[envVar]}\n`;
      }
    }

    return detailedInfo;
  }

  private async getProcessInfo(): Promise<string> {
    let output = '=== Process Information ===\n';
    
    // Current process info
    output += `Current Process:\n`;
    output += `  PID: ${process.pid}\n`;
    output += `  PPID: ${process.ppid}\n`;
    output += `  Title: ${process.title}\n`;
    output += `  Version: ${process.version}\n`;
    output += `  Platform: ${process.platform}\n`;
    output += `  Arch: ${process.arch}\n`;
    output += `  Uptime: ${this.formatUptime(process.uptime())}\n\n`;

    // Try to get system processes (platform-specific)
    try {
      const processes = await this.getSystemProcesses();
      if (processes.length > 0) {
        output += 'System Processes (top 10 by memory):\n';
        output += 'PID\tNAME\t\tMEMORY\n';
        output += '---\t----\t\t------\n';
        
        processes.slice(0, 10).forEach(proc => {
          const name = proc.name.padEnd(15);
          const memory = proc.memoryUsage ? this.formatBytes(proc.memoryUsage) : 'N/A';
          output += `${proc.pid}\t${name}\t${memory}\n`;
        });
      }
    } catch (error) {
      output += 'Could not retrieve system processes\n';
    }

    return output;
  }

  private async getNetworkInfo(): Promise<string> {
    let output = '=== Network Information ===\n';
    
    const networkInterfaces = os.networkInterfaces();
    
    for (const [name, interfaces] of Object.entries(networkInterfaces)) {
      if (interfaces) {
        output += `\n${name}:\n`;
        for (const iface of interfaces) {
          output += `  Family: ${iface.family}\n`;
          output += `  Address: ${iface.address}\n`;
          output += `  Netmask: ${iface.netmask}\n`;
          output += `  MAC: ${iface.mac}\n`;
          output += `  Internal: ${iface.internal}\n`;
          output += `  Scope ID: ${iface.scopeid || 'N/A'}\n`;
          output += '  ---\n';
        }
      }
    }

    // Try to get additional network info
    try {
      const networkStats = await this.getNetworkStats();
      output += '\n' + networkStats;
    } catch (error) {
      output += '\nCould not retrieve network statistics\n';
    }

    return output;
  }

  private async getSystemProcesses(): Promise<ProcessInfo[]> {
    return new Promise((resolve) => {
      
      // Platform-specific process listing
      let command: string;
      let args: string[];
      
      switch (os.platform()) {
        case 'win32':
          command = 'tasklist';
          args = ['/fo', 'csv'];
          break;
        case 'darwin':
        case 'linux':
          command = 'ps';
          args = ['aux'];
          break;
        default:
          resolve([]);
          return;
      }

      const child = spawn(command, args, { stdio: 'pipe' });
      let output = '';

      child.stdout?.on('data', (data) => {
        output += data.toString();
      });

      child.on('close', () => {
        try {
          const parsed = this.parseProcessOutput(output, os.platform());
          resolve(parsed);
        } catch (error) {
          resolve([]);
        }
      });

      child.on('error', () => {
        resolve([]);
      });
    });
  }

  private parseProcessOutput(output: string, platform: NodeJS.Platform): ProcessInfo[] {
    const processes: ProcessInfo[] = [];
    const lines = output.split('\n').slice(1); // Skip header

    for (const line of lines) {
      if (!line.trim()) continue;

      try {
        let process: ProcessInfo;

        if (platform === 'win32') {
          // Parse Windows tasklist output
          const parts = line.split(',').map(p => p.replace(/"/g, ''));
          if (parts.length >= 5) {
            process = {
              pid: parseInt(parts[1]) || 0,
              name: parts[0] || 'Unknown',
              command: parts[0] || 'Unknown',
              status: 'running',
              startTime: new Date(), // Not available in tasklist
              memoryUsage: this.parseMemoryString(parts[4]),
            };
          } else {
            continue;
          }
        } else {
          // Parse Unix ps output
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 11) {
            process = {
              pid: parseInt(parts[1]) || 0,
              name: parts[10] || 'Unknown',
              command: parts.slice(10).join(' ') || 'Unknown',
              status: 'running',
              startTime: new Date(), // Would need additional parsing
              cpuUsage: parseFloat(parts[2]) || 0,
              memoryUsage: parseFloat(parts[3]) || 0,
            };
          } else {
            continue;
          }
        }

        processes.push(process);
      } catch (error) {
        // Skip malformed lines
      }
    }

    return processes.sort((a, b) => (b.memoryUsage || 0) - (a.memoryUsage || 0));
  }

  private async getNetworkStats(): Promise<string> {
    return new Promise((resolve) => {
      let command: string;
      let args: string[];
      
      switch (os.platform()) {
        case 'win32':
          command = 'netstat';
          args = ['-e'];
          break;
        case 'darwin':
          command = 'netstat';
          args = ['-i'];
          break;
        case 'linux':
          command = 'cat';
          args = ['/proc/net/dev'];
          break;
        default:
          resolve('Network statistics not available on this platform');
          return;
      }

      const child = spawn(command, args, { stdio: 'pipe' });
      let output = '';

      child.stdout?.on('data', (data) => {
        output += data.toString();
      });

      child.on('close', () => {
        resolve(output || 'No network statistics available');
      });

      child.on('error', () => {
        resolve('Could not retrieve network statistics');
      });
    });
  }

  private formatInfo(title: string, info: Record<string, any>): string {
    let output = `=== ${title} ===\n`;
    
    for (const [key, value] of Object.entries(info)) {
      const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      output += `${formattedKey}: ${value}\n`;
    }
    
    return output;
  }

  private formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  private formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    const parts = [];
    if (days > 0) parts.push(`${days}d`);
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    if (secs > 0) parts.push(`${secs}s`);
    
    return parts.join(' ') || '0s';
  }

  private parseMemoryString(memStr: string): number {
    const match = memStr.match(/(\d+(?:,\d+)*)\s*(\w+)?/);
    if (!match) return 0;
    
    const num = parseInt(match[1].replace(/,/g, ''));
    const unit = match[2]?.toLowerCase();
    
    switch (unit) {
      case 'kb':
      case 'k':
        return num * 1024;
      case 'mb':
      case 'm':
        return num * 1024 * 1024;
      case 'gb':
      case 'g':
        return num * 1024 * 1024 * 1024;
      default:
        return num;
    }
  }
}
