{"name": "chroma-js", "description": "JavaScript library for color conversions", "version": "2.6.0", "author": "<PERSON>", "type": "module", "homepage": "https://github.com/gka/chroma.js", "keywords": ["color"], "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "https://vis4.net"}], "bugs": "https://github.com/gka/chroma.js/issues", "repository": {"type": "git", "url": "git://github.com/gka/chroma.js.git"}, "exports": {".": {"import": "./index.js", "require": "./dist/chroma.cjs"}}, "main": "./index.js", "scripts": {"prepublishOnly": "npm test -- --run && node .bin/update-version.cjs && npm run build", "build": "rollup -c && cross-env DEV=1 rollup -c ", "docs": "cd docs && make", "docs-preview": "cd docs && make && make preview", "test": "vitest test/*.js", "test:old": "vows --dot-matrix test/*.cjs", "lint": "prettier --check index.js index-light.js src *.config.js test && eslint index.js index-light.js src", "format": "prettier --write index.js index-light.js src *.config.js test", "prepare": "husky"}, "devDependencies": {"@babel/eslint-parser": "^7.25.0", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.8.0", "@rollup/plugin-buble": "^1.0.3", "@rollup/plugin-terser": "^0.4.4", "cross-env": "^7.0.3", "es6-shim": "^0.35.8", "eslint": "^9.8.0", "globals": "^15.8.0", "http-server": "^14.1.1", "husky": "^9.1.3", "markdown-to-html": "0.0.13", "minimatch": "^10.0.1", "prettier": "^3.3.3", "rollup": "^4.19.1", "rollup-plugin-license": "^3.5.2", "vitest": "^2.0.4", "vows": "^0.8.3"}, "license": "(BSD-3-Clause AND Apache-2.0)", "spm": {"main": "chroma.js", "ignore": ["src", "doc", "test"]}}