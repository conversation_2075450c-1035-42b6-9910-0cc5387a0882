"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Agent = void 0;
const events_1 = require("events");
const nanoid_1 = require("nanoid");
const logger_1 = require("../utils/logger");
const session_1 = require("./session");
const context_1 = require("./context");
const providers_1 = require("../ai/providers");
const registry_1 = require("../tools/registry");
const approval_1 = require("../security/approval");
const validator_1 = require("../security/validator");
class Agent extends events_1.EventEmitter {
    config;
    sessionManager;
    contextManager;
    aiProvider;
    toolRegistry;
    approvalSystem;
    riskValidator;
    currentSession = null;
    isProcessing = false;
    constructor(config) {
        super();
        this.config = config;
        this.sessionManager = new session_1.SessionManager();
        this.contextManager = new context_1.ContextManager(config.workingDirectory);
        this.aiProvider = new providers_1.AIProviderManager(config);
        this.toolRegistry = new registry_1.ToolRegistry();
        this.approvalSystem = new approval_1.ApprovalSystem(config.approvalMode);
        this.riskValidator = new validator_1.RiskValidator();
        this.setupEventHandlers();
        logger_1.logger.info('Agent initialized', {
            provider: config.provider,
            model: config.model,
            approvalMode: config.approvalMode
        });
    }
    setupEventHandlers() {
        this.contextManager.on('context-updated', (context) => {
            this.emit('context-updated', context);
        });
        this.approvalSystem.on('approval-required', (toolCall) => {
            this.emit('approval-required', toolCall);
        });
        this.toolRegistry.on('tool-executed', (result) => {
            this.emit('tool-executed', result);
        });
    }
    async initialize() {
        try {
            await this.sessionManager.initialize();
            await this.contextManager.initialize();
            await this.toolRegistry.initialize();
            // Create or resume session
            if (this.config.sessionId) {
                this.currentSession = await this.sessionManager.loadSession(this.config.sessionId);
            }
            else {
                this.currentSession = await this.sessionManager.createSession({
                    workingDirectory: this.config.workingDirectory,
                    context: await this.contextManager.getContext(),
                });
            }
            logger_1.logger.info('Agent initialized successfully', {
                sessionId: this.currentSession.id
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize agent', error);
            throw error;
        }
    }
    async processMessage(message, streamHandler) {
        if (this.isProcessing) {
            throw new Error('Agent is already processing a message');
        }
        if (!this.currentSession) {
            throw new Error('Agent not initialized');
        }
        this.isProcessing = true;
        const messageId = (0, nanoid_1.nanoid)();
        try {
            // Add user message to conversation
            const userMessage = {
                id: messageId,
                role: 'user',
                content: message,
                timestamp: new Date(),
            };
            await this.sessionManager.addMessage(this.currentSession.id, userMessage);
            this.emit('message-added', userMessage);
            // Get current context
            const context = await this.contextManager.getContext();
            // Prepare conversation history
            const messages = await this.sessionManager.getMessages(this.currentSession.id);
            // Get available tools
            const tools = this.toolRegistry.getAvailableTools();
            // Stream AI response
            const assistantMessage = await this.aiProvider.streamCompletion({
                messages,
                tools,
                context,
                onChunk: streamHandler?.onChunk,
                onToolCall: async (toolCall) => {
                    return await this.handleToolCall(toolCall);
                },
            });
            // Add assistant message to conversation
            await this.sessionManager.addMessage(this.currentSession.id, assistantMessage);
            this.emit('message-added', assistantMessage);
            // Update context if needed
            await this.contextManager.updateContext();
            return assistantMessage;
        }
        catch (error) {
            logger_1.logger.error('Error processing message', error);
            streamHandler?.onError(error);
            throw error;
        }
        finally {
            this.isProcessing = false;
            streamHandler?.onComplete();
        }
    }
    async handleToolCall(toolCall) {
        const startTime = Date.now();
        try {
            // Validate and assess risk
            const riskAssessment = await this.riskValidator.assessRisk(toolCall);
            toolCall.riskLevel = riskAssessment.level;
            this.emit('tool-call-received', { toolCall, riskAssessment });
            // Check if approval is required
            const approved = await this.approvalSystem.requestApproval(toolCall, riskAssessment);
            if (!approved) {
                return {
                    id: (0, nanoid_1.nanoid)(),
                    toolCallId: toolCall.id,
                    success: false,
                    output: '',
                    error: 'Tool call was not approved',
                    executionTime: Date.now() - startTime,
                };
            }
            // Execute the tool
            const result = await this.toolRegistry.executeTool(toolCall);
            // Update session with tool result
            if (this.currentSession) {
                await this.sessionManager.updateToolResult(this.currentSession.id, result);
            }
            this.emit('tool-executed', result);
            return result;
        }
        catch (error) {
            logger_1.logger.error('Error executing tool', error);
            const errorResult = {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: toolCall.id,
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime: Date.now() - startTime,
            };
            this.emit('tool-error', { toolCall, error: errorResult });
            return errorResult;
        }
    }
    async getSession() {
        return this.currentSession;
    }
    async getSessions() {
        return await this.sessionManager.getSessions();
    }
    async switchSession(sessionId) {
        const session = await this.sessionManager.loadSession(sessionId);
        this.currentSession = session;
        // Update working directory if different
        if (session.workingDirectory !== this.config.workingDirectory) {
            this.config.workingDirectory = session.workingDirectory;
            await this.contextManager.setWorkingDirectory(session.workingDirectory);
        }
        this.emit('session-switched', session);
    }
    async createNewSession() {
        const session = await this.sessionManager.createSession({
            workingDirectory: this.config.workingDirectory,
            context: await this.contextManager.getContext(),
        });
        this.currentSession = session;
        this.emit('session-created', session);
        return session;
    }
    async deleteSession(sessionId) {
        await this.sessionManager.deleteSession(sessionId);
        if (this.currentSession?.id === sessionId) {
            this.currentSession = null;
        }
        this.emit('session-deleted', { sessionId });
    }
    async updateConfig(newConfig) {
        Object.assign(this.config, newConfig);
        if (newConfig.provider || newConfig.model || newConfig.apiKey || newConfig.baseUrl) {
            await this.aiProvider.updateConfig(this.config);
        }
        if (newConfig.approvalMode) {
            this.approvalSystem.setMode(newConfig.approvalMode);
        }
        if (newConfig.workingDirectory) {
            await this.contextManager.setWorkingDirectory(newConfig.workingDirectory);
        }
        this.emit('config-updated', this.config);
    }
    getConfig() {
        return { ...this.config };
    }
    async getContext() {
        return await this.contextManager.getContext();
    }
    async shutdown() {
        try {
            if (this.currentSession) {
                await this.sessionManager.saveSession(this.currentSession);
            }
            await this.contextManager.shutdown();
            await this.toolRegistry.shutdown();
            this.removeAllListeners();
            logger_1.logger.info('Agent shutdown completed');
        }
        catch (error) {
            logger_1.logger.error('Error during agent shutdown', error);
            throw error;
        }
    }
    // Event handling methods
    onEvent(event, handler) {
        this.on(event, handler);
    }
    offEvent(event, handler) {
        this.off(event, handler);
    }
    // Utility methods
    isReady() {
        return this.currentSession !== null && !this.isProcessing;
    }
    getStatus() {
        return {
            ready: this.isReady(),
            processing: this.isProcessing,
            sessionId: this.currentSession?.id,
            provider: this.config.provider,
            model: this.config.model,
            approvalMode: this.config.approvalMode,
        };
    }
}
exports.Agent = Agent;
//# sourceMappingURL=agent.js.map