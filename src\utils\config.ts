import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { config as dotenvConfig } from 'dotenv';
import YAML from 'yaml';
import { z } from 'zod';
import { logger } from './logger';
import type { AgentConfig, AIProvider, ApprovalMode } from '@/types';

// Load environment variables
dotenvConfig();

// Configuration schema
const ConfigSchema = z.object({
  provider: z.enum(['openai', 'deepseek', 'ollama', 'azure']).default('openai'),
  model: z.string().default('gpt-4'),
  apiKey: z.string().optional(),
  baseUrl: z.string().url().optional(),
  maxTokens: z.number().positive().default(4096),
  temperature: z.number().min(0).max(2).default(0.7),
  approvalMode: z.enum(['suggest', 'auto-edit', 'full-auto']).default('suggest'),
  workingDirectory: z.string().default(process.cwd()),
  
  // Security settings
  security: z.object({
    allowedCommands: z.array(z.string()).optional(),
    blockedCommands: z.array(z.string()).optional(),
    allowedPaths: z.array(z.string()).optional(),
    blockedPaths: z.array(z.string()).optional(),
    maxExecutionTime: z.number().positive().default(30000),
    sandboxMode: z.boolean().default(false),
  }).optional(),
  
  // Logging settings
  logging: z.object({
    level: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
    console: z.boolean().default(false),
    file: z.boolean().default(true),
  }).optional(),
  
  // UI settings
  ui: z.object({
    interactive: z.boolean().default(true),
    verbose: z.boolean().default(false),
    quiet: z.boolean().default(false),
    theme: z.enum(['default', 'dark', 'light']).default('default'),
  }).optional(),
  
  // Session settings
  session: z.object({
    autoSave: z.boolean().default(true),
    maxSessions: z.number().positive().default(100),
    cleanupDays: z.number().positive().default(30),
  }).optional(),
});

type Config = z.infer<typeof ConfigSchema>;

export class ConfigManager {
  private configPath: string;
  private config: Config | null = null;

  constructor(configPath?: string) {
    this.configPath = configPath || this.getDefaultConfigPath();
  }

  private getDefaultConfigPath(): string {
    const configDir = path.join(os.homedir(), '.kritrima');
    return path.join(configDir, 'config.yaml');
  }

  async load(): Promise<Config> {
    try {
      // Ensure config directory exists
      await fs.mkdir(path.dirname(this.configPath), { recursive: true });

      // Try to load existing config
      try {
        const configContent = await fs.readFile(this.configPath, 'utf-8');
        const rawConfig = YAML.parse(configContent);
        this.config = ConfigSchema.parse(rawConfig);
        
        logger.info('Configuration loaded', { path: this.configPath });
      } catch (error) {
        // Config file doesn't exist or is invalid, create default
        logger.info('Creating default configuration', { path: this.configPath });
        this.config = this.createDefaultConfig();
        await this.save();
      }

      // Override with environment variables
      this.applyEnvironmentOverrides();

      return this.config;
    } catch (error) {
      logger.error('Failed to load configuration', error);
      throw error;
    }
  }

  private createDefaultConfig(): Config {
    return ConfigSchema.parse({
      provider: 'openai',
      model: 'gpt-4',
      approvalMode: 'suggest',
      workingDirectory: process.cwd(),
    });
  }

  private applyEnvironmentOverrides(): void {
    if (!this.config) return;

    // AI Provider settings
    if (process.env['KRITRIMA_PROVIDER']) {
      this.config.provider = process.env['KRITRIMA_PROVIDER'] as AIProvider;
    }

    if (process.env['KRITRIMA_MODEL']) {
      this.config.model = process.env['KRITRIMA_MODEL'];
    }

    if (process.env['KRITRIMA_API_KEY'] || process.env['OPENAI_API_KEY']) {
      this.config.apiKey = process.env['KRITRIMA_API_KEY'] || process.env['OPENAI_API_KEY'];
    }

    if (process.env['KRITRIMA_BASE_URL']) {
      this.config.baseUrl = process.env['KRITRIMA_BASE_URL'];
    }

    if (process.env['KRITRIMA_MAX_TOKENS']) {
      this.config.maxTokens = parseInt(process.env['KRITRIMA_MAX_TOKENS']);
    }

    if (process.env['KRITRIMA_TEMPERATURE']) {
      this.config.temperature = parseFloat(process.env['KRITRIMA_TEMPERATURE']);
    }

    if (process.env['KRITRIMA_APPROVAL_MODE']) {
      this.config.approvalMode = process.env['KRITRIMA_APPROVAL_MODE'] as ApprovalMode;
    }

    if (process.env['KRITRIMA_WORKING_DIR']) {
      this.config.workingDirectory = process.env['KRITRIMA_WORKING_DIR'];
    }

    // Logging settings
    if (process.env['LOG_LEVEL']) {
      this.config.logging = this.config.logging || {
        level: 'info',
        console: false,
        file: true,
      };
      this.config.logging.level = process.env['LOG_LEVEL'] as any;
    }

    if (process.env['LOG_CONSOLE']) {
      this.config.logging = this.config.logging || {
        level: 'info',
        console: false,
        file: true,
      };
      this.config.logging.console = process.env['LOG_CONSOLE'] === 'true';
    }

    // Provider-specific API keys
    if (process.env['DEEPSEEK_API_KEY'] && this.config.provider === 'deepseek') {
      this.config.apiKey = process.env['DEEPSEEK_API_KEY'];
    }

    if (process.env['AZURE_OPENAI_API_KEY'] && this.config.provider === 'azure') {
      this.config.apiKey = process.env['AZURE_OPENAI_API_KEY'];
    }
  }

  async save(): Promise<void> {
    if (!this.config) {
      throw new Error('No configuration to save');
    }

    try {
      const configContent = YAML.stringify(this.config, {
        indent: 2,
        lineWidth: 100,
      });
      
      await fs.writeFile(this.configPath, configContent, 'utf-8');
      logger.info('Configuration saved', { path: this.configPath });
    } catch (error) {
      logger.error('Failed to save configuration', error);
      throw error;
    }
  }

  async update(updates: Partial<Config>): Promise<void> {
    if (!this.config) {
      throw new Error('Configuration not loaded');
    }

    // Validate updates
    const updatedConfig = { ...this.config, ...updates };
    this.config = ConfigSchema.parse(updatedConfig);
    
    await this.save();
    logger.info('Configuration updated', { updates });
  }

  get(): Config {
    if (!this.config) {
      throw new Error('Configuration not loaded');
    }
    return { ...this.config };
  }

  toAgentConfig(): AgentConfig {
    if (!this.config) {
      throw new Error('Configuration not loaded');
    }

    return {
      provider: this.config.provider,
      model: this.config.model,
      apiKey: this.config.apiKey,
      baseUrl: this.config.baseUrl,
      maxTokens: this.config.maxTokens,
      temperature: this.config.temperature,
      approvalMode: this.config.approvalMode,
      workingDirectory: this.config.workingDirectory,
    };
  }

  // Utility methods for specific configurations
  getProviderConfig() {
    if (!this.config) return null;
    
    return {
      provider: this.config.provider,
      model: this.config.model,
      apiKey: this.config.apiKey,
      baseUrl: this.config.baseUrl,
    };
  }

  getSecurityConfig() {
    return this.config?.security || {};
  }

  getLoggingConfig() {
    return this.config?.logging || {};
  }

  getUIConfig() {
    return this.config?.ui || {};
  }

  getSessionConfig() {
    return this.config?.session || {};
  }

  // Validation methods
  async validateConfig(): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    if (!this.config) {
      errors.push('Configuration not loaded');
      return { valid: false, errors };
    }

    // Validate API key for providers that require it
    if (['openai', 'deepseek', 'azure'].includes(this.config.provider) && !this.config.apiKey) {
      errors.push(`API key required for provider: ${this.config.provider}`);
    }

    // Validate base URL for Azure
    if (this.config.provider === 'azure' && !this.config.baseUrl) {
      errors.push('Base URL required for Azure provider');
    }

    // Validate working directory
    try {
      await fs.access(this.config.workingDirectory);
    } catch (error) {
      errors.push(`Working directory not accessible: ${this.config.workingDirectory}`);
    }

    // Validate model name format
    if (!this.config.model || this.config.model.trim().length === 0) {
      errors.push('Model name cannot be empty');
    }

    return { valid: errors.length === 0, errors };
  }

  // Provider-specific configurations
  static getProviderDefaults(provider: AIProvider): Partial<Config> {
    switch (provider) {
      case 'openai':
        return {
          provider: 'openai',
          model: 'gpt-4',
          baseUrl: undefined,
        };
      
      case 'deepseek':
        return {
          provider: 'deepseek',
          model: 'deepseek-chat',
          baseUrl: 'https://api.deepseek.com/v1',
        };
      
      case 'ollama':
        return {
          provider: 'ollama',
          model: 'llama2',
          baseUrl: 'http://localhost:11434/v1',
          apiKey: 'ollama',
        };
      
      case 'azure':
        return {
          provider: 'azure',
          model: 'gpt-4',
          // baseUrl and apiKey must be provided by user
        };
      
      default:
        return {};
    }
  }

  // Configuration templates
  static createTemplate(provider: AIProvider): Config {
    const defaults = ConfigManager.getProviderDefaults(provider);
    return ConfigSchema.parse({
      ...defaults,
      approvalMode: 'suggest',
      workingDirectory: process.cwd(),
    });
  }

  // Export configuration for backup
  async exportConfig(): Promise<string> {
    if (!this.config) {
      throw new Error('Configuration not loaded');
    }

    return YAML.stringify({
      ...this.config,
      // Remove sensitive information
      apiKey: this.config.apiKey ? '[REDACTED]' : undefined,
    }, { indent: 2 });
  }

  // Import configuration from backup
  async importConfig(configYaml: string): Promise<void> {
    try {
      const rawConfig = YAML.parse(configYaml);
      this.config = ConfigSchema.parse(rawConfig);
      await this.save();
      logger.info('Configuration imported successfully');
    } catch (error) {
      logger.error('Failed to import configuration', error);
      throw error;
    }
  }
}

// Global config manager instance
export const configManager = new ConfigManager();

// Convenience function to load configuration
export async function loadConfig(configPath?: string): Promise<AgentConfig> {
  const manager = configPath ? new ConfigManager(configPath) : configManager;
  await manager.load();
  return manager.toAgentConfig();
}
