
v0.9.3
------

New: Promise#resolveTimeout() and Promise#rejectTimeout()


v0.9.2
------

New: Promise.debounceNextTick()


v0.9.1
------

Promise.interceptor() improved
Promise.timeout() and Promise.variableTimeout() bug fixed (now support any thenable, not only Seventh's one)


v0.9.0
------

BREAKING: Promise.returnValueInterceptor() removed, replaced by Promise.interceptor(), instead of triggering the interceptorFn
with the function return value, it is triggered when that return value is resolved (if it needs to).
It also accept an errorInterceptorFn.

MAYBE BREAKING: decorators Promise.once(), Promise.serialize(), Promise.debounce(), Promise.debounceDelay() and Promise.debounceUpdate()
now have automatic supports for 'this', allowing easier management of object's method, instead of creating it inside the constructor,
it's now possible to create it on the prototype, because each 'this' object has its own meta-data through a WeakMap.


v0.8.3
------

New: Promise.nextTick() (exposing internal nextTick() function), Promise.resolveNextTick(), Promise.rejectNextTick()
Promise.debounceUpdate() now supports option 'waitNextTick' (it's like setting option 'waitFn' to Promise.resolveNextTick)


v0.8.2
------

Package cleaned


v0.8.1
------

.debounceUpdate() improved: we can now pass options to add delay/delayFn/waitFn


v0.8.0
------

BREAKING CHANGES, conformance to new ECMAScript Promise.any(): Promise.any() and Promise.some() now rejects with AggregateError
Adding Promise.allSettled()
Require Node engine >= 16.13.0 (require AggregateError)


v0.7.40
-------

Queue: .addBatch()


v0.7.39
-------

Queue: adding a job with no data (=undefined) set its id as its data


v0.7.38
-------

Queue: job/queue stats


v0.7.37
-------

New: Promise.Queue(), a queue with dependencies, that can be ever-growing


v0.7.36
-------

New: Promise#isSettled()


v0.7.35
-------

Factorize .onceEvent*OrError()


v0.7.34
-------

.debounceSync() support a batch no delay mode


v0.7.33
-------

.debounceSync has a .onDebounce property for syncGet params


v0.7.32
-------

.debounceSync() decoratee now accepts a final Promise.NO_DELAY option to force syncing without delay when not busy


v0.7.31
-------

New: .debounceDelay()


v0.7.30
-------

New: Promise.resolveAtAnimationFrame() and Promise.rejectAtAnimationFrame()


v0.7.29
-------

Browser build


v0.7.28
-------

New: Promise.resolveNextTick(), Promise.rejectNextTick()


v0.7.27
-------

Fixed bad foreign promise usage


v0.7.26
-------

Fixed decorators thisBinding bugs
New: .debounceSync() to debounce synchronization algorithms


v0.7.25
-------

Fix bugged .finally() behavior, splitting into .finally() and .tapFinally()


v0.7.24
-------

New: .promisify()/.promisifyAll() now add extra property 'arg'/'args' to Error instances on rejection


