# Kritrima AI CLI - Complete Agentic Workflow Demo

This document demonstrates the complete agentic AI workflow implemented in Kritrima AI CLI.

## Architecture Overview

The Kritrima AI CLI implements a sophisticated agentic AI system with the following components:

### 1. Agent Engine (src/core/agent.ts)
- **Central orchestrator** that coordinates all system components
- **Conversation management** with persistent memory
- **Tool execution** with safety validation
- **Session management** across CLI sessions
- **Real-time streaming** responses from AI

### 2. AI Provider System (src/ai/providers.ts)
- **Universal OpenAI SDK** supporting multiple providers:
  - OpenAI GPT models
  - Deepseek models
  - Azure OpenAI
  - Ollama (local models)
- **Streaming responses** with real-time tool execution
- **Function calling** with structured tool schemas
- **Error handling** and retry logic

### 3. Tool Registry (src/tools/registry.ts)
- **Comprehensive tool system** with 15+ tools:
  - Shell command execution
  - File operations (read, write, create, delete, move, copy)
  - File search and grep functionality
  - System information gathering
  - Directory listing and navigation
- **Tool chaining** and parallel execution
- **Safety validation** for each tool call

### 4. Security System (src/security/)
- **Risk assessment** for all commands
- **Approval modes**: Suggest, Auto-Edit, Full-Auto
- **Command validation** and sanitization
- **Path safety** checks
- **Dangerous pattern** detection

### 5. Context Management (src/core/context.ts)
- **Automatic project discovery**:
  - Node.js, Python, Rust, Go, Java projects
  - Package manager detection
  - Dependency analysis
  - Framework identification
- **File watching** for real-time updates
- **Environment information** gathering
- **Intelligent context** injection into AI prompts

### 6. Session Storage (src/storage/session.ts)
- **Persistent conversation** memory
- **Session management** with SQLite
- **Context preservation** across CLI sessions
- **Session search** and filtering
- **Export/import** functionality

### 7. CLI Interface (src/cli/interface.ts)
- **Interactive mode** with rich terminal UI
- **Real-time streaming** display
- **Command completion** and history
- **Status monitoring** and feedback
- **Error handling** with graceful degradation

## Complete Workflow Example

### 1. User Input
```bash
kritrima chat
> "Create a React component for a todo list with TypeScript"
```

### 2. Agent Processing
- **Input validation** and sanitization
- **Context injection** (current directory, project type, etc.)
- **AI provider** selection and configuration
- **Tool registry** preparation

### 3. AI Analysis
- **Request understanding** using advanced language models
- **Task decomposition** into actionable steps
- **Tool selection** based on requirements
- **Safety assessment** for each planned action

### 4. Tool Execution Chain
```
1. 🔍 System Info → Detect current environment
2. 📁 File Search → Find existing React components
3. 📝 File Create → Generate component file
4. 📦 Package Check → Verify TypeScript dependencies
5. 🧪 File Write → Create test file
6. ✅ Validation → Verify component syntax
```

### 5. Real-time Streaming
```
🤖 I'll help you create a React TypeScript todo component...

🔧 Checking current project structure...
✅ Detected React TypeScript project

🔧 Creating TodoList component...
✅ Created src/components/TodoList.tsx

🔧 Adding TypeScript interfaces...
✅ Added proper type definitions

🔧 Creating component tests...
✅ Created src/components/__tests__/TodoList.test.tsx

🎉 Todo component created successfully!
```

### 6. Safety Validation
- **Command approval** based on risk level
- **User confirmation** for destructive operations
- **Sandbox execution** for untrusted commands
- **Rollback capability** for failed operations

### 7. Result Processing
- **Output formatting** for user display
- **Error handling** with helpful suggestions
- **Context updates** with new file information
- **Session persistence** for future reference

## Advanced Features

### Multi-Tool Chaining
```javascript
// Example: "Set up a new Express server with TypeScript"
[
  { tool: "shell", command: "npm init -y" },
  { tool: "shell", command: "npm install express @types/express typescript" },
  { tool: "file_create", path: "src/server.ts", content: "..." },
  { tool: "file_create", path: "tsconfig.json", content: "..." },
  { tool: "shell", command: "npm run build" }
]
```

### Parallel Execution
```javascript
// Example: "Analyze project dependencies and system resources"
await Promise.all([
  toolRegistry.execute("file_read", { path: "package.json" }),
  toolRegistry.execute("system_info", { type: "detailed" }),
  toolRegistry.execute("shell", { command: "npm audit" })
]);
```

### Context-Aware Responses
```javascript
// AI automatically knows:
- Current working directory
- Project type (React, Node.js, Python, etc.)
- Available scripts and dependencies
- Git status and branch information
- System environment and capabilities
```

### Intelligent Error Recovery
```javascript
// If a command fails, the AI can:
1. Analyze the error message
2. Suggest alternative approaches
3. Automatically retry with different parameters
4. Ask for user guidance when needed
```

## Production Features

### Enterprise-Grade Security
- **Command validation** against known dangerous patterns
- **Path traversal** protection
- **Environment isolation** options
- **Audit logging** for all operations

### Performance Optimization
- **Lazy loading** of tools and providers
- **Caching** of context and session data
- **Streaming responses** for immediate feedback
- **Resource monitoring** and limits

### Reliability
- **Graceful error handling** at every level
- **Automatic retry** logic with exponential backoff
- **Session recovery** after crashes
- **Comprehensive logging** for debugging

### Extensibility
- **Plugin architecture** for custom tools
- **Provider abstraction** for new AI models
- **Configuration system** for customization
- **Event system** for integrations

## Getting Started

1. **Installation**:
   ```bash
   npm install -g kritrima-ai-cli
   ```

2. **Configuration**:
   ```bash
   kritrima config init openai
   kritrima config set apiKey "your-api-key"
   ```

3. **Interactive Mode**:
   ```bash
   kritrima chat
   ```

4. **Single Commands**:
   ```bash
   kritrima exec "Show me the project structure"
   ```

This architecture provides a production-ready foundation for AI-powered coding assistance with enterprise-grade security, reliability, and extensibility.
