import { EventEmitter } from 'events';
import { nanoid } from 'nanoid';
import { logger } from '@/utils/logger';
import { SessionManager } from './session';
import { ContextManager } from './context';
import { AIProviderManager } from '@/ai/providers';
import { ToolRegistry } from '@/tools/registry';
import { ApprovalSystem } from '@/security/approval';
import { RiskValidator } from '@/security/validator';
import type {
  AgentConfig,
  ConversationMessage,
  ToolCall,
  ToolResult,
  StreamHandler,
  EventHandler,
  Session,
} from '@/types';

export class Agent extends EventEmitter {
  private sessionManager: SessionManager;
  private contextManager: ContextManager;
  private aiProvider: AIProviderManager;
  private toolRegistry: ToolRegistry;
  private approvalSystem: ApprovalSystem;
  private riskValidator: RiskValidator;
  private currentSession: Session | null = null;
  private isProcessing = false;

  constructor(private config: AgentConfig) {
    super();
    
    this.sessionManager = new SessionManager();
    this.contextManager = new ContextManager(config.workingDirectory);
    this.aiProvider = new AIProviderManager(config);
    this.toolRegistry = new ToolRegistry();
    this.approvalSystem = new ApprovalSystem(config.approvalMode);
    this.riskValidator = new RiskValidator();

    this.setupEventHandlers();
    logger.info('Agent initialized', { 
      provider: config.provider, 
      model: config.model,
      approvalMode: config.approvalMode 
    });
  }

  private setupEventHandlers(): void {
    this.contextManager.on('context-updated', (context) => {
      this.emit('context-updated', context);
    });

    this.approvalSystem.on('approval-required', (toolCall) => {
      this.emit('approval-required', toolCall);
    });

    this.toolRegistry.on('tool-executed', (result) => {
      this.emit('tool-executed', result);
    });
  }

  async initialize(): Promise<void> {
    try {
      await this.sessionManager.initialize();
      await this.contextManager.initialize();
      await this.toolRegistry.initialize();

      // Create or resume session
      if (this.config.sessionId) {
        this.currentSession = await this.sessionManager.loadSession(this.config.sessionId);
      } else {
        this.currentSession = await this.sessionManager.createSession({
          workingDirectory: this.config.workingDirectory,
          context: await this.contextManager.getContext(),
        });
      }

      logger.info('Agent initialized successfully', {
        sessionId: this.currentSession.id
      });
    } catch (error) {
      logger.error('Failed to initialize agent', error);
      throw error;
    }
  }

  async processMessage(
    message: string,
    streamHandler?: StreamHandler
  ): Promise<ConversationMessage> {
    if (this.isProcessing) {
      throw new Error('Agent is already processing a message');
    }

    if (!this.currentSession) {
      throw new Error('Agent not initialized');
    }

    this.isProcessing = true;
    const messageId = nanoid();

    try {
      // Add user message to conversation
      const userMessage: ConversationMessage = {
        id: messageId,
        role: 'user',
        content: message,
        timestamp: new Date(),
      };

      await this.sessionManager.addMessage(this.currentSession.id, userMessage);
      this.emit('message-added', userMessage);

      // Get current context
      const context = await this.contextManager.getContext();
      
      // Prepare conversation history
      const messages = await this.sessionManager.getMessages(this.currentSession.id);
      
      // Get available tools
      const tools = this.toolRegistry.getAvailableTools();

      // Stream AI response
      const assistantMessage = await this.aiProvider.streamCompletion({
        messages,
        tools,
        context,
        onChunk: streamHandler?.onChunk,
        onToolCall: async (toolCall) => {
          return await this.handleToolCall(toolCall);
        },
      });

      // Add assistant message to conversation
      await this.sessionManager.addMessage(this.currentSession.id, assistantMessage);
      this.emit('message-added', assistantMessage);

      // Update context if needed
      await this.contextManager.updateContext();

      return assistantMessage;

    } catch (error) {
      logger.error('Error processing message', error);
      streamHandler?.onError(error as Error);
      throw error;
    } finally {
      this.isProcessing = false;
      streamHandler?.onComplete();
    }
  }

  private async handleToolCall(toolCall: ToolCall): Promise<ToolResult> {
    const startTime = Date.now();
    
    try {
      // Validate and assess risk
      const riskAssessment = await this.riskValidator.assessRisk(toolCall);
      toolCall.riskLevel = riskAssessment.level;

      this.emit('tool-call-received', { toolCall, riskAssessment });

      // Check if approval is required
      const approved = await this.approvalSystem.requestApproval(toolCall, riskAssessment);
      
      if (!approved) {
        return {
          id: nanoid(),
          toolCallId: toolCall.id,
          success: false,
          output: '',
          error: 'Tool call was not approved',
          executionTime: Date.now() - startTime,
        };
      }

      // Execute the tool
      const result = await this.toolRegistry.executeTool(toolCall);
      
      // Update session with tool result
      if (this.currentSession) {
        await this.sessionManager.updateToolResult(this.currentSession.id, result);
      }

      this.emit('tool-executed', result);
      return result;

    } catch (error) {
      logger.error('Error executing tool', error);
      
      const errorResult: ToolResult = {
        id: nanoid(),
        toolCallId: toolCall.id,
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime,
      };

      this.emit('tool-error', { toolCall, error: errorResult });
      return errorResult;
    }
  }

  async getSession(): Promise<Session | null> {
    return this.currentSession;
  }

  async getSessions(): Promise<Session[]> {
    return await this.sessionManager.getSessions();
  }

  async switchSession(sessionId: string): Promise<void> {
    const session = await this.sessionManager.loadSession(sessionId);
    this.currentSession = session;
    
    // Update working directory if different
    if (session.workingDirectory !== this.config.workingDirectory) {
      this.config.workingDirectory = session.workingDirectory;
      await this.contextManager.setWorkingDirectory(session.workingDirectory);
    }

    this.emit('session-switched', session);
  }

  async createNewSession(): Promise<Session> {
    const session = await this.sessionManager.createSession({
      workingDirectory: this.config.workingDirectory,
      context: await this.contextManager.getContext(),
    });
    
    this.currentSession = session;
    this.emit('session-created', session);
    return session;
  }

  async deleteSession(sessionId: string): Promise<void> {
    await this.sessionManager.deleteSession(sessionId);
    
    if (this.currentSession?.id === sessionId) {
      this.currentSession = null;
    }
    
    this.emit('session-deleted', { sessionId });
  }

  async updateConfig(newConfig: Partial<AgentConfig>): Promise<void> {
    Object.assign(this.config, newConfig);
    
    if (newConfig.provider || newConfig.model || newConfig.apiKey || newConfig.baseUrl) {
      await this.aiProvider.updateConfig(this.config);
    }
    
    if (newConfig.approvalMode) {
      this.approvalSystem.setMode(newConfig.approvalMode);
    }
    
    if (newConfig.workingDirectory) {
      await this.contextManager.setWorkingDirectory(newConfig.workingDirectory);
    }

    this.emit('config-updated', this.config);
  }

  getConfig(): AgentConfig {
    return { ...this.config };
  }

  async getContext() {
    return await this.contextManager.getContext();
  }

  async shutdown(): Promise<void> {
    try {
      if (this.currentSession) {
        await this.sessionManager.saveSession(this.currentSession);
      }
      
      await this.contextManager.shutdown();
      await this.toolRegistry.shutdown();
      
      this.removeAllListeners();
      logger.info('Agent shutdown completed');
    } catch (error) {
      logger.error('Error during agent shutdown', error);
      throw error;
    }
  }

  // Event handling methods
  onEvent<T = Record<string, unknown>>(event: string, handler: EventHandler<T>): void {
    this.on(event, handler);
  }

  offEvent<T = Record<string, unknown>>(event: string, handler: EventHandler<T>): void {
    this.off(event, handler);
  }

  // Utility methods
  isReady(): boolean {
    return this.currentSession !== null && !this.isProcessing;
  }

  getStatus() {
    return {
      ready: this.isReady(),
      processing: this.isProcessing,
      sessionId: this.currentSession?.id,
      provider: this.config.provider,
      model: this.config.model,
      approvalMode: this.config.approvalMode,
    };
  }
}
