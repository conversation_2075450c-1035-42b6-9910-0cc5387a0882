{"version": 3, "file": "validator.js", "sourceRoot": "", "sources": ["../../src/security/validator.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAwB;AACxB,2CAAwC;AAGxC,MAAa,aAAa;IAChB,gBAAgB,GAAa,EAAE,CAAC;IAChC,gBAAgB,GAAa,EAAE,CAAC;IAChC,kBAAkB,GAAa,EAAE,CAAC;IAClC,YAAY,GAAgB,IAAI,GAAG,EAAE,CAAC;IAE9C;QACE,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,yBAAyB;QACzB,IAAI,CAAC,gBAAgB,GAAG;YACtB,gBAAgB,EAAqB,WAAW;YAChD,gBAAgB,EAAqB,WAAW;YAChD,UAAU,EAA2B,cAAc;YACnD,MAAM,EAA+B,sBAAsB;YAC3D,OAAO,EAA8B,oBAAoB;YACzD,kBAAkB,EAAmB,iBAAiB;YACtD,YAAY,EAAyB,2BAA2B;YAChE,cAAc,EAAuB,0BAA0B;YAC/D,+BAA+B,EAAM,kBAAkB;YACvD,4BAA4B,EAAQ,YAAY;SACjD,CAAC;QAEF,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,GAAG;YACtB,SAAS,EAA4B,gBAAgB;YACrD,OAAO,EAA8B,cAAc;YACnD,aAAa,EAAwB,wBAAwB;YAC7D,UAAU,EAA2B,mBAAmB;YACxD,OAAO,EAA8B,gBAAgB;YACrD,SAAS,EAA4B,iBAAiB;YACtD,UAAU,EAA2B,eAAe;YACpD,kBAAkB,EAAmB,sBAAsB;YAC3D,+BAA+B,EAAM,yBAAyB;YAC9D,eAAe,EAAsB,gBAAgB;YACrD,eAAe,EAAsB,gBAAgB;YACrD,SAAS,EAA4B,kBAAkB;YACvD,SAAS,EAA4B,iBAAiB;SACvD,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,kBAAkB,GAAG;YACxB,oBAAoB,EAAiB,qBAAqB;YAC1D,eAAe,EAAsB,yBAAyB;YAC9D,eAAe,EAAsB,uBAAuB;YAC5D,eAAe,EAAsB,uBAAuB;YAC5D,gBAAgB,EAAqB,uBAAuB;YAC5D,YAAY,EAAyB,WAAW;YAChD,eAAe,EAAsB,cAAc;YACnD,cAAc,EAAuB,aAAa;YAClD,kCAAkC,EAAG,kBAAkB;YACvD,gCAAgC,EAAK,kBAAkB;YACvD,cAAc,EAAuB,eAAe;YACpD,aAAa,EAAwB,2BAA2B;SACjE,CAAC;QAEF,uCAAuC;QACvC,MAAM,eAAe,GAAG;YACtB,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;YACnE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM;YACjE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK;YAC/D,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY;YAC7D,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU;YACnD,gBAAgB,EAAE,eAAe,EAAE,eAAe;YAClD,kBAAkB,EAAE,eAAe,EAAE,eAAe;SACrD,CAAC;QAEF,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,QAAkB;QACjC,MAAM,UAAU,GAAmB;YACjC,KAAK,EAAE,MAAM;YACb,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,EAAE;YACf,WAAW,EAAE,KAAK;SACnB,CAAC;QAEF,IAAI,CAAC;YACH,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACtB,KAAK,OAAO;oBACV,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;oBAC9C,MAAM;gBACR,KAAK,aAAa;oBAChB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;oBAC5C,MAAM;gBACR,KAAK,YAAY,CAAC;gBAClB,KAAK,aAAa;oBAChB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;oBAC3C,MAAM;gBACR,KAAK,WAAW;oBACd,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;oBAC1C,MAAM;gBACR;oBACE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACjD,CAAC;YAED,4CAA4C;YAC5C,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,KAAK,KAAK,MAAM,IAAI,UAAU,CAAC,KAAK,KAAK,KAAK,CAAC;YAEnF,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBACxC,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,UAAU,CAAC,KAAK;gBAC3B,WAAW,EAAE,UAAU,CAAC,OAAO,CAAC,MAAM;aACvC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;YAC1B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACjE,UAAU,CAAC,WAAW,GAAG,KAAK,CAAC;QACjC,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,kBAAkB,CAAC,QAAkB,EAAE,UAA0B;QACvE,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAiB,CAAC;QACrD,MAAM,IAAI,GAAI,QAAQ,CAAC,SAAS,CAAC,IAAiB,IAAI,EAAE,CAAC;QACzD,MAAM,WAAW,GAAG,GAAG,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;QAE1D,kCAAkC;QAClC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5C,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC9B,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC;gBAC9B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,0CAA0C,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACpF,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;gBACxF,OAAO;YACT,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5C,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC9B,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;gBAC1B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,sCAAsC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAChF,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;YAC1F,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9C,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC9B,IAAI,UAAU,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;oBAChC,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC;gBAC9B,CAAC;gBACD,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,wCAAwC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAClF,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YACvF,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACzE,IAAI,UAAU,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;gBAChC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YAClF,CAAC;YACD,OAAO;QACT,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAEpD,mEAAmE;QACnE,IAAI,UAAU,CAAC,KAAK,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACnE,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;YACzB,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,OAAe,EAAE,UAA0B;QACvE,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,kDAAkD;QAClD,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,mBAAmB,CAAC,CAAC;QAClG,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;YAClC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACjD,IAAI,UAAU,CAAC,KAAK,KAAK,MAAM,IAAI,UAAU,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;oBAC9D,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC;gBAC9B,CAAC;gBACD,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;gBACrE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;YAC9E,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrG,IAAI,UAAU,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;gBAChC,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;YAC3B,CAAC;YACD,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACtD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;QACvF,CAAC;QAED,uCAAuC;QACvC,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5G,IAAI,UAAU,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;gBAChC,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC;YAC9B,CAAC;YACD,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACxD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,QAAkB,EAAE,UAA0B;QACrE,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAc,CAAC;QACnD,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,SAAoB,CAAC;QAE1D,IAAI,SAAS,EAAE,CAAC;YACd,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;YAC1B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACxD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrF,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC;YAC5B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACrD,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC;YAC9B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YAC7D,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QACxF,CAAC;QAED,+BAA+B;QAC/B,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACtD,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;YAC1B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;YAC1E,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,QAAkB,EAAE,UAA0B;QACpE,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAc,CAAC;QACnD,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAiB,CAAC;QAErD,uCAAuC;QACvC,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;YAC1B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YACtD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,oEAAoE,CAAC,CAAC;QACpG,CAAC;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC;YAC5B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAC9D,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAClF,CAAC;QAED,wCAAwC;QACxC,IAAI,OAAO,IAAI,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,EAAE,CAAC;YACvD,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;YAC1B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YACvE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,UAAU,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC;YAChC,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;YACzB,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,QAAkB,EAAE,UAA0B;QACnE,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAgB,CAAC;QACnD,MAAM,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAqB,CAAC;QAE7D,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC;YAChE,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;YAC1B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACjE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;QAC/F,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;YACzB,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,QAAkB,EAAE,UAA0B;QACtE,uCAAuC;QACvC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;QAC1B,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,2BAA2B,CAAC,CAAC;IAC3E,CAAC;IAEO,YAAY,CAAC,QAAgB;QACnC,MAAM,WAAW,GAAG;YAClB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;YACzD,aAAa,EAAE,mBAAmB,EAAE,yBAAyB;YAC7D,cAAc,EAAE,cAAc;SAC/B,CAAC;QAEF,MAAM,cAAc,GAAG,cAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC9D,OAAO,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAChC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CACjD,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACvC,MAAM,oBAAoB,GAAG;YAC3B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;YAC9C,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;YACpD,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;SAC7C,CAAC;QAEF,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAEO,yBAAyB,CAAC,OAAe;QAC/C,MAAM,kBAAkB,GAAG;YACzB,WAAW;YACX,WAAW;YACX,aAAa;YACb,iBAAiB;YACjB,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,eAAe;YACf,eAAe;YACf,UAAU;YACV,UAAU;SACX,CAAC;QAEF,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,wBAAwB;IACxB,kBAAkB,CAAC,OAAe;QAChC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,kBAAkB,CAAC,OAAe;QAChC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IACvE,CAAC;IAED,oBAAoB,CAAC,OAAe;QAClC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IACzE,CAAC;IAED,cAAc,CAAC,OAAe;QAC5B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC/B,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,iBAAiB,CAAC,OAAe;QAC/B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAClC,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACpD,CAAC;CACF;AA/VD,sCA+VC"}