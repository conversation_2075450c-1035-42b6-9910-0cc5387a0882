(function(e){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=e()}else if(typeof define==="function"&&define.amd){define([],e)}else{var t;if(typeof window!=="undefined"){t=window}else if(typeof global!=="undefined"){t=global}else if(typeof self!=="undefined"){t=self}else{t=this}t.treeKit=e()}})(function(){var e,t,r;return function(){function e(t,r,n){function o(i,u){if(!r[i]){if(!t[i]){var s="function"==typeof require&&require;if(!u&&s)return s(i,!0);if(f)return f(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var c=r[i]={exports:{}};t[i][0].call(c.exports,function(e){var r=t[i][1][e];return o(r||e)},c,c.exports,e,t,r,n)}return r[i].exports}for(var f="function"==typeof require&&require,i=0;i<n.length;i++)o(n[i]);return o}return e}()({1:[function(e,t,r){"use strict";r.map=function(e,t){if(!e||typeof e!=="object"){throw new Error("Expecting an object")}return Object.fromEntries(Object.entries(e).map(e=>[e[0],t(e[1])]))};r.filter=function(e,t){if(!e||typeof e!=="object"){throw new Error("Expecting an object")}return Object.fromEntries(Object.entries(e).filter(e=>t(e[1])))}},{}],2:[function(e,t,r){"use strict";const n={};t.exports=n;n.extend=e("./extend.js");n.clone=e("./clone.js");n.path=e("./path.js");n.dotPath=e("./dotPath.js");n.wildDotPath=e("./wildDotPath.js");Object.assign(n,e("./arrayLike.js"))},{"./arrayLike.js":1,"./clone.js":3,"./dotPath.js":4,"./extend.js":5,"./path.js":6,"./wildDotPath.js":7}],3:[function(e,t,r){"use strict";function n(e,t){var r=Object.getPrototypeOf(e);if(n.opaque.has(r)){return n.opaque.get(r)(e)}var o,f,i,u,s,a,c=[{source:e,target:Array.isArray(e)?[]:Object.create(r)}],p=c[0].target,l=new Map;l.set(e,p);while(u=c.shift()){i=Object.getOwnPropertyNames(u.source);for(o=0;o<i.length;o++){f=Object.getOwnPropertyDescriptor(u.source,i[o]);if(!f.value||typeof f.value!=="object"){Object.defineProperty(u.target,i[o],f);continue}s=f.value;if(t){if(l.has(s)){f.value=l.get(s);Object.defineProperty(u.target,i[o],f);continue}}a=Object.getPrototypeOf(f.value);if(n.opaque.has(a)){f.value=n.opaque.get(a)(f.value);Object.defineProperty(u.target,i[o],f);continue}f.value=Array.isArray(s)?[]:Object.create(a);if(t){l.set(s,f.value)}Object.defineProperty(u.target,i[o],f);c.push({source:s,target:f.value})}}return p}t.exports=n;n.opaque=new Map;n.opaque.set(Date.prototype,e=>new Date(e))},{}],4:[function(e,t,r){"use strict";const n={};t.exports=n;const o=[];const f="This would cause prototype pollution";function i(e){if(Array.isArray(e)){return e}if(!e){return o}if(typeof e==="string"){return e[e.length-1]==="."?e.slice(0,-1).split("."):e.split(".")}throw new TypeError("[tree.dotPath]: the path argument should be a string or an array")}n.toPathArray=i;function u(e,t,r=0){var n,o,i,u=e;for(n=0,o=t.length+r;n<o;n++){i=t[n];if(typeof i==="object"||i==="__proto__"||typeof u==="function"){throw new Error(f)}if(!u||typeof u!=="object"){return undefined}u=u[i]}return u}function s(e,t){var r,n,o,i=e;for(r=0,n=t.length-1;r<n;r++){o=t[r];if(typeof o==="object"||o==="__proto__"||typeof i[o]==="function"){throw new Error(f)}if(!i[o]||typeof i[o]!=="object"){i[o]={}}i=i[o]}return i}n.get=((e,t)=>u(e,i(t)));n.set=((e,t,r)=>{if(!e||typeof e!=="object"){return}var n=i(t),o=n[n.length-1];if(typeof o==="object"||o==="__proto__"){throw new Error(f)}var u=s(e,n);u[o]=r;return r});n.define=((e,t,r)=>{if(!e||typeof e!=="object"){return}var n=i(t),o=n[n.length-1];if(typeof o==="object"||o==="__proto__"){throw new Error(f)}var u=s(e,n);if(!(o in u)){u[o]=r}return u[o]});n.inc=((e,t)=>{if(!e||typeof e!=="object"){return}var r=i(t),n=r[r.length-1];if(typeof n==="object"||n==="__proto__"){throw new Error(f)}var o=s(e,r);if(typeof o[n]==="number"){o[n]++}else if(!o[n]||typeof o[n]!=="object"){o[n]=1}return o[n]});n.dec=((e,t)=>{if(!e||typeof e!=="object"){return}var r=i(t),n=r[r.length-1];if(typeof n==="object"||n==="__proto__"){throw new Error(f)}var o=s(e,r);if(typeof o[n]==="number"){o[n]--}else if(!o[n]||typeof o[n]!=="object"){o[n]=-1}return o[n]});n.concat=((e,t,r)=>{if(!e||typeof e!=="object"){return}var n=i(t),o=n[n.length-1];if(typeof o==="object"||o==="__proto__"){throw new Error(f)}var u=s(e,n);if(!u[o]){u[o]=r}else if(Array.isArray(u[o])&&Array.isArray(r)){u[o]=u[o].concat(r)}return u[o]});n.insert=((e,t,r)=>{if(!e||typeof e!=="object"){return}var n=i(t),o=n[n.length-1];if(typeof o==="object"||o==="__proto__"){throw new Error(f)}var u=s(e,n);if(!u[o]){u[o]=r}else if(Array.isArray(u[o])&&Array.isArray(r)){u[o]=r.concat(u[o])}return u[o]});n.delete=((e,t)=>{var r=i(t),n=r[r.length-1];if(typeof n==="object"||n==="__proto__"){throw new Error(f)}var o=u(e,r,-1);if(!o||typeof o!=="object"||!Object.hasOwn(o,n)){return false}delete o[n];return true});n.autoPush=((e,t,r)=>{if(!e||typeof e!=="object"){return}var n=i(t),o=n[n.length-1];if(typeof o==="object"||o==="__proto__"){throw new Error(f)}var u=s(e,n);if(u[o]===undefined){u[o]=r}else if(Array.isArray(u[o])){u[o].push(r)}else{u[o]=[u[o],r]}return u[o]});n.append=((e,t,r)=>{if(!e||typeof e!=="object"){return}var n=i(t),o=n[n.length-1];if(typeof o==="object"||o==="__proto__"){throw new Error(f)}var u=s(e,n);if(!u[o]){u[o]=[r]}else if(Array.isArray(u[o])){u[o].push(r)}return u[o]});n.prepend=((e,t,r)=>{if(!e||typeof e!=="object"){return}var n=i(t),o=n[n.length-1];if(typeof o==="object"||o==="__proto__"){throw new Error(f)}var u=s(e,n);if(!u[o]){u[o]=[r]}else if(Array.isArray(u[o])){u[o].unshift(r)}return u[o]})},{}],5:[function(e,t,r){"use strict";function n(e,t,...r){var n,f,i=false,u=r.length;if(!u){return t}if(!e||typeof e!=="object"){e={}}var s={depth:0,prefix:""};if(e.deep){if(Array.isArray(e.deep)){e.deep=new Set(e.deep)}else if(!(e.deep instanceof Set)){e.deep=true}}if(e.immutables){if(Array.isArray(e.immutables)){e.immutables=new Set(e.immutables)}else if(!(e.immutables instanceof Set)){delete e.immutables}}if(!e.maxDepth&&e.deep&&!e.circular){e.maxDepth=100}if(e.deepFunc){e.deep=true}if(e.flat){e.deep=true;e.proto=false;e.inherit=false;e.unflat=false;if(typeof e.flat!=="string"){e.flat="."}}if(e.unflat){e.deep=false;e.proto=false;e.inherit=false;e.flat=false;if(typeof e.unflat!=="string"){e.unflat="."}}if(e.inherit){e.own=true;e.proto=false}else if(e.proto){e.own=true}if(!t||typeof t!=="object"&&typeof t!=="function"){i=true}if(!e.skipRoot&&(e.inherit||e.proto)){for(n=u-1;n>=0;n--){f=r[n];if(f&&(typeof f==="object"||typeof f==="function")){if(e.inherit){if(i){t=Object.create(f)}else{Object.setPrototypeOf(t,f)}}else if(e.proto){if(i){t=Object.create(Object.getPrototypeOf(f))}else{Object.setPrototypeOf(t,Object.getPrototypeOf(f))}}break}}}else if(i){t={}}s.references={sources:[],targets:[]};for(n=0;n<u;n++){f=r[n];if(!f||typeof f!=="object"&&typeof f!=="function"){continue}o(s,e,t,f,e.mask<=n+1)}return t}t.exports=n;function o(e,t,r,n,o){var i,u;if(t.maxDepth&&e.depth>t.maxDepth){throw new Error("[tree] extend(): max depth reached("+t.maxDepth+")")}if(t.circular){e.references.sources.push(n);e.references.targets.push(r)}if(t.unflat&&e.depth===0){for(u in n){e.unflatKeys=u.split(t.unflat);e.unflatIndex=0;e.unflatFullKey=u;f(e,t,r,n,e.unflatKeys[e.unflatIndex],o)}delete e.unflatKeys;delete e.unflatIndex;delete e.unflatFullKey}else if(t.own){if(t.nonEnum){i=Object.getOwnPropertyNames(n)}else{i=Object.keys(n)}for(u of i){f(e,t,r,n,u,o)}}else{for(u in n){f(e,t,r,n,u,o)}}}function f(e,t,r,n,i,u){if(i==="__proto__"){return}let s,a,c;if(e.unflatKeys){if(e.unflatIndex<e.unflatKeys.length-1){s={}}else{s=n[e.unflatFullKey]}}else if(t.descriptor){a=Object.getOwnPropertyDescriptor(n,i);s=a.value}else{s=n[i]}let p=e.prefix+i;if(t.nofunc&&typeof s==="function"){return}let l=r[p];let d=l&&(typeof l==="object"||typeof l==="function");let y=s&&(typeof s==="object"||typeof s==="function");if((t.deep||e.unflatKeys)&&s&&(typeof s==="object"||t.deepFunc&&typeof s==="function")&&(!t.descriptor||!a.get)&&((c=Object.getPrototypeOf(s))||true)&&(!(t.deep instanceof Set)||t.deep.has(c))&&(!t.immutables||!t.immutables.has(c))&&(!t.preserve||d)&&(!u||d)){let y=t.circular?e.references.sources.indexOf(s):-1;if(t.flat){if(y>=0){return}o({depth:e.depth+1,prefix:e.prefix+i+t.flat,references:e.references},t,r,s,u)}else{if(y>=0){l=e.references.targets[y];if(t.descriptor){Object.defineProperty(r,p,{value:l,enumerable:a.enumerable,writable:a.writable,configurable:a.configurable})}else{r[p]=l}return}if(!d||!Object.hasOwn(r,p)){if(Array.isArray(s)){l=[]}else if(t.proto){l=Object.create(c)}else if(t.inherit){l=Object.create(s)}else{l={}}if(t.descriptor){Object.defineProperty(r,p,{value:l,enumerable:a.enumerable,writable:a.writable,configurable:a.configurable})}else{r[p]=l}}else if(t.proto&&Object.getPrototypeOf(l)!==c){Object.setPrototypeOf(l,c)}else if(t.inherit&&Object.getPrototypeOf(l)!==s){Object.setPrototypeOf(l,s)}if(t.circular){e.references.sources.push(s);e.references.targets.push(l)}if(e.unflatKeys&&e.unflatIndex<e.unflatKeys.length-1){let r=e.unflatKeys[e.unflatIndex+1];f({depth:e.depth,unflatKeys:e.unflatKeys,unflatIndex:e.unflatIndex+1,unflatFullKey:e.unflatFullKey,prefix:"",references:e.references},t,l,n,r,u)}else{o({depth:e.depth+1,prefix:"",references:e.references},t,l,s,u)}}}else if(u&&(l===undefined||d||y)){return}else if(t.preserve&&l!==undefined){return}else if(!t.inherit){if(t.descriptor){Object.defineProperty(r,p,a)}else{r[p]=l=s}}if(t.move){delete n[i]}}},{}],6:[function(e,t,r){"use strict";const n={};t.exports=n;const o="This would cause prototype pollution";n.op=function(e,t,r,n){var f,i,u,s,a,c=false,p=false,l,d=true;if(!t||typeof t!=="object"){return}if(typeof r==="string"){if(r){i=r.match(/([.#[\]]|[^.#[\]]+)/g)}else{i=[""]}if(i[0]==="."){i.unshift("")}if(i[i.length-1]==="."){i.push("")}}else if(Array.isArray(r)){i=r;p=true}else{throw new TypeError("[tree.path] ."+e+"(): the path argument should be a string or an array")}switch(e){case"get":case"delete":l=false;break;case"set":case"define":case"inc":case"dec":case"append":case"prepend":case"concat":case"insert":case"autoPush":l=true;break;default:throw new TypeError("[tree.path] .op(): wrong type of operation '"+e+"'")}s=t;u=i.length-1;for(f=0;f<=u;f++){if(p){if(a===undefined){a=i[f];if(typeof a==="object"||a==="__proto__"){throw new Error(o)}continue}if(typeof s[a]==="function"){throw new Error(o)}if(!s[a]||typeof s[a]!=="object"){if(!l){return undefined}s[a]={}}s=s[a];a=i[f];if(typeof a==="object"||a==="__proto__"){throw new Error(o)}continue}else if(i[f]==="."){c=false;if(a===undefined){if(!d){d=true;continue}a=""}if(typeof s[a]==="function"){throw new Error(o)}if(!s[a]||typeof s[a]!=="object"){if(!l){return undefined}s[a]={}}s=s[a];d=true;continue}else if(i[f]==="#"||i[f]==="["){c=true;d=false;if(a===undefined){if(!Array.isArray(s)){return undefined}continue}if(typeof s[a]==="function"){throw new Error(o)}if(!s[a]||!Array.isArray(s[a])){if(!l){return undefined}s[a]=[]}s=s[a];continue}else if(i[f]==="]"){d=false;continue}d=false;if(!c){a=i[f];if(typeof a==="object"||a==="__proto__"){throw new Error(o)}continue}switch(i[f]){case"length":a="length";break;case"first":a=0;break;case"last":a=s.length-1;if(a<0){a=0}break;case"next":if(!l){return undefined}a=s.length;break;case"insert":if(!l){return undefined}s.unshift(undefined);a=0;break;default:a=parseInt(i[f],10)}}switch(e){case"get":return s[a];case"delete":if(c&&typeof a==="number"){s.splice(a,1)}else{delete s[a]}return;case"set":s[a]=n;return s[a];case"define":if(!(a in s)){s[a]=n}return s[a];case"inc":if(typeof s[a]==="number"){s[a]++}else if(!s[a]||typeof s[a]!=="object"){s[a]=1}return s[a];case"dec":if(typeof s[a]==="number"){s[a]--}else if(!s[a]||typeof s[a]!=="object"){s[a]=-1}return s[a];case"append":if(!s[a]){s[a]=[n]}else if(Array.isArray(s[a])){s[a].push(n)}return s[a];case"prepend":if(!s[a]){s[a]=[n]}else if(Array.isArray(s[a])){s[a].unshift(n)}return s[a];case"concat":if(!s[a]){s[a]=n}else if(Array.isArray(s[a])&&Array.isArray(n)){s[a]=s[a].concat(n)}return s[a];case"insert":if(!s[a]){s[a]=n}else if(Array.isArray(s[a])&&Array.isArray(n)){s[a]=n.concat(s[a])}return s[a];case"autoPush":if(s[a]===undefined){s[a]=n}else if(Array.isArray(s[a])){s[a].push(n)}else{s[a]=[s[a],n]}return s[a]}};n.get=n.op.bind(undefined,"get");n.delete=n.op.bind(undefined,"delete");n.set=n.op.bind(undefined,"set");n.define=n.op.bind(undefined,"define");n.inc=n.op.bind(undefined,"inc");n.dec=n.op.bind(undefined,"dec");n.append=n.op.bind(undefined,"append");n.prepend=n.op.bind(undefined,"prepend");n.concat=n.op.bind(undefined,"concat");n.insert=n.op.bind(undefined,"insert");n.autoPush=n.op.bind(undefined,"autoPush");n.prototype={get:function(e){return n.get(this,e)},delete:function(e){return n.delete(this,e)},set:function(e,t){return n.set(this,e,t)},define:function(e,t){return n.define(this,e,t)},inc:function(e,t){return n.inc(this,e,t)},dec:function(e,t){return n.dec(this,e,t)},append:function(e,t){return n.append(this,e,t)},prepend:function(e,t){return n.prepend(this,e,t)},concat:function(e,t){return n.concat(this,e,t)},insert:function(e,t){return n.insert(this,e,t)},autoPush:function(e,t){return n.autoPush(this,e,t)}};n.upgrade=function(e){Object.defineProperties(e,{get:{value:n.op.bind(undefined,"get",e)},delete:{value:n.op.bind(undefined,"delete",e)},set:{value:n.op.bind(undefined,"set",e)},define:{value:n.op.bind(undefined,"define",e)},inc:{value:n.op.bind(undefined,"inc",e)},dec:{value:n.op.bind(undefined,"dec",e)},append:{value:n.op.bind(undefined,"append",e)},prepend:{value:n.op.bind(undefined,"prepend",e)},concat:{value:n.op.bind(undefined,"concat",e)},insert:{value:n.op.bind(undefined,"insert",e)},autoPush:{value:n.op.bind(undefined,"autoPush",e)}})}},{}],7:[function(e,t,r){"use strict";const n={};t.exports=n;const o=[];const f="This would cause prototype pollution";function i(e){if(Array.isArray(e)){return e}if(!e){return o}if(typeof e==="string"){return e[e.length-1]==="."?e.slice(0,-1).split("."):e.split(".")}throw new TypeError("[tree.wildDotPath]: the path argument should be a string or an array")}n.toPathArray=i;function u(e,t,r=0,n=0,o=[]){var i,s,a=e;for(i=t.length+r;n<i;n++){s=t[n];if(typeof s==="object"||s==="__proto__"||typeof a==="function"){throw new Error(f)}if(!a||typeof a!=="object"){return o}if(s==="*"){for(let e of Array.isArray(a)?a:Object.values(a)){u(e,t,r,n+1,o)}return o}a=a[s]}o.push(a);return o}const s=0;const a=1;const c=2;function p(e,t,r,n=0,o=0,i="",u=null){if(!u){u=r===c?{}:[]}var l,d,y=e;for(l=t.length+n;o<l;o++){d=t[o];if(typeof d==="object"||d==="__proto__"||typeof y==="function"){throw new Error(f)}if(!y||typeof y!=="object"){return u}if(d==="*"){if(Array.isArray(y)){let e=0;for(let f of y){p(f,t,r,n,o+1,i?i+"."+e:""+e,u);e++}}else{for(let[e,f]of Object.entries(y)){p(f,t,r,n,o+1,i?i+"."+e:""+e,u)}}return u}i=i?i+"."+d:d;y=y[d]}if(r===s){u.push(y)}else if(r===a){u.push(i)}else if(r===c){u[i]=y}return u}n.get=n.getValues=((e,t)=>u(e,i(t)));n.getPaths=((e,t)=>p(e,i(t),a));n.getPathValueMap=((e,t)=>p(e,i(t),c));function l(e,t,r,n,o=0){var i,u,s=e;for(i=t.length-1;o<i;o++){u=t[o];if(typeof u==="object"||u==="__proto__"||typeof s==="function"){throw new Error(f)}if(!s||typeof s!=="object"){return 0}if(u==="*"){let e=0;for(let f of Array.isArray(s)?s:Object.values(s)){e+=l(f,t,r,n,o+1)}return e}if(!s[u]||typeof s[u]!=="object"){s[u]={}}s=s[u]}u=t[o];if(u==="*"){if(!s||typeof s!=="object"){return 0}let e=0;if(Array.isArray(s)){for(let t=0;t<s.length;t++){e+=r(s,t,n)}}else{for(let t of Object.keys(s)){e+=r(s,t,n)}}return e}return r(s,u,n)}const d=(e,t,r)=>{e[t]=r;return 1};n.set=((e,t,r)=>{if(!e||typeof e!=="object"){return 0}return l(e,i(t),d,r)});const y=(e,t,r)=>{if(t in e){return 0}e[t]=r;return 1};n.define=((e,t,r)=>{if(!e||typeof e!=="object"){return 0}return l(e,i(t),y,r)});const b=(e,t)=>{if(typeof e[t]==="number"){e[t]++}else if(!e[t]||typeof e[t]!=="object"){e[t]=1}return 1};n.inc=((e,t)=>{if(!e||typeof e!=="object"){return 0}return l(e,i(t),b)});const h=(e,t)=>{if(typeof e[t]==="number"){e[t]--}else if(!e[t]||typeof e[t]!=="object"){e[t]=-1}return 1};n.dec=((e,t)=>{if(!e||typeof e!=="object"){return 0}return l(e,i(t),h)});const j=(e,t,r)=>{if(!e[t]){e[t]=r}else if(Array.isArray(e[t])&&Array.isArray(r)){e[t]=e[t].concat(r)}return 1};n.concat=((e,t,r)=>{if(!e||typeof e!=="object"){return 0}return l(e,i(t),j,r)});const w=(e,t,r)=>{if(!e[t]){e[t]=r}else if(Array.isArray(e[t])&&Array.isArray(r)){e[t]=r.concat(e[t])}return 1};n.insert=((e,t,r)=>{if(!e||typeof e!=="object"){return 0}return l(e,i(t),w,r)});const g=(e,t)=>{if(!Object.hasOwn(e,t)){return 0}delete e[t];return 1};n.delete=((e,t)=>{if(!e||typeof e!=="object"){return 0}return l(e,i(t),g)});const _=(e,t,r)=>{if(e[t]===undefined){e[t]=r}else if(Array.isArray(e[t])){e[t].push(r)}else{e[t]=[e[t],r]}return 1};n.autoPush=((e,t,r)=>{if(!e||typeof e!=="object"){return 0}return l(e,i(t),_,r)});const A=(e,t,r)=>{if(!e[t]){e[t]=[r]}else if(Array.isArray(e[t])){e[t].push(r)}return 1};n.append=((e,t,r)=>{if(!e||typeof e!=="object"){return 0}return l(e,i(t),A,r)});const v=(e,t,r)=>{if(!e[t]){e[t]=[r]}else if(Array.isArray(e[t])){e[t].unshift(r)}return 1};n.prepend=((e,t,r)=>{if(!e||typeof e!=="object"){return 0}return l(e,i(t),v,r)})},{}]},{},[2])(2)});