/*
	Terminal Kit

	Copyright (c) 2009 - 2022 <PERSON><PERSON><PERSON>

	The MIT License (MIT)

	Permission is hereby granted, free of charge, to any person obtaining a copy
	of this software and associated documentation files (the "Software"), to deal
	in the Software without restriction, including without limitation the rights
	to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
	copies of the Software, and to permit persons to whom the Software is
	furnished to do so, subject to the following conditions:

	The above copyright notice and this permission notice shall be included in all
	copies or substantial portions of the Software.

	THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
	SOFTWARE.
*/

"use strict" ;



const tree = require( 'tree-kit' ) ;
const xterm256 = require( './xterm-256color.js' ) ;
const xtermGeneric = require( './xterm.generic.js' ) ;



// Fail-safe xterm-compatible.

// So far, we derivate from xterm-256color and then just add specific things (owned properties)
// of xterm.generic.js, thus we achieve a clean inheritance model without duplicated code.

module.exports = {
	esc: tree.extend( { own: true } , Object.create( xterm256.esc ) , xtermGeneric.esc ) ,
	keymap: Object.create( xtermGeneric.keymap ) ,
	handler: Object.create( xtermGeneric.handler ) ,
	support: {
		deltaEscapeSequence: true ,
		"256colors": true ,
		"24bitsColors": undefined ,	// DEPRECATED
		"trueColor": undefined	// maybe, maybe not
	} ,
	colorRegister: require( '../colorScheme/vga.json' )
} ;

