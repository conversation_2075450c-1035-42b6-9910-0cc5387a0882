{"name": "string-kit", "version": "0.19.2", "engines": {"node": ">=14.15.0"}, "description": "A string manipulation toolbox, featuring a string formatter (inspired by sprintf), a variable inspector (output featuring ANSI colors and HTML) and various escape functions (shell argument, regexp, html, etc).", "main": "lib/string.js", "directories": {"test": "test", "bench": "bench"}, "dependencies": {}, "scripts": {"test": "tea-time -R dot"}, "repository": {"type": "git", "url": "https://github.com/cronvel/string-kit.git"}, "keywords": ["string", "manipulation", "format", "sprintf", "printf", "inspect", "color", "debug", "dump", "escape", "shell", "regexp", "html"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/cronvel/string-kit/issues"}, "config": {"tea-time": {"coverDir": ["lib"]}}, "copyright": {"title": "String Kit", "years": [2014, 2021], "owner": "<PERSON><PERSON><PERSON>"}}