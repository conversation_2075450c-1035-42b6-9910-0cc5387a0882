{"name": "terminal-kit", "version": "3.1.2", "description": "256 colors, keys and mouse, input field, progress bars, screen buffer (including 32-bit composition and image loading), text buffer, and many more... Whether you just need colors and styles, build a simple interactive command line tool or a complexe terminal app: this is the absolute terminal lib for Node.js!", "main": "lib/termkit.js", "directories": {"test": "test"}, "engines": {"node": ">=16.13.0"}, "dependencies": {"@cronvel/get-pixels": "^3.4.1", "chroma-js": "^2.4.2", "lazyness": "^1.2.0", "ndarray": "^1.0.19", "nextgen-events": "^1.5.3", "seventh": "^0.9.2", "string-kit": "^0.19.0", "tree-kit": "^0.8.7"}, "scripts": {"test": "tea-time -R dot"}, "repository": {"type": "git", "url": "https://github.com/cronvel/terminal-kit.git"}, "keywords": ["terminal", "console", "ansi", "cli", "xterm", "color", "256 colors", "true color", "style", "input", "input field", "mouse", "gpm", "cursor", "menu", "spinner", "progress bar", "screenbuffer", "textbuffer", "32-bit", "composition", "image", "png", "jpeg", "gif"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/cronvel/terminal-kit/issues"}, "copyright": {"title": "Terminal Kit", "years": [2009, 2022], "owner": "<PERSON><PERSON><PERSON>"}}