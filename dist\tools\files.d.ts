import type { ToolResult } from '../types';
export declare class FileTool {
    private readonly maxFileSize;
    private readonly backupDir;
    read(args: Record<string, unknown>): Promise<ToolResult>;
    write(args: Record<string, unknown>): Promise<ToolResult>;
    create(args: Record<string, unknown>): Promise<ToolResult>;
    delete(args: Record<string, unknown>): Promise<ToolResult>;
    move(args: Record<string, unknown>): Promise<ToolResult>;
    copy(args: Record<string, unknown>): Promise<ToolResult>;
    search(args: Record<string, unknown>): Promise<ToolResult>;
    grep(args: Record<string, unknown>): Promise<ToolResult>;
    private copyDirectory;
    private searchInFile;
    private readFilePartial;
    private isPathSafe;
    private formatBytes;
    createBackup(filePath: string): Promise<string>;
    getFileInfo(filePath: string): Promise<ToolResult>;
    private isTextFile;
    listDirectory(dirPath: string, options?: {
        recursive?: boolean;
        showHidden?: boolean;
    }): Promise<ToolResult>;
    private getDirectoryEntries;
}
//# sourceMappingURL=files.d.ts.map