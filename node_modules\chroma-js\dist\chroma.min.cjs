/**
 * chroma.js - JavaScript library for color conversions
 *
 * Copyright (c) 2011-2024, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * 3. The name <PERSON> may not be used to endorse or promote products
 * derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL GREGOR AISCH OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON> DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * -------------------------------------------------------
 *
 * chroma.js includes colors from colorbrewer2.org, which are released under
 * the following license:
 *
 * Copyright (c) 2002 Cynthia Brewer, Mark Harrower,
 * and The Pennsylvania State University.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific
 * language governing permissions and limitations under the License.
 *
 * ------------------------------------------------------
 *
 * Named colors are taken from X11 Color Names.
 * http://www.w3.org/TR/css3-color/#svg-color
 *
 * @preserve
 */

!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(r="undefined"!=typeof globalThis?globalThis:r||self).chroma=e()}(this,(function(){"use strict";function r(r,e,t){return void 0===e&&(e=0),void 0===t&&(t=1),l(h(e,r),t)}function e(e){e._clipped=!1,e._unclipped=e.slice(0);for(var t=0;t<=3;t++)t<3?((e[t]<0||e[t]>255)&&(e._clipped=!0),e[t]=r(e[t],0,255)):3===t&&(e[t]=r(e[t],0,1));return e}for(var t={},n=0,a=["Boolean","Number","String","Function","Array","Date","RegExp","Undefined","Null"];n<a.length;n+=1){var f=a[n];t["[object "+f+"]"]=f.toLowerCase()}function o(r){return t[Object.prototype.toString.call(r)]||"object"}function u(r,e){return void 0===e&&(e=null),r.length>=3?Array.prototype.slice.call(r):"object"==o(r[0])&&e?e.split("").filter((function(e){return void 0!==r[0][e]})).map((function(e){return r[0][e]})):r[0]}function c(r){if(r.length<2)return null;var e=r.length-1;return"string"==o(r[e])?r[e].toLowerCase():null}var i=Math.PI,l=Math.min,h=Math.max,d=2*i,s=i/3,b=i/180,g=180/i,p={format:{},autodetect:[]},v=function(){for(var r=[],t=arguments.length;t--;)r[t]=arguments[t];var n=this;if("object"===o(r[0])&&r[0].constructor&&r[0].constructor===this.constructor)return r[0];var a=c(r),f=!1;if(!a){f=!0,p.sorted||(p.autodetect=p.autodetect.sort((function(r,e){return e.p-r.p})),p.sorted=!0);for(var u=0,i=p.autodetect;u<i.length;u+=1){var l=i[u];if(a=l.test.apply(l,r))break}}if(!p.format[a])throw new Error("unknown format: "+r);var h=p.format[a].apply(null,f?r:r.slice(0,-1));n._rgb=e(h),3===n._rgb.length&&n._rgb.push(1)};v.prototype.toString=function(){return"function"==o(this.hex)?this.hex():"["+this._rgb.join(",")+"]"};var m=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(m.Color,[null].concat(r)))};m.Color=v,m.version="2.6.0";var y=Math.max;v.prototype.cmyk=function(){return function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"rgb"),n=t[0],a=t[1],f=t[2],o=1-y(n/=255,y(a/=255,f/=255)),c=o<1?1/(1-o):0;return[(1-n-o)*c,(1-a-o)*c,(1-f-o)*c,o]}(this._rgb)},m.cmyk=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["cmyk"])))},p.format.cmyk=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=(r=u(r,"cmyk"))[0],n=r[1],a=r[2],f=r[3],o=r.length>4?r[4]:1;return 1===f?[0,0,0,o]:[t>=1?0:255*(1-t)*(1-f),n>=1?0:255*(1-n)*(1-f),a>=1?0:255*(1-a)*(1-f),o]},p.autodetect.push({p:2,test:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if("array"===o(r=u(r,"cmyk"))&&4===r.length)return"cmyk"}});var w=function(r){return Math.round(100*r)/100},k=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t,n,a=(r=u(r,"rgba"))[0],f=r[1],o=r[2],c=l(a/=255,f/=255,o/=255),i=h(a,f,o),d=(i+c)/2;return i===c?(t=0,n=Number.NaN):t=d<.5?(i-c)/(i+c):(i-c)/(2-i-c),a==i?n=(f-o)/(i-c):f==i?n=2+(o-a)/(i-c):o==i&&(n=4+(a-f)/(i-c)),(n*=60)<0&&(n+=360),r.length>3&&void 0!==r[3]?[n,t,d,r[3]]:[n,t,d]},M=Math.round,N=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"rgba"),n=c(r)||"rgb";return"hsl"==n.substr(0,3)?function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"hsla"),n=c(r)||"lsa";return t[0]=w(t[0]||0),t[1]=w(100*t[1])+"%",t[2]=w(100*t[2])+"%","hsla"===n||t.length>3&&t[3]<1?(t[3]=t.length>3?t[3]:1,n="hsla"):t.length=3,n+"("+t.join(",")+")"}(k(t),n):(t[0]=M(t[0]),t[1]=M(t[1]),t[2]=M(t[2]),("rgba"===n||t.length>3&&t[3]<1)&&(t[3]=t.length>3?t[3]:1,n="rgba"),n+"("+t.slice(0,"rgb"===n?3:4).join(",")+")")},_=Math.round,x=function(){for(var r,e=[],t=arguments.length;t--;)e[t]=arguments[t];var n,a,f,o=(e=u(e,"hsl"))[0],c=e[1],i=e[2];if(0===c)n=a=f=255*i;else{var l=[0,0,0],h=[0,0,0],d=i<.5?i*(1+c):i+c-i*c,s=2*i-d,b=o/360;l[0]=b+1/3,l[1]=b,l[2]=b-1/3;for(var g=0;g<3;g++)l[g]<0&&(l[g]+=1),l[g]>1&&(l[g]-=1),6*l[g]<1?h[g]=s+6*(d-s)*l[g]:2*l[g]<1?h[g]=d:3*l[g]<2?h[g]=s+(d-s)*(2/3-l[g])*6:h[g]=s;n=(r=[_(255*h[0]),_(255*h[1]),_(255*h[2])])[0],a=r[1],f=r[2]}return e.length>3?[n,a,f,e[3]]:[n,a,f,1]},A=/^rgb\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*\)$/,F=/^rgba\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*,\s*([01]|[01]?\.\d+)\)$/,E=/^rgb\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,j=/^rgba\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,q=/^hsl\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,L=/^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,O=Math.round,P=function(r){var e;if(r=r.toLowerCase().trim(),p.format.named)try{return p.format.named(r)}catch(r){}if(e=r.match(A)){for(var t=e.slice(1,4),n=0;n<3;n++)t[n]=+t[n];return t[3]=1,t}if(e=r.match(F)){for(var a=e.slice(1,5),f=0;f<4;f++)a[f]=+a[f];return a}if(e=r.match(E)){for(var o=e.slice(1,4),u=0;u<3;u++)o[u]=O(2.55*o[u]);return o[3]=1,o}if(e=r.match(j)){for(var c=e.slice(1,5),i=0;i<3;i++)c[i]=O(2.55*c[i]);return c[3]=+c[3],c}if(e=r.match(q)){var l=e.slice(1,4);l[1]*=.01,l[2]*=.01;var h=x(l);return h[3]=1,h}if(e=r.match(L)){var d=e.slice(1,4);d[1]*=.01,d[2]*=.01;var s=x(d);return s[3]=+e[4],s}};P.test=function(r){return A.test(r)||F.test(r)||E.test(r)||j.test(r)||q.test(r)||L.test(r)},v.prototype.css=function(r){return N(this._rgb,r)},m.css=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["css"])))},p.format.css=P,p.autodetect.push({p:5,test:function(r){for(var e=[],t=arguments.length-1;t-- >0;)e[t]=arguments[t+1];if(!e.length&&"string"===o(r)&&P.test(r))return"css"}}),p.format.gl=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"rgba");return t[0]*=255,t[1]*=255,t[2]*=255,t},m.gl=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["gl"])))},v.prototype.gl=function(){var r=this._rgb;return[r[0]/255,r[1]/255,r[2]/255,r[3]]};var G=Math.floor;v.prototype.hcg=function(){return function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t,n=u(r,"rgb"),a=n[0],f=n[1],o=n[2],c=l(a,f,o),i=h(a,f,o),d=i-c,s=100*d/255,b=c/(255-d)*100;return 0===d?t=Number.NaN:(a===i&&(t=(f-o)/d),f===i&&(t=2+(o-a)/d),o===i&&(t=4+(a-f)/d),(t*=60)<0&&(t+=360)),[t,s,b]}(this._rgb)},m.hcg=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["hcg"])))},p.format.hcg=function(){for(var r,e,t,n,a,f,o=[],c=arguments.length;c--;)o[c]=arguments[c];var i,l,h,d=(o=u(o,"hcg"))[0],s=o[1],b=o[2];b*=255;var g=255*s;if(0===s)i=l=h=b;else{360===d&&(d=0),d>360&&(d-=360),d<0&&(d+=360);var p=G(d/=60),v=d-p,m=b*(1-s),y=m+g*(1-v),w=m+g*v,k=m+g;switch(p){case 0:i=(r=[k,w,m])[0],l=r[1],h=r[2];break;case 1:i=(e=[y,k,m])[0],l=e[1],h=e[2];break;case 2:i=(t=[m,k,w])[0],l=t[1],h=t[2];break;case 3:i=(n=[m,y,k])[0],l=n[1],h=n[2];break;case 4:i=(a=[w,m,k])[0],l=a[1],h=a[2];break;case 5:i=(f=[k,m,y])[0],l=f[1],h=f[2]}}return[i,l,h,o.length>3?o[3]:1]},p.autodetect.push({p:1,test:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if("array"===o(r=u(r,"hcg"))&&3===r.length)return"hcg"}});var B=/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,C=/^#?([A-Fa-f0-9]{8}|[A-Fa-f0-9]{4})$/,R=function(r){if(r.match(B)){4!==r.length&&7!==r.length||(r=r.substr(1)),3===r.length&&(r=(r=r.split(""))[0]+r[0]+r[1]+r[1]+r[2]+r[2]);var e=parseInt(r,16);return[e>>16,e>>8&255,255&e,1]}if(r.match(C)){5!==r.length&&9!==r.length||(r=r.substr(1)),4===r.length&&(r=(r=r.split(""))[0]+r[0]+r[1]+r[1]+r[2]+r[2]+r[3]+r[3]);var t=parseInt(r,16);return[t>>24&255,t>>16&255,t>>8&255,Math.round((255&t)/255*100)/100]}throw new Error("unknown hex color: "+r)},S=Math.round,$=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"rgba"),n=t[0],a=t[1],f=t[2],o=t[3],i=c(r)||"auto";void 0===o&&(o=1),"auto"===i&&(i=o<1?"rgba":"rgb");var l="000000"+((n=S(n))<<16|(a=S(a))<<8|(f=S(f))).toString(16);l=l.substr(l.length-6);var h="0"+S(255*o).toString(16);switch(h=h.substr(h.length-2),i.toLowerCase()){case"rgba":return"#"+l+h;case"argb":return"#"+h+l;default:return"#"+l}};v.prototype.hex=function(r){return $(this._rgb,r)},m.hex=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["hex"])))},p.format.hex=R,p.autodetect.push({p:4,test:function(r){for(var e=[],t=arguments.length-1;t-- >0;)e[t]=arguments[t+1];if(!e.length&&"string"===o(r)&&[3,4,5,6,7,8,9].indexOf(r.length)>=0)return"hex"}});var Y=Math.cos,z=Math.min,I=Math.sqrt,U=Math.acos;v.prototype.hsi=function(){return function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t,n=u(r,"rgb"),a=n[0],f=n[1],o=n[2],c=z(a/=255,f/=255,o/=255),i=(a+f+o)/3,l=i>0?1-c/i:0;return 0===l?t=NaN:(t=(a-f+(a-o))/2,t/=I((a-f)*(a-f)+(a-o)*(f-o)),t=U(t),o>f&&(t=d-t),t/=d),[360*t,l,i]}(this._rgb)},m.hsi=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["hsi"])))},p.format.hsi=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];var n,a,f,o=(e=u(e,"hsi"))[0],c=e[1],i=e[2];return isNaN(o)&&(o=0),isNaN(c)&&(c=0),o>360&&(o-=360),o<0&&(o+=360),(o/=360)<1/3?a=1-((f=(1-c)/3)+(n=(1+c*Y(d*o)/Y(s-d*o))/3)):o<2/3?f=1-((n=(1-c)/3)+(a=(1+c*Y(d*(o-=1/3))/Y(s-d*o))/3)):n=1-((a=(1-c)/3)+(f=(1+c*Y(d*(o-=2/3))/Y(s-d*o))/3)),[255*(n=r(i*n*3)),255*(a=r(i*a*3)),255*(f=r(i*f*3)),e.length>3?e[3]:1]},p.autodetect.push({p:2,test:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if("array"===o(r=u(r,"hsi"))&&3===r.length)return"hsi"}}),v.prototype.hsl=function(){return k(this._rgb)},m.hsl=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["hsl"])))},p.format.hsl=x,p.autodetect.push({p:2,test:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if("array"===o(r=u(r,"hsl"))&&3===r.length)return"hsl"}});var V=Math.floor,X=Math.min,D=Math.max;v.prototype.hsv=function(){return function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t,n,a,f=(r=u(r,"rgb"))[0],o=r[1],c=r[2],i=X(f,o,c),l=D(f,o,c),h=l-i;return a=l/255,0===l?(t=Number.NaN,n=0):(n=h/l,f===l&&(t=(o-c)/h),o===l&&(t=2+(c-f)/h),c===l&&(t=4+(f-o)/h),(t*=60)<0&&(t+=360)),[t,n,a]}(this._rgb)},m.hsv=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["hsv"])))},p.format.hsv=function(){for(var r,e,t,n,a,f,o=[],c=arguments.length;c--;)o[c]=arguments[c];var i,l,h,d=(o=u(o,"hsv"))[0],s=o[1],b=o[2];if(b*=255,0===s)i=l=h=b;else{360===d&&(d=0),d>360&&(d-=360),d<0&&(d+=360);var g=V(d/=60),p=d-g,v=b*(1-s),m=b*(1-s*p),y=b*(1-s*(1-p));switch(g){case 0:i=(r=[b,y,v])[0],l=r[1],h=r[2];break;case 1:i=(e=[m,b,v])[0],l=e[1],h=e[2];break;case 2:i=(t=[v,b,y])[0],l=t[1],h=t[2];break;case 3:i=(n=[v,m,b])[0],l=n[1],h=n[2];break;case 4:i=(a=[y,v,b])[0],l=a[1],h=a[2];break;case 5:i=(f=[b,v,m])[0],l=f[1],h=f[2]}}return[i,l,h,o.length>3?o[3]:1]},p.autodetect.push({p:2,test:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if("array"===o(r=u(r,"hsv"))&&3===r.length)return"hsv"}});var T=18,H=.95047,J=1,K=1.08883,Q=.137931034,W=.206896552,Z=.12841855,rr=.008856452,er=Math.pow,tr=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t,n,a,f=(r=u(r,"lab"))[0],o=r[1],c=r[2];return n=(f+16)/116,t=isNaN(o)?n:n+o/500,a=isNaN(c)?n:n-c/200,n=J*ar(n),t=H*ar(t),a=K*ar(a),[nr(3.2404542*t-1.5371385*n-.4985314*a),nr(-.969266*t+1.8760108*n+.041556*a),nr(.0556434*t-.2040259*n+1.0572252*a),r.length>3?r[3]:1]},nr=function(r){return 255*(r<=.00304?12.92*r:1.055*er(r,1/2.4)-.055)},ar=function(r){return r>W?r*r*r:Z*(r-Q)},fr=Math.pow,or=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"rgb"),n=t[0],a=t[1],f=t[2],o=ir(n,a,f),c=o[0],i=o[1],l=116*i-16;return[l<0?0:l,500*(c-i),200*(i-o[2])]},ur=function(r){return(r/=255)<=.04045?r/12.92:fr((r+.055)/1.055,2.4)},cr=function(r){return r>rr?fr(r,1/3):r/Z+Q},ir=function(r,e,t){return r=ur(r),e=ur(e),t=ur(t),[cr((.4124564*r+.3575761*e+.1804375*t)/H),cr((.2126729*r+.7151522*e+.072175*t)/J),cr((.0193339*r+.119192*e+.9503041*t)/K)]};v.prototype.lab=function(){return or(this._rgb)},m.lab=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["lab"])))},p.format.lab=tr,p.autodetect.push({p:2,test:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if("array"===o(r=u(r,"lab"))&&3===r.length)return"lab"}});var lr=Math.sin,hr=Math.cos,dr=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"lch"),n=t[0],a=t[1],f=t[2];return isNaN(f)&&(f=0),[n,hr(f*=b)*a,lr(f)*a]},sr=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=(r=u(r,"lch"))[0],n=r[1],a=r[2],f=dr(t,n,a),o=f[0],c=f[1],i=f[2],l=tr(o,c,i);return[l[0],l[1],l[2],r.length>3?r[3]:1]},br=Math.sqrt,gr=Math.atan2,pr=Math.round,vr=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"lab"),n=t[0],a=t[1],f=t[2],o=br(a*a+f*f),c=(gr(f,a)*g+360)%360;return 0===pr(1e4*o)&&(c=Number.NaN),[n,o,c]},mr=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"rgb"),n=t[0],a=t[1],f=t[2],o=or(n,a,f),c=o[0],i=o[1],l=o[2];return vr(c,i,l)};v.prototype.lch=function(){return mr(this._rgb)},v.prototype.hcl=function(){return mr(this._rgb).reverse()},m.lch=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["lch"])))},m.hcl=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["hcl"])))},p.format.lch=sr,p.format.hcl=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"hcl").reverse();return sr.apply(void 0,t)},["lch","hcl"].forEach((function(r){return p.autodetect.push({p:2,test:function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];if("array"===o(e=u(e,r))&&3===e.length)return r}})}));var yr={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",laserlemon:"#ffff54",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrod:"#fafad2",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",maroon2:"#7f0000",maroon3:"#b03060",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",purple2:"#7f007f",purple3:"#a020f0",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};v.prototype.name=function(){for(var r=$(this._rgb,"rgb"),e=0,t=Object.keys(yr);e<t.length;e+=1){var n=t[e];if(yr[n]===r)return n.toLowerCase()}return r},p.format.named=function(r){if(r=r.toLowerCase(),yr[r])return R(yr[r]);throw new Error("unknown color name: "+r)},p.autodetect.push({p:5,test:function(r){for(var e=[],t=arguments.length-1;t-- >0;)e[t]=arguments[t+1];if(!e.length&&"string"===o(r)&&yr[r.toLowerCase()])return"named"}});v.prototype.num=function(){return function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"rgb");return(t[0]<<16)+(t[1]<<8)+t[2]}(this._rgb)},m.num=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["num"])))},p.format.num=function(r){if("number"==o(r)&&r>=0&&r<=16777215)return[r>>16,r>>8&255,255&r,1];throw new Error("unknown num color: "+r)},p.autodetect.push({p:5,test:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if(1===r.length&&"number"===o(r[0])&&r[0]>=0&&r[0]<=16777215)return"num"}});var wr=Math.round;v.prototype.rgb=function(r){return void 0===r&&(r=!0),!1===r?this._rgb.slice(0,3):this._rgb.slice(0,3).map(wr)},v.prototype.rgba=function(r){return void 0===r&&(r=!0),this._rgb.slice(0,4).map((function(e,t){return t<3?!1===r?e:wr(e):e}))},m.rgb=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["rgb"])))},p.format.rgb=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"rgba");return void 0===t[3]&&(t[3]=1),t},p.autodetect.push({p:3,test:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if("array"===o(r=u(r,"rgba"))&&(3===r.length||4===r.length&&"number"==o(r[3])&&r[3]>=0&&r[3]<=1))return"rgb"}});var kr=Math.log,Mr=function(r){var e,t,n,a=r/100;return a<66?(e=255,t=a<6?0:-155.25485562709179-.44596950469579133*(t=a-2)+104.49216199393888*kr(t),n=a<20?0:.8274096064007395*(n=a-10)-254.76935184120902+115.67994401066147*kr(n)):(e=351.97690566805693+.114206453784165*(e=a-55)-40.25366309332127*kr(e),t=325.4494125711974+.07943456536662342*(t=a-50)-28.0852963507957*kr(t),n=255),[e,t,n,1]},Nr=Math.round;v.prototype.temp=v.prototype.kelvin=v.prototype.temperature=function(){return function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];for(var t,n=u(r,"rgb"),a=n[0],f=n[2],o=1e3,c=4e4;c-o>.4;){var i=Mr(t=.5*(c+o));i[2]/i[0]>=f/a?c=t:o=t}return Nr(t)}(this._rgb)},m.temp=m.kelvin=m.temperature=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["temp"])))},p.format.temp=p.format.kelvin=p.format.temperature=Mr;var _r=Math.pow,xr=Math.sign,Ar=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=(r=u(r,"lab"))[0],n=r[1],a=r[2],f=_r(t+.3963377774*n+.2158037573*a,3),o=_r(t-.1055613458*n-.0638541728*a,3),c=_r(t-.0894841775*n-1.291485548*a,3);return[255*Fr(4.0767416621*f-3.3077115913*o+.2309699292*c),255*Fr(-1.2684380046*f+2.6097574011*o-.3413193965*c),255*Fr(-.0041960863*f-.7034186147*o+1.707614701*c),r.length>3?r[3]:1]};function Fr(r){var e=Math.abs(r);return e>.0031308?(xr(r)||1)*(1.055*_r(e,1/2.4)-.055):12.92*r}var Er=Math.cbrt,jr=Math.pow,qr=Math.sign,Lr=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"rgb"),n=t[0],a=t[1],f=t[2],o=[Or(n/255),Or(a/255),Or(f/255)],c=o[0],i=o[1],l=o[2],h=Er(.4122214708*c+.5363325363*i+.0514459929*l),d=Er(.2119034982*c+.6806995451*i+.1073969566*l),s=Er(.0883024619*c+.2817188376*i+.6299787005*l);return[.2104542553*h+.793617785*d-.0040720468*s,1.9779984951*h-2.428592205*d+.4505937099*s,.0259040371*h+.7827717662*d-.808675766*s]};function Or(r){var e=Math.abs(r);return e<.04045?r/12.92:(qr(r)||1)*jr((e+.055)/1.055,2.4)}v.prototype.oklab=function(){return Lr(this._rgb)},m.oklab=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["oklab"])))},p.format.oklab=Ar,p.autodetect.push({p:3,test:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if("array"===o(r=u(r,"oklab"))&&3===r.length)return"oklab"}});v.prototype.oklch=function(){return function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=u(r,"rgb"),n=t[0],a=t[1],f=t[2],o=Lr(n,a,f),c=o[0],i=o[1],l=o[2];return vr(c,i,l)}(this._rgb)},m.oklch=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];return new(Function.prototype.bind.apply(v,[null].concat(r,["oklch"])))},p.format.oklch=function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];var t=(r=u(r,"lch"))[0],n=r[1],a=r[2],f=dr(t,n,a),o=f[0],c=f[1],i=f[2],l=Ar(o,c,i);return[l[0],l[1],l[2],r.length>3?r[3]:1]},p.autodetect.push({p:3,test:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];if("array"===o(r=u(r,"oklch"))&&3===r.length)return"oklch"}}),v.prototype.alpha=function(r,e){return void 0===e&&(e=!1),void 0!==r&&"number"===o(r)?e?(this._rgb[3]=r,this):new v([this._rgb[0],this._rgb[1],this._rgb[2],r],"rgb"):this._rgb[3]},v.prototype.clipped=function(){return this._rgb._clipped||!1},v.prototype.darken=function(r){void 0===r&&(r=1);var e=this.lab();return e[0]-=T*r,new v(e,"lab").alpha(this.alpha(),!0)},v.prototype.brighten=function(r){return void 0===r&&(r=1),this.darken(-r)},v.prototype.darker=v.prototype.darken,v.prototype.brighter=v.prototype.brighten,v.prototype.get=function(r){var e=r.split("."),t=e[0],n=e[1],a=this[t]();if(n){var f=t.indexOf(n)-("ok"===t.substr(0,2)?2:0);if(f>-1)return a[f];throw new Error("unknown channel "+n+" in mode "+t)}return a};var Pr=Math.pow;v.prototype.luminance=function(r,e){if(void 0===e&&(e="rgb"),void 0!==r&&"number"===o(r)){if(0===r)return new v([0,0,0,this._rgb[3]],"rgb");if(1===r)return new v([255,255,255,this._rgb[3]],"rgb");var t=this.luminance(),n=20,a=function(t,f){var o=t.interpolate(f,.5,e),u=o.luminance();return Math.abs(r-u)<1e-7||!n--?o:u>r?a(t,o):a(o,f)},f=(t>r?a(new v([0,0,0]),this):a(this,new v([255,255,255]))).rgb();return new v(f.concat([this._rgb[3]]))}return Gr.apply(void 0,this._rgb.slice(0,3))};var Gr=function(r,e,t){return.2126*(r=Br(r))+.7152*(e=Br(e))+.0722*(t=Br(t))},Br=function(r){return(r/=255)<=.03928?r/12.92:Pr((r+.055)/1.055,2.4)},Cr={};function Rr(r,e,t){void 0===t&&(t=.5);for(var n=[],a=arguments.length-3;a-- >0;)n[a]=arguments[a+3];var f=n[0]||"lrgb";if(Cr[f]||n.length||(f=Object.keys(Cr)[0]),!Cr[f])throw new Error("interpolation mode "+f+" is not defined");return"object"!==o(r)&&(r=new v(r)),"object"!==o(e)&&(e=new v(e)),Cr[f](r,e,t).alpha(r.alpha()+t*(e.alpha()-r.alpha()))}v.prototype.mix=v.prototype.interpolate=function(r,e){void 0===e&&(e=.5);for(var t=[],n=arguments.length-2;n-- >0;)t[n]=arguments[n+2];return Rr.apply(void 0,[this,r,e].concat(t))},v.prototype.premultiply=function(r){void 0===r&&(r=!1);var e=this._rgb,t=e[3];return r?(this._rgb=[e[0]*t,e[1]*t,e[2]*t,t],this):new v([e[0]*t,e[1]*t,e[2]*t,t],"rgb")},v.prototype.saturate=function(r){void 0===r&&(r=1);var e=this.lch();return e[1]+=T*r,e[1]<0&&(e[1]=0),new v(e,"lch").alpha(this.alpha(),!0)},v.prototype.desaturate=function(r){return void 0===r&&(r=1),this.saturate(-r)},v.prototype.set=function(r,e,t){void 0===t&&(t=!1);var n=r.split("."),a=n[0],f=n[1],u=this[a]();if(f){var c=a.indexOf(f)-("ok"===a.substr(0,2)?2:0);if(c>-1){if("string"==o(e))switch(e.charAt(0)){case"+":case"-":u[c]+=+e;break;case"*":u[c]*=+e.substr(1);break;case"/":u[c]/=+e.substr(1);break;default:u[c]=+e}else{if("number"!==o(e))throw new Error("unsupported value for Color.set");u[c]=e}var i=new v(u,a);return t?(this._rgb=i._rgb,this):i}throw new Error("unknown channel "+f+" in mode "+a)}return u},v.prototype.tint=function(r){void 0===r&&(r=.5);for(var e=[],t=arguments.length-1;t-- >0;)e[t]=arguments[t+1];return Rr.apply(void 0,[this,"white",r].concat(e))},v.prototype.shade=function(r){void 0===r&&(r=.5);for(var e=[],t=arguments.length-1;t-- >0;)e[t]=arguments[t+1];return Rr.apply(void 0,[this,"black",r].concat(e))};Cr.rgb=function(r,e,t){var n=r._rgb,a=e._rgb;return new v(n[0]+t*(a[0]-n[0]),n[1]+t*(a[1]-n[1]),n[2]+t*(a[2]-n[2]),"rgb")};var Sr=Math.sqrt,$r=Math.pow;Cr.lrgb=function(r,e,t){var n=r._rgb,a=n[0],f=n[1],o=n[2],u=e._rgb,c=u[0],i=u[1],l=u[2];return new v(Sr($r(a,2)*(1-t)+$r(c,2)*t),Sr($r(f,2)*(1-t)+$r(i,2)*t),Sr($r(o,2)*(1-t)+$r(l,2)*t),"rgb")};function Yr(r,e,t,n){var a,f,o,u,c,i,l,h,d,s,b,g,p;return"hsl"===n?(o=r.hsl(),u=e.hsl()):"hsv"===n?(o=r.hsv(),u=e.hsv()):"hcg"===n?(o=r.hcg(),u=e.hcg()):"hsi"===n?(o=r.hsi(),u=e.hsi()):"lch"===n||"hcl"===n?(n="hcl",o=r.hcl(),u=e.hcl()):"oklch"===n&&(o=r.oklch().reverse(),u=e.oklch().reverse()),"h"!==n.substr(0,1)&&"oklch"!==n||(c=(a=o)[0],l=a[1],d=a[2],i=(f=u)[0],h=f[1],s=f[2]),isNaN(c)||isNaN(i)?isNaN(c)?isNaN(i)?g=Number.NaN:(g=i,1!=d&&0!=d||"hsv"==n||(b=h)):(g=c,1!=s&&0!=s||"hsv"==n||(b=l)):g=c+t*(i>c&&i-c>180?i-(c+360):i<c&&c-i>180?i+360-c:i-c),void 0===b&&(b=l+t*(h-l)),p=d+t*(s-d),new v("oklch"===n?[p,b,g]:[g,b,p],n)}Cr.lab=function(r,e,t){var n=r.lab(),a=e.lab();return new v(n[0]+t*(a[0]-n[0]),n[1]+t*(a[1]-n[1]),n[2]+t*(a[2]-n[2]),"lab")};var zr=function(r,e,t){return Yr(r,e,t,"lch")};Cr.lch=zr,Cr.hcl=zr;Cr.num=function(r,e,t){var n=r.num(),a=e.num();return new v(n+t*(a-n),"num")};Cr.hcg=function(r,e,t){return Yr(r,e,t,"hcg")};Cr.hsi=function(r,e,t){return Yr(r,e,t,"hsi")};Cr.hsl=function(r,e,t){return Yr(r,e,t,"hsl")};Cr.hsv=function(r,e,t){return Yr(r,e,t,"hsv")};Cr.oklab=function(r,e,t){var n=r.oklab(),a=e.oklab();return new v(n[0]+t*(a[0]-n[0]),n[1]+t*(a[1]-n[1]),n[2]+t*(a[2]-n[2]),"oklab")};Cr.oklch=function(r,e,t){return Yr(r,e,t,"oklch")};var Ir=Math.pow,Ur=Math.sqrt,Vr=Math.PI,Xr=Math.cos,Dr=Math.sin,Tr=Math.atan2;var Hr=function(r,t){for(var n=r.length,a=[0,0,0,0],f=0;f<r.length;f++){var o=r[f],u=t[f]/n,c=o._rgb;a[0]+=Ir(c[0],2)*u,a[1]+=Ir(c[1],2)*u,a[2]+=Ir(c[2],2)*u,a[3]+=c[3]*u}return a[0]=Ur(a[0]),a[1]=Ur(a[1]),a[2]=Ur(a[2]),a[3]>.9999999&&(a[3]=1),new v(e(a))},Jr=Math.pow;function Kr(e){var t="rgb",n=m("#ccc"),a=0,f=[0,1],u=[],c=[0,0],i=!1,l=[],h=!1,d=0,s=1,b=!1,g={},p=!0,v=1,y=function(r){if((r=r||["#fff","#000"])&&"string"===o(r)&&m.brewer&&m.brewer[r.toLowerCase()]&&(r=m.brewer[r.toLowerCase()]),"array"===o(r)){1===r.length&&(r=[r[0],r[0]]),r=r.slice(0);for(var e=0;e<r.length;e++)r[e]=m(r[e]);u.length=0;for(var t=0;t<r.length;t++)u.push(t/(r.length-1))}return N(),l=r},w=function(r){return r},k=function(r){return r},M=function(e,a){var f,h;if(null==a&&(a=!1),isNaN(e)||null===e)return n;if(a)h=e;else if(i&&i.length>2){var b=function(r){if(null!=i){for(var e=i.length-1,t=0;t<e&&r>=i[t];)t++;return t-1}return 0}(e);h=b/(i.length-2)}else h=s!==d?(e-d)/(s-d):1;h=k(h),a||(h=w(h)),1!==v&&(h=Jr(h,v)),h=r(h=c[0]+h*(1-c[0]-c[1]),0,1);var y=Math.floor(1e4*h);if(p&&g[y])f=g[y];else{if("array"===o(l))for(var M=0;M<u.length;M++){var N=u[M];if(h<=N){f=l[M];break}if(h>=N&&M===u.length-1){f=l[M];break}if(h>N&&h<u[M+1]){h=(h-N)/(u[M+1]-N),f=m.interpolate(l[M],l[M+1],h,t);break}}else"function"===o(l)&&(f=l(h));p&&(g[y]=f)}return f},N=function(){return g={}};y(e);var _=function(r){var e=m(M(r));return h&&e[h]?e[h]():e};return _.classes=function(r){if(null!=r){if("array"===o(r))i=r,f=[r[0],r[r.length-1]];else{var e=m.analyze(f);i=0===r?[e.min,e.max]:m.limits(e,"e",r)}return _}return i},_.domain=function(r){if(!arguments.length)return f;d=r[0],s=r[r.length-1],u=[];var e=l.length;if(r.length===e&&d!==s)for(var t=0,n=Array.from(r);t<n.length;t+=1){var a=n[t];u.push((a-d)/(s-d))}else{for(var o=0;o<e;o++)u.push(o/(e-1));if(r.length>2){var c=r.map((function(e,t){return t/(r.length-1)})),i=r.map((function(r){return(r-d)/(s-d)}));i.every((function(r,e){return c[e]===r}))||(k=function(r){if(r<=0||r>=1)return r;for(var e=0;r>=i[e+1];)e++;var t=(r-i[e])/(i[e+1]-i[e]);return c[e]+t*(c[e+1]-c[e])})}}return f=[d,s],_},_.mode=function(r){return arguments.length?(t=r,N(),_):t},_.range=function(r,e){return y(r),_},_.out=function(r){return h=r,_},_.spread=function(r){return arguments.length?(a=r,_):a},_.correctLightness=function(r){return null==r&&(r=!0),b=r,N(),w=b?function(r){for(var e=M(0,!0).lab()[0],t=M(1,!0).lab()[0],n=e>t,a=M(r,!0).lab()[0],f=e+(t-e)*r,o=a-f,u=0,c=1,i=20;Math.abs(o)>.01&&i-- >0;)n&&(o*=-1),o<0?(u=r,r+=.5*(c-r)):(c=r,r+=.5*(u-r)),a=M(r,!0).lab()[0],o=a-f;return r}:function(r){return r},_},_.padding=function(r){return null!=r?("number"===o(r)&&(r=[r,r]),c=r,_):c},_.colors=function(r,t){arguments.length<2&&(t="hex");var n=[];if(0===arguments.length)n=l.slice(0);else if(1===r)n=[_(.5)];else if(r>1){var a=f[0],o=f[1]-a;n=function(r,e){for(var t=[],n=r<e,a=e,f=r;n?f<a:f>a;n?f++:f--)t.push(f);return t}(0,r).map((function(e){return _(a+e/(r-1)*o)}))}else{e=[];var u=[];if(i&&i.length>2)for(var c=1,h=i.length,d=1<=h;d?c<h:c>h;d?c++:c--)u.push(.5*(i[c-1]+i[c]));else u=f;n=u.map((function(r){return _(r)}))}return m[t]&&(n=n.map((function(r){return r[t]()}))),n},_.cache=function(r){return null!=r?(p=r,_):p},_.gamma=function(r){return null!=r?(v=r,_):v},_.nodata=function(r){return null!=r?(n=m(r),_):n},_}var Qr=function(r,e,t){if(!Qr[t])throw new Error("unknown blend mode "+t);return Qr[t](r,e)},Wr=function(r){return function(e,t){var n=m(t).rgb(),a=m(e).rgb();return m.rgb(r(n,a))}},Zr=function(r){return function(e,t){var n=[];return n[0]=r(e[0],t[0]),n[1]=r(e[1],t[1]),n[2]=r(e[2],t[2]),n}};Qr.normal=Wr(Zr((function(r){return r}))),Qr.multiply=Wr(Zr((function(r,e){return r*e/255}))),Qr.screen=Wr(Zr((function(r,e){return 255*(1-(1-r/255)*(1-e/255))}))),Qr.overlay=Wr(Zr((function(r,e){return e<128?2*r*e/255:255*(1-2*(1-r/255)*(1-e/255))}))),Qr.darken=Wr(Zr((function(r,e){return r>e?e:r}))),Qr.lighten=Wr(Zr((function(r,e){return r>e?r:e}))),Qr.dodge=Wr(Zr((function(r,e){return 255===r||(r=e/255*255/(1-r/255))>255?255:r}))),Qr.burn=Wr(Zr((function(r,e){return 255*(1-(1-e/255)/(r/255))})));var re=Math.pow,ee=Math.sin,te=Math.cos;var ne=Math.floor,ae=Math.random;var fe=Math.log,oe=Math.pow,ue=Math.floor,ce=Math.abs;function ie(r,e){void 0===e&&(e=null);var t={min:Number.MAX_VALUE,max:-1*Number.MAX_VALUE,sum:0,values:[],count:0};return"object"===o(r)&&(r=Object.values(r)),r.forEach((function(r){e&&"object"===o(r)&&(r=r[e]),null==r||isNaN(r)||(t.values.push(r),t.sum+=r,r<t.min&&(t.min=r),r>t.max&&(t.max=r),t.count+=1)})),t.domain=[t.min,t.max],t.limits=function(r,e){return le(t,r,e)},t}function le(r,e,t){void 0===e&&(e="equal"),void 0===t&&(t=7),"array"==o(r)&&(r=ie(r));var n=r.min,a=r.max,f=r.values.sort((function(r,e){return r-e}));if(1===t)return[n,a];var u=[];if("c"===e.substr(0,1)&&(u.push(n),u.push(a)),"e"===e.substr(0,1)){u.push(n);for(var c=1;c<t;c++)u.push(n+c/t*(a-n));u.push(a)}else if("l"===e.substr(0,1)){if(n<=0)throw new Error("Logarithmic scales are only possible for values > 0");var i=Math.LOG10E*fe(n),l=Math.LOG10E*fe(a);u.push(n);for(var h=1;h<t;h++)u.push(oe(10,i+h/t*(l-i)));u.push(a)}else if("q"===e.substr(0,1)){u.push(n);for(var d=1;d<t;d++){var s=(f.length-1)*d/t,b=ue(s);if(b===s)u.push(f[b]);else{var g=s-b;u.push(f[b]*(1-g)+f[b+1]*g)}}u.push(a)}else if("k"===e.substr(0,1)){var p,v=f.length,m=new Array(v),y=new Array(t),w=!0,k=0,M=null;(M=[]).push(n);for(var N=1;N<t;N++)M.push(n+N/t*(a-n));for(M.push(a);w;){for(var _=0;_<t;_++)y[_]=0;for(var x=0;x<v;x++)for(var A=f[x],F=Number.MAX_VALUE,E=void 0,j=0;j<t;j++){var q=ce(M[j]-A);q<F&&(F=q,E=j),y[E]++,m[x]=E}for(var L=new Array(t),O=0;O<t;O++)L[O]=null;for(var P=0;P<v;P++)null===L[p=m[P]]?L[p]=f[P]:L[p]+=f[P];for(var G=0;G<t;G++)L[G]*=1/y[G];w=!1;for(var B=0;B<t;B++)if(L[B]!==M[B]){w=!0;break}M=L,++k>200&&(w=!1)}for(var C={},R=0;R<t;R++)C[R]=[];for(var S=0;S<v;S++)C[p=m[S]].push(f[S]);for(var $=[],Y=0;Y<t;Y++)$.push(C[Y][0]),$.push(C[Y][C[Y].length-1]);$=$.sort((function(r,e){return r-e})),u.push($[0]);for(var z=1;z<$.length;z+=2){var I=$[z];isNaN(I)||-1!==u.indexOf(I)||u.push(I)}}return u}var he=Math.sqrt,de=Math.pow,se=Math.min,be=Math.max,ge=Math.atan2,pe=Math.abs,ve=Math.cos,me=Math.sin,ye=Math.exp,we=Math.PI;for(var ke={cool:function(){return Kr([m.hsl(180,1,.9),m.hsl(250,.7,.4)])},hot:function(){return Kr(["#000","#f00","#ff0","#fff"]).mode("rgb")}},Me={OrRd:["#fff7ec","#fee8c8","#fdd49e","#fdbb84","#fc8d59","#ef6548","#d7301f","#b30000","#7f0000"],PuBu:["#fff7fb","#ece7f2","#d0d1e6","#a6bddb","#74a9cf","#3690c0","#0570b0","#045a8d","#023858"],BuPu:["#f7fcfd","#e0ecf4","#bfd3e6","#9ebcda","#8c96c6","#8c6bb1","#88419d","#810f7c","#4d004b"],Oranges:["#fff5eb","#fee6ce","#fdd0a2","#fdae6b","#fd8d3c","#f16913","#d94801","#a63603","#7f2704"],BuGn:["#f7fcfd","#e5f5f9","#ccece6","#99d8c9","#66c2a4","#41ae76","#238b45","#006d2c","#00441b"],YlOrBr:["#ffffe5","#fff7bc","#fee391","#fec44f","#fe9929","#ec7014","#cc4c02","#993404","#662506"],YlGn:["#ffffe5","#f7fcb9","#d9f0a3","#addd8e","#78c679","#41ab5d","#238443","#006837","#004529"],Reds:["#fff5f0","#fee0d2","#fcbba1","#fc9272","#fb6a4a","#ef3b2c","#cb181d","#a50f15","#67000d"],RdPu:["#fff7f3","#fde0dd","#fcc5c0","#fa9fb5","#f768a1","#dd3497","#ae017e","#7a0177","#49006a"],Greens:["#f7fcf5","#e5f5e0","#c7e9c0","#a1d99b","#74c476","#41ab5d","#238b45","#006d2c","#00441b"],YlGnBu:["#ffffd9","#edf8b1","#c7e9b4","#7fcdbb","#41b6c4","#1d91c0","#225ea8","#253494","#081d58"],Purples:["#fcfbfd","#efedf5","#dadaeb","#bcbddc","#9e9ac8","#807dba","#6a51a3","#54278f","#3f007d"],GnBu:["#f7fcf0","#e0f3db","#ccebc5","#a8ddb5","#7bccc4","#4eb3d3","#2b8cbe","#0868ac","#084081"],Greys:["#ffffff","#f0f0f0","#d9d9d9","#bdbdbd","#969696","#737373","#525252","#252525","#000000"],YlOrRd:["#ffffcc","#ffeda0","#fed976","#feb24c","#fd8d3c","#fc4e2a","#e31a1c","#bd0026","#800026"],PuRd:["#f7f4f9","#e7e1ef","#d4b9da","#c994c7","#df65b0","#e7298a","#ce1256","#980043","#67001f"],Blues:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],PuBuGn:["#fff7fb","#ece2f0","#d0d1e6","#a6bddb","#67a9cf","#3690c0","#02818a","#016c59","#014636"],Viridis:["#440154","#482777","#3f4a8a","#31678e","#26838f","#1f9d8a","#6cce5a","#b6de2b","#fee825"],Spectral:["#9e0142","#d53e4f","#f46d43","#fdae61","#fee08b","#ffffbf","#e6f598","#abdda4","#66c2a5","#3288bd","#5e4fa2"],RdYlGn:["#a50026","#d73027","#f46d43","#fdae61","#fee08b","#ffffbf","#d9ef8b","#a6d96a","#66bd63","#1a9850","#006837"],RdBu:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#f7f7f7","#d1e5f0","#92c5de","#4393c3","#2166ac","#053061"],PiYG:["#8e0152","#c51b7d","#de77ae","#f1b6da","#fde0ef","#f7f7f7","#e6f5d0","#b8e186","#7fbc41","#4d9221","#276419"],PRGn:["#40004b","#762a83","#9970ab","#c2a5cf","#e7d4e8","#f7f7f7","#d9f0d3","#a6dba0","#5aae61","#1b7837","#00441b"],RdYlBu:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#ffffbf","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"],BrBG:["#543005","#8c510a","#bf812d","#dfc27d","#f6e8c3","#f5f5f5","#c7eae5","#80cdc1","#35978f","#01665e","#003c30"],RdGy:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#ffffff","#e0e0e0","#bababa","#878787","#4d4d4d","#1a1a1a"],PuOr:["#7f3b08","#b35806","#e08214","#fdb863","#fee0b6","#f7f7f7","#d8daeb","#b2abd2","#8073ac","#542788","#2d004b"],Set2:["#66c2a5","#fc8d62","#8da0cb","#e78ac3","#a6d854","#ffd92f","#e5c494","#b3b3b3"],Accent:["#7fc97f","#beaed4","#fdc086","#ffff99","#386cb0","#f0027f","#bf5b17","#666666"],Set1:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],Set3:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd","#ccebc5","#ffed6f"],Dark2:["#1b9e77","#d95f02","#7570b3","#e7298a","#66a61e","#e6ab02","#a6761d","#666666"],Paired:["#a6cee3","#1f78b4","#b2df8a","#33a02c","#fb9a99","#e31a1c","#fdbf6f","#ff7f00","#cab2d6","#6a3d9a","#ffff99","#b15928"],Pastel2:["#b3e2cd","#fdcdac","#cbd5e8","#f4cae4","#e6f5c9","#fff2ae","#f1e2cc","#cccccc"],Pastel1:["#fbb4ae","#b3cde3","#ccebc5","#decbe4","#fed9a6","#ffffcc","#e5d8bd","#fddaec","#f2f2f2"]},Ne=0,_e=Object.keys(Me);Ne<_e.length;Ne+=1){var xe=_e[Ne];Me[xe.toLowerCase()]=Me[xe]}return Object.assign(m,{average:function(r,e,t){void 0===e&&(e="lrgb"),void 0===t&&(t=null);var n=r.length;t||(t=Array.from(new Array(n)).map((function(){return 1})));var a=n/t.reduce((function(r,e){return r+e}));if(t.forEach((function(r,e){t[e]*=a})),r=r.map((function(r){return new v(r)})),"lrgb"===e)return Hr(r,t);for(var f=r.shift(),o=f.get(e),u=[],c=0,i=0,l=0;l<o.length;l++)if(o[l]=(o[l]||0)*t[0],u.push(isNaN(o[l])?0:t[0]),"h"===e.charAt(l)&&!isNaN(o[l])){var h=o[l]/180*Vr;c+=Xr(h)*t[0],i+=Dr(h)*t[0]}var d=f.alpha()*t[0];r.forEach((function(r,n){var a=r.get(e);d+=r.alpha()*t[n+1];for(var f=0;f<o.length;f++)if(!isNaN(a[f]))if(u[f]+=t[n+1],"h"===e.charAt(f)){var l=a[f]/180*Vr;c+=Xr(l)*t[n+1],i+=Dr(l)*t[n+1]}else o[f]+=a[f]*t[n+1]}));for(var s=0;s<o.length;s++)if("h"===e.charAt(s)){for(var b=Tr(i/u[s],c/u[s])/Vr*180;b<0;)b+=360;for(;b>=360;)b-=360;o[s]=b}else o[s]=o[s]/u[s];return d/=n,new v(o,e).alpha(d>.99999?1:d,!0)},bezier:function(r){var e=function(r){var e,t,n,a,f,o,u;if(2===(r=r.map((function(r){return new v(r)}))).length)e=r.map((function(r){return r.lab()})),f=e[0],o=e[1],a=function(r){var e=[0,1,2].map((function(e){return f[e]+r*(o[e]-f[e])}));return new v(e,"lab")};else if(3===r.length)t=r.map((function(r){return r.lab()})),f=t[0],o=t[1],u=t[2],a=function(r){var e=[0,1,2].map((function(e){return(1-r)*(1-r)*f[e]+2*(1-r)*r*o[e]+r*r*u[e]}));return new v(e,"lab")};else if(4===r.length){var c;n=r.map((function(r){return r.lab()})),f=n[0],o=n[1],u=n[2],c=n[3],a=function(r){var e=[0,1,2].map((function(e){return(1-r)*(1-r)*(1-r)*f[e]+3*(1-r)*(1-r)*r*o[e]+3*(1-r)*r*r*u[e]+r*r*r*c[e]}));return new v(e,"lab")}}else{if(!(r.length>=5))throw new RangeError("No point in running bezier with only one color.");var i,l,h;i=r.map((function(r){return r.lab()})),h=r.length-1,l=function(r){for(var e=[1,1],t=1;t<r;t++){for(var n=[1],a=1;a<=e.length;a++)n[a]=(e[a]||0)+e[a-1];e=n}return e}(h),a=function(r){var e=1-r,t=[0,1,2].map((function(t){return i.reduce((function(n,a,f){return n+l[f]*Math.pow(e,h-f)*Math.pow(r,f)*a[t]}),0)}));return new v(t,"lab")}}return a}(r);return e.scale=function(){return Kr(e)},e},blend:Qr,cubehelix:function(r,t,n,a,f){void 0===r&&(r=300),void 0===t&&(t=-1.5),void 0===n&&(n=1),void 0===a&&(a=1),void 0===f&&(f=[0,1]);var u,c=0;"array"===o(f)?u=f[1]-f[0]:(u=0,f=[f,f]);var i=function(o){var i=d*((r+120)/360+t*o),l=re(f[0]+u*o,a),h=(0!==c?n[0]+o*c:n)*l*(1-l)/2,s=te(i),b=ee(i);return m(e([255*(l+h*(-.14861*s+1.78277*b)),255*(l+h*(-.29227*s-.90649*b)),255*(l+h*(1.97294*s)),1]))};return i.start=function(e){return null==e?r:(r=e,i)},i.rotations=function(r){return null==r?t:(t=r,i)},i.gamma=function(r){return null==r?a:(a=r,i)},i.hue=function(r){return null==r?n:("array"===o(n=r)?0===(c=n[1]-n[0])&&(n=n[1]):c=0,i)},i.lightness=function(r){return null==r?f:("array"===o(r)?(f=r,u=r[1]-r[0]):(f=[r,r],u=0),i)},i.scale=function(){return m.scale(i)},i.hue(n),i},mix:Rr,interpolate:Rr,random:function(){for(var r="#",e=0;e<6;e++)r+="0123456789abcdef".charAt(ne(16*ae()));return new v(r,"hex")},scale:Kr,analyze:ie,contrast:function(r,e){r=new v(r),e=new v(e);var t=r.luminance(),n=e.luminance();return t>n?(t+.05)/(n+.05):(n+.05)/(t+.05)},deltaE:function(r,e,t,n,a){void 0===t&&(t=1),void 0===n&&(n=1),void 0===a&&(a=1);var f=function(r){return 360*r/(2*we)},o=function(r){return 2*we*r/360};r=new v(r),e=new v(e);var u=Array.from(r.lab()),c=u[0],i=u[1],l=u[2],h=Array.from(e.lab()),d=h[0],s=h[1],b=h[2],g=(c+d)/2,p=(he(de(i,2)+de(l,2))+he(de(s,2)+de(b,2)))/2,m=.5*(1-he(de(p,7)/(de(p,7)+de(25,7)))),y=i*(1+m),w=s*(1+m),k=he(de(y,2)+de(l,2)),M=he(de(w,2)+de(b,2)),N=(k+M)/2,_=f(ge(l,y)),x=f(ge(b,w)),A=_>=0?_:_+360,F=x>=0?x:x+360,E=pe(A-F)>180?(A+F+360)/2:(A+F)/2,j=1-.17*ve(o(E-30))+.24*ve(o(2*E))+.32*ve(o(3*E+6))-.2*ve(o(4*E-63)),q=F-A;q=pe(q)<=180?q:F<=A?q+360:q-360,q=2*he(k*M)*me(o(q)/2);var L=d-c,O=M-k,P=1+.015*de(g-50,2)/he(20+de(g-50,2)),G=1+.045*N,B=1+.015*N*j,C=30*ye(-de((E-275)/25,2)),R=-(2*he(de(N,7)/(de(N,7)+de(25,7))))*me(2*o(C)),S=he(de(L/(t*P),2)+de(O/(n*G),2)+de(q/(a*B),2)+R*(O/(n*G))*(q/(a*B)));return be(0,se(100,S))},distance:function(r,e,t){void 0===t&&(t="lab"),r=new v(r),e=new v(e);var n=r.get(t),a=e.get(t),f=0;for(var o in n){var u=(n[o]||0)-(a[o]||0);f+=u*u}return Math.sqrt(f)},limits:le,valid:function(){for(var r=[],e=arguments.length;e--;)r[e]=arguments[e];try{return new(Function.prototype.bind.apply(v,[null].concat(r))),!0}catch(r){return!1}},scales:ke,input:p,colors:yr,brewer:Me}),m}));
