import type { ToolResult } from '../types';
export declare class SystemTool {
    getInfo(args: Record<string, unknown>): Promise<ToolResult>;
    private getBasicInfo;
    private getDetailedInfo;
    private getProcessInfo;
    private getNetworkInfo;
    private getSystemProcesses;
    private parseProcessOutput;
    private getNetworkStats;
    private formatInfo;
    private formatBytes;
    private formatUptime;
    private parseMemoryString;
}
//# sourceMappingURL=system.d.ts.map